import * as ts from 'typescript';
import type { TSESTreeOptions } from '../parser-options';
import type { MutableParseSettings } from './index';
export declare function createParseSettings(code: ts.SourceFile | string, options?: Partial<TSESTreeOptions>): MutableParseSettings;
export declare function clearTSConfigMatchCache(): void;
export declare function clearTSServerProjectService(): void;
//# sourceMappingURL=createParseSettings.d.ts.map