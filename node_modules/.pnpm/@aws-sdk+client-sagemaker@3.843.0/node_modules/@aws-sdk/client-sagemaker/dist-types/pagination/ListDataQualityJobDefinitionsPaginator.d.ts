import { Paginator } from "@smithy/types";
import { ListDataQualityJobDefinitionsCommandInput, ListDataQualityJobDefinitionsCommandOutput } from "../commands/ListDataQualityJobDefinitionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListDataQualityJobDefinitions: (config: SageMakerPaginationConfiguration, input: ListDataQualityJobDefinitionsCommandInput, ...rest: any[]) => Paginator<ListDataQualityJobDefinitionsCommandOutput>;
