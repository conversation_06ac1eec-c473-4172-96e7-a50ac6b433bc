import { Paginator } from "@smithy/types";
import { ListPipelineExecutionsCommandInput, ListPipelineExecutionsCommandOutput } from "../commands/ListPipelineExecutionsCommand";
import { SageMakerPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateListPipelineExecutions: (config: SageMakerPaginationConfiguration, input: ListPipelineExecutionsCommandInput, ...rest: any[]) => Paginator<ListPipelineExecutionsCommandOutput>;
