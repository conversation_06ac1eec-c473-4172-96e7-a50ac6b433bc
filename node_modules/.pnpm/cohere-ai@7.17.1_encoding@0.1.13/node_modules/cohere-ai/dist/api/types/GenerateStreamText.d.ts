/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
export interface GenerateStreamText extends Cohere.GenerateStreamEvent {
    /** A segment of text of the generation. */
    text: string;
    /** Refers to the nth generation. Only present when `num_generations` is greater than zero, and only when text responses are being streamed. */
    index?: number;
    isFinished: boolean;
}
