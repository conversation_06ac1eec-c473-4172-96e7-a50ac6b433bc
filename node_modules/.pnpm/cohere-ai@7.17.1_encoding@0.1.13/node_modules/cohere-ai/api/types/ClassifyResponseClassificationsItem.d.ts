/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
export interface ClassifyResponseClassificationsItem {
    id: string;
    /** The input text that was classified */
    input?: string;
    /** The predicted label for the associated query (only filled for single-label models) */
    prediction?: string;
    /** An array containing the predicted labels for the associated query (only filled for single-label classification) */
    predictions: string[];
    /** The confidence score for the top predicted class (only filled for single-label classification) */
    confidence?: number;
    /** An array containing the confidence scores of all the predictions in the same order */
    confidences: number[];
    /** A map containing each label and its confidence score according to the classifier. All the confidence scores add up to 1 for single-label classification. For multi-label classification the label confidences are independent of each other, so they don't have to sum up to 1. */
    labels: Record<string, Cohere.ClassifyResponseClassificationsItemLabelsValue>;
    /** The type of classification performed */
    classificationType: Cohere.ClassifyResponseClassificationsItemClassificationType;
}
