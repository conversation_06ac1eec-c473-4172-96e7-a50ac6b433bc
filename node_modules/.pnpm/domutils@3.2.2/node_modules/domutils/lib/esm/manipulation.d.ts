import type { ChildN<PERSON>, ParentNode } from "domhandler";
/**
 * Remove an element from the dom
 *
 * @category Manipulation
 * @param elem The element to be removed
 */
export declare function removeElement(elem: ChildNode): void;
/**
 * Replace an element in the dom
 *
 * @category Manipulation
 * @param elem The element to be replaced
 * @param replacement The element to be added
 */
export declare function replaceElement(elem: ChildNode, replacement: ChildNode): void;
/**
 * Append a child to an element.
 *
 * @category Manipulation
 * @param parent The element to append to.
 * @param child The element to be added as a child.
 */
export declare function appendChild(parent: ParentNode, child: ChildNode): void;
/**
 * Append an element after another.
 *
 * @category Manipulation
 * @param elem The element to append after.
 * @param next The element be added.
 */
export declare function append(elem: ChildNode, next: ChildNode): void;
/**
 * Prepend a child to an element.
 *
 * @category Manipulation
 * @param parent The element to prepend before.
 * @param child The element to be added as a child.
 */
export declare function prependChild(parent: <PERSON><PERSON><PERSON><PERSON>, child: ChildNode): void;
/**
 * Prepend an element before another.
 *
 * @category Manipulation
 * @param elem The element to prepend before.
 * @param prev The element be added.
 */
export declare function prepend(elem: ChildNode, prev: ChildNode): void;
//# sourceMappingURL=manipulation.d.ts.map