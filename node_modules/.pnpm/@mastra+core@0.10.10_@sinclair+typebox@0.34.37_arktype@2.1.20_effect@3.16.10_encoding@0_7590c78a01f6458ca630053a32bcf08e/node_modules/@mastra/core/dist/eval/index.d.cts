import { M as Metric, T as TestInfo, E as EvaluationResult } from '../types-Bo1uigWx.cjs';
export { a as MetricResult } from '../types-Bo1uigWx.cjs';
import { A as Agent } from '../base-Bu9IeyHt.cjs';
import 'ai';
import '../base-B_y9sMg0.cjs';
import '@opentelemetry/api';
import '../logger-B8XXh6ya.cjs';
import '../error/index.cjs';
import 'stream';
import '@opentelemetry/sdk-trace-base';
import 'zod';
import 'json-schema';
import 'sift';
import '../runtime-context/index.cjs';
import 'xstate';
import 'node:events';
import '../vector/index.cjs';
import '../vector/filter/index.cjs';
import '../tts/index.cjs';
import 'node:http';
import 'hono';
import 'stream/web';
import 'events';
import 'node:stream/web';
import '../workflows/constants.cjs';
import 'ai/test';
import '../deployer/index.cjs';
import '../bundler/index.cjs';
import 'hono/cors';
import 'hono-openapi';

declare function evaluate<T extends Agent>({ agentName, input, metric, output, runId, globalRunId, testInfo, instructions, }: {
    agentName: string;
    input: Parameters<T['generate']>[0];
    metric: Metric;
    output: string;
    globalRunId: string;
    runId?: string;
    testInfo?: TestInfo;
    instructions: string;
}): Promise<EvaluationResult>;

export { EvaluationResult, Metric, TestInfo, evaluate };
