'use strict';

var chunkGWFS5DAR_cjs = require('./chunk-GWFS5DAR.cjs');
var chunkMP2QBLUJ_cjs = require('./chunk-MP2QBLUJ.cjs');
var chunk26QYW6NW_cjs = require('./chunk-26QYW6NW.cjs');
var chunkUPIUWMJV_cjs = require('./chunk-UPIUWMJV.cjs');
var chunkRL7RV3SN_cjs = require('./chunk-RL7RV3SN.cjs');
var chunkF2WMR75C_cjs = require('./chunk-F2WMR75C.cjs');
var chunkJ52TXHZV_cjs = require('./chunk-J52TXHZV.cjs');
var chunkB7SQOKEC_cjs = require('./chunk-B7SQOKEC.cjs');
var chunkMUNFCOMB_cjs = require('./chunk-MUNFCOMB.cjs');
var chunk7H2GET5Z_cjs = require('./chunk-7H2GET5Z.cjs');
var chunkLYF7XPUM_cjs = require('./chunk-LYF7XPUM.cjs');
var chunkST5RMVLG_cjs = require('./chunk-ST5RMVLG.cjs');
var chunkQQ5K5TZE_cjs = require('./chunk-QQ5K5TZE.cjs');
var chunk7F6BQXE2_cjs = require('./chunk-7F6BQXE2.cjs');
var chunk4Z3OU5RY_cjs = require('./chunk-4Z3OU5RY.cjs');
var chunkZZLBNB3U_cjs = require('./chunk-ZZLBNB3U.cjs');
var chunkP3Q73CAW_cjs = require('./chunk-P3Q73CAW.cjs');

// src/agent/index.warning.ts
var Agent2 = class extends chunkLYF7XPUM_cjs.Agent {
  constructor(config) {
    super(config);
    this.logger.warn('Please import "Agent from "@mastra/core/agent" instead of "@mastra/core"');
  }
};

// src/base.warning.ts
var MastraBase2 = class extends chunkP3Q73CAW_cjs.MastraBase {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraBase" from "@mastra/core/base" instead of "@mastra/core"');
  }
};

// src/deployer/index.warning.ts
var MastraDeployer2 = class extends chunkZZLBNB3U_cjs.MastraDeployer {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraDeployer" from "@mastra/core/deployer" instead of "@mastra/core"');
  }
};

// src/storage/base.warning.ts
var MastraStorage2 = class extends chunkF2WMR75C_cjs.MastraStorage {
  constructor({ name }) {
    super({
      name
    });
    this.logger.warn('Please import "MastraStorage" from "@mastra/core/storage" instead of "@mastra/core"');
  }
};

// src/integration/integration.warning.ts
var Integration2 = class extends chunkB7SQOKEC_cjs.Integration {
  constructor() {
    super();
    console.warn('Please import "Integration" from "@mastra/core/integration" instead of "@mastra/core"');
  }
};

// src/integration/openapi-toolset.warning.ts
var OpenAPIToolset2 = class extends chunkB7SQOKEC_cjs.OpenAPIToolset {
  constructor() {
    super();
    console.warn('Please import "OpenAPIToolset" from "@mastra/core/integration" instead of "@mastra/core"');
  }
};

// src/memory/index.warning.ts
var MastraMemory2 = class extends chunkUPIUWMJV_cjs.MastraMemory {
  constructor(_arg) {
    super({ name: `Deprecated memory` });
    this.logger.warn('Please import "MastraMemory" from "@mastra/core/memory" instead of "@mastra/core"');
  }
};

// src/tools/index.warning.ts
var Tool2 = class extends chunk4Z3OU5RY_cjs.Tool {
  constructor(opts) {
    super(opts);
    console.warn('Please import "Tool" from "@mastra/core/tools" instead of "@mastra/core"');
  }
};

// src/tts/index.warning.ts
var MastraTTS2 = class extends chunkGWFS5DAR_cjs.MastraTTS {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "MastraTTS" from "@mastra/core/tts" instead of "@mastra/core"');
  }
};

// src/vector/index.warning.ts
var MastraVector2 = class extends chunkMP2QBLUJ_cjs.MastraVector {
  constructor() {
    super();
    this.logger.warn('Please import "MastraVector" from "@mastra/core/vector" instead of "@mastra/core"');
  }
};

// src/workflows/workflow.warning.ts
var Workflow2 = class extends chunk26QYW6NW_cjs.Workflow {
  constructor(args) {
    super(args);
    this.logger.warn('Please import "Workflow" from "@mastra/core/workflows" instead of "@mastra/core"');
  }
};

Object.defineProperty(exports, "DefaultExecutionEngine", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.DefaultExecutionEngine; }
});
Object.defineProperty(exports, "ExecutionEngine", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.ExecutionEngine; }
});
Object.defineProperty(exports, "Run", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.Run; }
});
Object.defineProperty(exports, "cloneStep", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.cloneStep; }
});
Object.defineProperty(exports, "cloneWorkflow", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.cloneWorkflow; }
});
Object.defineProperty(exports, "createStep", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.createStep; }
});
Object.defineProperty(exports, "createWorkflow", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.createWorkflow; }
});
Object.defineProperty(exports, "mapVariable", {
  enumerable: true,
  get: function () { return chunk26QYW6NW_cjs.mapVariable; }
});
Object.defineProperty(exports, "MemoryProcessor", {
  enumerable: true,
  get: function () { return chunkUPIUWMJV_cjs.MemoryProcessor; }
});
Object.defineProperty(exports, "memoryDefaultOptions", {
  enumerable: true,
  get: function () { return chunkUPIUWMJV_cjs.memoryDefaultOptions; }
});
Object.defineProperty(exports, "CohereRelevanceScorer", {
  enumerable: true,
  get: function () { return chunkRL7RV3SN_cjs.CohereRelevanceScorer; }
});
Object.defineProperty(exports, "MastraAgentRelevanceScorer", {
  enumerable: true,
  get: function () { return chunkRL7RV3SN_cjs.MastraAgentRelevanceScorer; }
});
Object.defineProperty(exports, "createSimilarityPrompt", {
  enumerable: true,
  get: function () { return chunkRL7RV3SN_cjs.createSimilarityPrompt; }
});
Object.defineProperty(exports, "Metric", {
  enumerable: true,
  get: function () { return chunkJ52TXHZV_cjs.Metric; }
});
Object.defineProperty(exports, "evaluate", {
  enumerable: true,
  get: function () { return chunkJ52TXHZV_cjs.evaluate; }
});
Object.defineProperty(exports, "createMockModel", {
  enumerable: true,
  get: function () { return chunkMUNFCOMB_cjs.createMockModel; }
});
Object.defineProperty(exports, "Mastra", {
  enumerable: true,
  get: function () { return chunk7H2GET5Z_cjs.Mastra; }
});
Object.defineProperty(exports, "AvailableHooks", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.AvailableHooks; }
});
Object.defineProperty(exports, "executeHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.executeHook; }
});
Object.defineProperty(exports, "registerHook", {
  enumerable: true,
  get: function () { return chunkST5RMVLG_cjs.registerHook; }
});
Object.defineProperty(exports, "InstrumentClass", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.InstrumentClass; }
});
Object.defineProperty(exports, "OTLPStorageExporter", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.OTLPTraceExporter; }
});
Object.defineProperty(exports, "Telemetry", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.Telemetry; }
});
Object.defineProperty(exports, "getBaggageValues", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.getBaggageValues; }
});
Object.defineProperty(exports, "hasActiveTelemetry", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.hasActiveTelemetry; }
});
Object.defineProperty(exports, "withSpan", {
  enumerable: true,
  get: function () { return chunkQQ5K5TZE_cjs.withSpan; }
});
Object.defineProperty(exports, "checkEvalStorageFields", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.checkEvalStorageFields; }
});
Object.defineProperty(exports, "createMastraProxy", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.createMastraProxy; }
});
Object.defineProperty(exports, "deepMerge", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.deepMerge; }
});
Object.defineProperty(exports, "delay", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.delay; }
});
Object.defineProperty(exports, "ensureToolProperties", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.ensureToolProperties; }
});
Object.defineProperty(exports, "isCoreMessage", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.isCoreMessage; }
});
Object.defineProperty(exports, "isUiMessage", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.isUiMessage; }
});
Object.defineProperty(exports, "isZodType", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.isZodType; }
});
Object.defineProperty(exports, "makeCoreTool", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.makeCoreTool; }
});
Object.defineProperty(exports, "maskStreamTags", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.maskStreamTags; }
});
Object.defineProperty(exports, "parseFieldKey", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.parseFieldKey; }
});
Object.defineProperty(exports, "parseSqlIdentifier", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.parseSqlIdentifier; }
});
Object.defineProperty(exports, "resolveSerializedZodOutput", {
  enumerable: true,
  get: function () { return chunk7F6BQXE2_cjs.resolveSerializedZodOutput; }
});
Object.defineProperty(exports, "createTool", {
  enumerable: true,
  get: function () { return chunk4Z3OU5RY_cjs.createTool; }
});
exports.Agent = Agent2;
exports.Integration = Integration2;
exports.MastraBase = MastraBase2;
exports.MastraDeployer = MastraDeployer2;
exports.MastraMemory = MastraMemory2;
exports.MastraStorage = MastraStorage2;
exports.MastraTTS = MastraTTS2;
exports.MastraVector = MastraVector2;
exports.OpenAPIToolset = OpenAPIToolset2;
exports.Tool = Tool2;
exports.Workflow = Workflow2;
