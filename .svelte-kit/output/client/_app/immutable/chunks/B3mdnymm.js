import"./CWj6FrbW.js";import{p as te,f as v,t as c,a as n,g as ae,r as e,b as j,e as C,c as t,s as l,j as a,o as ie}from"./wnqW1tdD.js";import{s as d}from"./CDPCzm7q.js";import{i as se}from"./BjRbZGyQ.js";import{e as D,i as E}from"./CsnEE4l9.js";import{c as S}from"./ojdN50pv.js";import{s as oe}from"./rh_XW2Tv.js";import{s as b}from"./Bz0_kaay.js";import{p as y}from"./Cmdkv-7M.js";import{a as ne,C as le}from"./Dk7oig4_.js";import"./Cvx8ZW61.js";import{b as k}from"./DpzY6icx.js";const je="free",de=[{id:"free",name:"Free",description:"A free plan to get you started!",price:"$0",priceIntervalName:"per month",stripe_price_id:null,features:["MIT Licence","Fast Performance","Stripe Integration"]},{id:"pro",name:"Pro",description:"A plan to test the purchase experience. Try buying this with the test credit card ****************.",price:"$5",priceIntervalName:"per month",stripe_price_id:"price_1PsKXXApN1csWalssDVkvNDD",stripe_product_id:"prod_Qjo9lzIwTezpcE",features:["Everything in Free","Support us with fake money","Test the purchase experience"]},{id:"enterprise",name:"Enterprise",description:"A plan to test the upgrade experience. Try buying this with the test credit card ****************.",price:"$15",priceIntervalName:"per month",stripe_price_id:"price_1PsKZBApN1csWalsniyNDRjs",stripe_product_id:"prod_QjoBZ36PfGidUS",features:["Everything in Pro","Try the 'upgrade plan' UX","Still actually free!"]}];var pe=v("<li> </li>"),ce=v("<div>Current Plan</div>"),ve=v("<a> </a>"),me=v('<div class="flex flex-col h-full"><div class="text-xl font-bold"> </div> <p class="mt-2 text-sm text-muted-foreground leading-relaxed"> </p> <div class="mt-auto pt-4 text-sm"><span class="font-bold">Plan Includes:</span> <ul class="list-disc list-inside mt-2 space-y-1"></ul></div> <div class="pt-8"><span class="text-4xl font-bold"> </span> <span class="text-muted-forground"> </span> <div class="mt-6 pt-4 flex-1 flex flex-row items-center"><!></div></div></div>'),ue=v("<div></div>");function Ce(F,p){te(p,!0);let X=y(p,"highlightedPlanId",3,""),z=y(p,"currentPlanId",3,""),B=y(p,"center",3,!0);var m=ue();D(m,21,()=>de,E,(K,i)=>{var w=j(),Q=C(w);const U=ie(()=>a(i).id===X()?"border-primary":"border-gray-200");S(Q,()=>le,(V,W)=>{W(V,{get class(){return`${a(U)??""} shadow-xl flex-1 flex-grow min-w-[260px] max-w-[310px] p-6`},children:(Z,fe)=>{var I=j(),G=C(I);S(G,()=>ne,(L,M)=>{M(L,{children:(R,_e)=>{var u=me(),f=t(u),q=t(f,!0);e(f);var _=l(f,2),H=t(_,!0);e(_);var x=l(_,2),N=l(t(x),2);D(N,21,()=>a(i).features,E,(s,r)=>{var o=pe(),P=t(o,!0);e(o),c(()=>d(P,a(r))),n(s,o)}),e(N),e(x);var T=l(x,2),h=t(T),J=t(h,!0);e(h);var g=l(h,2),O=t(g,!0);e(g);var A=l(g,2),Y=t(A);{var ee=s=>{var r=ce();c(o=>b(r,1,`${o??""}  no-animation w-[80%] mx-auto cursor-default`),[()=>k({variant:"outline"})]),n(s,r)},re=s=>{var r=ve(),o=t(r,!0);e(r),c(P=>{var $;oe(r,"href","/account/subscribe/"+((($=a(i))==null?void 0:$.stripe_price_id)??"free_plan")),b(r,1,`${P??""} w-[80%] mx-auto`),d(o,p.callToAction)},[()=>k({variant:"default"})]),n(s,r)};se(Y,s=>{a(i).id===z()?s(ee):s(re,!1)})}e(A),e(T),e(u),c(()=>{d(q,a(i).name),d(H,a(i).description),d(J,a(i).price),d(O,a(i).priceIntervalName)}),n(R,u)},$$slots:{default:!0}})}),n(Z,I)},$$slots:{default:!0}})}),n(K,w)}),e(m),c(()=>b(m,1,`flex flex-col lg:flex-row gap-10 ${B()?"place-content-center":""} flex-wrap`)),n(F,m),ae()}export{Ce as P,je as d,de as p};
