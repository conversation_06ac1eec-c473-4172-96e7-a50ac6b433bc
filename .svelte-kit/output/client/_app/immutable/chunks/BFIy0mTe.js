import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{p as F,b as v,e as b,a as m,g as G,aM as g,i as H,j as K,d as L}from"./wnqW1tdD.js";import{s as q}from"./BDqVm3Gq.js";import{i as I}from"./BxG_UISn.js";import{b as n}from"./B2uh23P-.js";import{l as x,p as f,s as Q}from"./Cmdkv-7M.js";import"./D477uqMC.js";import{e as s}from"./CDPCzm7q.js";import{i as R}from"./BjRbZGyQ.js";import{e as O}from"./CM6X1Z2I.js";import{a as S}from"./Cet13GU7.js";import{a as V}from"./rh_XW2Tv.js";import{b as A}from"./D5U2DSnR.js";import{b as C}from"./DpzY6icx.js";import{c as D}from"./BMdVdstb.js";function N(u,a){const l=[];return a.builders.forEach(o=>{const c=o.action(u);c&&l.push(c)}),{destroy:()=>{l.forEach(o=>{o.destroy&&o.destroy()})}}}function T(u){const a={};return u.forEach(l=>{Object.keys(l).forEach(o=>{o!=="action"&&(a[o]=l[o])})}),a}function U(u,a){const l=x(a,["children","$$slots","$$events","$$legacy"]),o=x(l,["href","type","builders","el"]);F(a,!1);let c=f(a,"href",24,()=>{}),y=f(a,"type",24,()=>{}),e=f(a,"builders",24,()=>[]),d=f(a,"el",28,()=>{});const w={"data-button-root":""};I();var r=v(),M=b(r);{var z=h=>{var _=v(),B=b(_);O(B,()=>c()?"a":"button",!1,(i,P)=>{A(i,t=>d(t),()=>d()),S(i,(t,J)=>N==null?void 0:N(t,J),()=>({builders:e()})),V(i,t=>({type:c()?void 0:y(),href:c(),tabindex:"0",...t,...o,...w}),[()=>T(e())]),s("click",i,function(t){n.call(this,a,t)}),s("change",i,function(t){n.call(this,a,t)}),s("keydown",i,function(t){n.call(this,a,t)}),s("keyup",i,function(t){n.call(this,a,t)}),s("mouseenter",i,function(t){n.call(this,a,t)}),s("mouseleave",i,function(t){n.call(this,a,t)}),s("mousedown",i,function(t){n.call(this,a,t)}),s("pointerdown",i,function(t){n.call(this,a,t)}),s("mouseup",i,function(t){n.call(this,a,t)}),s("pointerup",i,function(t){n.call(this,a,t)});var k=v(),j=b(k);q(j,a,"default",{},null),m(P,k)}),m(h,_)},E=h=>{var _=v(),B=b(_);O(B,()=>c()?"a":"button",!1,(i,P)=>{A(i,t=>d(t),()=>d()),V(i,()=>({type:c()?void 0:y(),href:c(),tabindex:"0",...o,...w})),s("click",i,function(t){n.call(this,a,t)}),s("change",i,function(t){n.call(this,a,t)}),s("keydown",i,function(t){n.call(this,a,t)}),s("keyup",i,function(t){n.call(this,a,t)}),s("mouseenter",i,function(t){n.call(this,a,t)}),s("mouseleave",i,function(t){n.call(this,a,t)}),s("mousedown",i,function(t){n.call(this,a,t)}),s("pointerdown",i,function(t){n.call(this,a,t)}),s("mouseup",i,function(t){n.call(this,a,t)}),s("pointerup",i,function(t){n.call(this,a,t)});var k=v(),j=b(k);q(j,a,"default",{},null),m(P,k)}),m(h,_)};R(M,h=>{g(e()),H(()=>e()&&e().length)?h(z):h(E,!1)})}m(u,r),G()}function ft(u,a){const l=x(a,["children","$$slots","$$events","$$legacy"]),o=x(l,["class","variant","size","builders"]);F(a,!1);let c=f(a,"class",8,void 0),y=f(a,"variant",8,"default"),e=f(a,"size",8,"default"),d=f(a,"builders",24,()=>[]);I();const w=L(()=>(g(D),g(C),g(y()),g(e()),g(c()),H(()=>D(C({variant:y(),size:e(),className:c()})))));U(u,Q({get builders(){return d()},get class(){return K(w)},type:"button"},()=>o,{$$events:{click(r){n.call(this,a,r)},keydown(r){n.call(this,a,r)}},children:(r,M)=>{var z=v(),E=b(z);q(E,a,"default",{},null),m(r,z)},$$slots:{default:!0}})),G()}export{ft as B};
