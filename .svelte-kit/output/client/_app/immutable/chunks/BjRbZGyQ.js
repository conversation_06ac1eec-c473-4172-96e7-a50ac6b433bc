import{K as p,w as u,x as S,M as g,Q as h,R as D,S as F,T as k,F as H,U as I,V as A,N as _,W as b,X as L,A as O}from"./wnqW1tdD.js";function Y(v,N,[t,s]=[0,0]){u&&t===0&&S();var a=v,f=null,e=null,i=L,R=t>0?g:0,n=!1;const m=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=h(a);r===D?s=0:r===F?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const E=s>t;!!i===E&&(a=k(),H(a),I(!1),T=!0,s=-1)}i?(f?A(f):l&&(f=_(()=>l(a))),e&&b(e,()=>{e=null})):(e?A(e):l&&(e=_(()=>l(a,[t+1,s]))),f&&b(f,()=>{f=null})),T&&I(!0)};p(()=>{n=!1,N(m),n||o(null,null)},R),u&&(a=O)}export{Y as i};
