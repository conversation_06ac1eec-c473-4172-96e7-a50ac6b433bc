import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{p as n,f,i as d,aM as r,c as u,r as _,a as g,g as h}from"./wnqW1tdD.js";import{s as v}from"./BDqVm3Gq.js";import{a as $}from"./rh_XW2Tv.js";import{i as x}from"./BxG_UISn.js";import{l as e,p as y}from"./Cmdkv-7M.js";import{c as o}from"./BMdVdstb.js";var C=f("<p><!></p>");function w(p,s){const i=e(s,["children","$$slots","$$events","$$legacy"]),m=e(i,["class"]);n(s,!1);let t=y(s,"class",8,void 0);x();var a=C();$(a,l=>({class:l,...m}),[()=>(r(o),r(t()),d(()=>o("text-muted-foreground text-sm",t())))]);var c=u(a);v(c,s,"default",{},null),_(a),g(p,a),h()}export{w as C};
