import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as c,e as l,a as i}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as $,s as p}from"./Cmdkv-7M.js";import{I as u}from"./CX_t0Ed_.js";function M(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M12 8V4H8"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2"}],["path",{d:"M2 14h2"}],["path",{d:"M20 14h2"}],["path",{d:"M15 13v2"}],["path",{d:"M9 13v2"}]];u(o,p({name:"bot"},()=>r,{get iconNode(){return s},children:(n,h)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(n,e)},$$slots:{default:!0}}))}function N(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),s=[["circle",{cx:"12",cy:"12",r:"10"}],["polyline",{points:"12 6 12 12 16 14"}]];u(o,p({name:"clock"},()=>r,{get iconNode(){return s},children:(n,h)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(n,e)},$$slots:{default:!0}}))}function x(o,t){const r=$(t,["children","$$slots","$$events","$$legacy"]),s=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}],["circle",{cx:"12",cy:"7",r:"4"}]];u(o,p({name:"user"},()=>r,{get iconNode(){return s},children:(n,h)=>{var e=c(),a=l(e);d(a,t,"default",{},null),i(n,e)},$$slots:{default:!0}}))}export{M as B,N as C,x as U};
