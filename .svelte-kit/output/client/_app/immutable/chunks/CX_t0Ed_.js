import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{p as C,aR as B,a as v,g as I,c as A,s as M,i as k,aM as s,r as O,b as P,e as R,j as u,o as q,aS as D}from"./wnqW1tdD.js";import{e as E,i as F}from"./CsnEE4l9.js";import{s as G}from"./BDqVm3Gq.js";import{e as H}from"./CM6X1Z2I.js";import{a as _}from"./rh_XW2Tv.js";import{i as J}from"./BxG_UISn.js";import{l as p,p as o}from"./Cmdkv-7M.js";/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};var L=B("<svg><!><!></svg>");function oe(b,e){const l=p(e,["children","$$slots","$$events","$$legacy"]),w=p(l,["name","color","size","strokeWidth","absoluteStrokeWidth","iconNode"]);C(e,!1);let d=o(e,"name",8,void 0),W=o(e,"color",8,"currentColor"),a=o(e,"size",8,24),c=o(e,"strokeWidth",8,2),f=o(e,"absoluteStrokeWidth",8,!1),x=o(e,"iconNode",24,()=>[]);const y=(...r)=>r.filter((t,n,m)=>!!t&&m.indexOf(t)===n).join(" ");J();var i=L();_(i,(r,t)=>({...K,...w,width:a(),height:a(),stroke:W(),"stroke-width":r,class:t}),[()=>(s(f()),s(c()),s(a()),k(()=>f()?Number(c())*24/Number(a()):c())),()=>(s(d()),s(l),k(()=>y("lucide-icon","lucide",d()?`lucide-${d()}`:"",l.class)))]);var h=A(i);E(h,1,x,F,(r,t)=>{var n=q(()=>D(u(t),2));let m=()=>u(n)[0],N=()=>u(n)[1];var g=P(),S=R(g);H(S,m,!0,(j,Q)=>{_(j,()=>({...N()}))}),v(r,g)});var z=M(h);G(z,e,"default",{},null),O(i),v(b,i),I()}export{oe as I};
