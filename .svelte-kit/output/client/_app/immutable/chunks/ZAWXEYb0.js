import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as i,e as p,a as m}from"./wnqW1tdD.js";import{s as l}from"./BDqVm3Gq.js";import{l as c,s as d}from"./Cmdkv-7M.js";import{I as $}from"./CX_t0Ed_.js";function C(t,o){const s=c(o,["children","$$slots","$$events","$$legacy"]),e=[["path",{d:"m9 18 6-6-6-6"}]];$(t,d({name:"chevron-right"},()=>s,{get iconNode(){return e},children:(a,f)=>{var r=i(),n=p(r);l(n,o,"default",{},null),m(a,r)},$$slots:{default:!0}}))}export{C};
