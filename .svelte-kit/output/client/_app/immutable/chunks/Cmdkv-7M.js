import{a1 as x,a2 as M,a3 as N,a4 as A,d as U,a5 as T,j as l,a6 as y,a7 as C,k as G,m as $,i as D,a8 as z,a9 as V,a0 as j,aa as q,ab as Z,ac as F,ad as H,ae as L,af as d,ag as J,ah as Q}from"./wnqW1tdD.js";import{c as W}from"./D5ITLM2v.js";const X={get(e,r){if(!e.exclude.includes(r))return l(e.version),r in e.special?e.special[r]():e.props[r]},set(e,r,s){return r in e.special||(e.special[r]=ee({get[r](){return e.props[r]}},r,T)),e.special[r](s),L(e.version),!0},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},deleteProperty(e,r){return e.exclude.includes(r)||(e.exclude.push(r),L(e.version)),!0},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function se(e,r){return new Proxy({props:e,exclude:r,special:{},version:z(0)},X)}const k={get(e,r){let s=e.props.length;for(;s--;){let n=e.props[s];if(d(n)&&(n=n()),typeof n=="object"&&n!==null&&r in n)return n[r]}},set(e,r,s){let n=e.props.length;for(;n--;){let u=e.props[n];d(u)&&(u=u());const a=x(u,r);if(a&&a.set)return a.set(s),!0}return!1},getOwnPropertyDescriptor(e,r){let s=e.props.length;for(;s--;){let n=e.props[s];if(d(n)&&(n=n()),typeof n=="object"&&n!==null&&r in n){const u=x(n,r);return u&&!u.configurable&&(u.configurable=!0),u}}},has(e,r){if(r===j||r===q)return!1;for(let s of e.props)if(d(s)&&(s=s()),s!=null&&r in s)return!0;return!1},ownKeys(e){const r=[];for(let s of e.props)if(d(s)&&(s=s()),!!s){for(const n in s)r.includes(n)||r.push(n);for(const n of Object.getOwnPropertySymbols(s))r.includes(n)||r.push(n)}return r}};function ie(...e){return new Proxy({props:e},k)}function g(e){var r;return((r=e.ctx)==null?void 0:r.d)??!1}function ee(e,r,s,n){var E;var u=(s&H)!==0,a=!Z||(s&F)!==0,v=(s&V)!==0,B=(s&J)!==0,O=!1,c;v?[c,O]=W(()=>e[r]):c=e[r];var Y=j in e||q in e,_=v&&(((E=x(e,r))==null?void 0:E.set)??(Y&&r in e&&(i=>e[r]=i)))||void 0,f=n,b=!0,m=!1,I=()=>(m=!0,b&&(b=!1,B?f=D(n):f=n),f);c===void 0&&n!==void 0&&(_&&a&&M(),c=I(),_&&_(c));var o;if(a)o=()=>{var i=e[r];return i===void 0?I():(b=!0,m=!1,i)};else{var R=(u?A:U)(()=>e[r]);R.f|=N,o=()=>{var i=l(R);return i!==void 0&&(f=void 0),i===void 0?f:i}}if(!(s&T)&&a)return o;if(_){var K=e.$$legacy;return function(i,p){return arguments.length>0?((!a||!p||K||O)&&_(p?o():i),i):o()}}var P=!1,S=!1,h=$(c),t=A(()=>{var i=o(),p=l(h);return P?(P=!1,S=!0,p):(S=!1,h.v=i)});return v&&l(t),u||(t.equals=y),function(i,p){if(Q!==null&&(P=S,o(),l(h)),arguments.length>0){const w=p?l(t):a&&v?C(i):i;if(!t.equals(w)){if(P=!0,G(h,w),m&&f!==void 0&&(f=w),g(t))return i;D(()=>l(t))}return i}return g(t)?t.v:l(t)}}export{se as l,ee as p,ie as s};
