import{c as i}from"./CfBaWyh2.js";function d(a,n){const t={};return n.forEach(e=>{t[e]={[`data-${a}-${e}`]:""}}),e=>t[e]}function o(a){return a?{"aria-disabled":"true","data-disabled":""}:{"aria-disabled":void 0,"data-disabled":void 0}}function u(){const a=i();return n=>{const{originalEvent:t}=n.detail,{cancelable:e}=n,r=t.type;a(r,{originalEvent:t,currentTarget:t.currentTarget},{cancelable:e})||n.preventDefault()}}export{u as a,d as c,o as d};
