import{w as A}from"./wnqW1tdD.js";function N(r){var u,i,f="";if(typeof r=="string"||typeof r=="number")f+=r;else if(typeof r=="object")if(Array.isArray(r)){var t=r.length;for(u=0;u<t;u++)r[u]&&(i=N(r[u]))&&(f&&(f+=" "),f+=i)}else for(i in r)r[i]&&(f&&(f+=" "),f+=i);return f}function S(){for(var r,u,i=0,f="",t=arguments.length;i<t;i++)(r=arguments[i])&&(u=N(r))&&(f&&(f+=" "),f+=u);return f}function q(r){return typeof r=="object"?S(r):r??""}const h=[...` 	
\r\f \v\uFEFF`];function C(r,u,i){var f=r==null?"":""+r;if(u&&(f=f?f+" "+u:u),i){for(var t in i)if(i[t])f=f?f+" "+t:t;else if(f.length)for(var g=t.length,o=0;(o=f.indexOf(t,o))>=0;){var l=o+g;(o===0||h.includes(f[o-1]))&&(l===f.length||h.includes(f[l]))?f=(o===0?"":f.substring(0,o))+f.substring(l+1):o=l}}return f===""?null:f}function j(r,u=!1){var i=u?" !important;":";",f="";for(var t in r){var g=r[t];g!=null&&g!==""&&(f+=" "+t+": "+g+i)}return f}function p(r){return r[0]!=="-"||r[1]!=="-"?r.toLowerCase():r}function z(r,u){if(u){var i="",f,t;if(Array.isArray(u)?(f=u[0],t=u[1]):f=u,r){r=String(r).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var g=!1,o=0,l=!1,b=[];f&&b.push(...Object.keys(f).map(p)),t&&b.push(...Object.keys(t).map(p));var c=0,s=-1;const v=r.length;for(var a=0;a<v;a++){var n=r[a];if(l?n==="/"&&r[a-1]==="*"&&(l=!1):g?g===n&&(g=!1):n==="/"&&r[a+1]==="*"?l=!0:n==='"'||n==="'"?g=n:n==="("?o++:n===")"&&o--,!l&&g===!1&&o===0){if(n===":"&&s===-1)s=a;else if(n===";"||a===v-1){if(s!==-1){var O=p(r.substring(c,s).trim());if(!b.includes(O)){n!==";"&&a++;var L=r.substring(c,a).trim();i+=" "+L+";"}}c=a+1,s=-1}}}}return f&&(i+=j(f)),t&&(i+=j(t,!0)),i=i.trim(),i===""?null:i}return r==null?null:String(r)}function B(r,u,i,f,t,g){var o=r.__className;if(A||o!==i||o===void 0){var l=C(i,f,g);(!A||l!==r.getAttribute("class"))&&(l==null?r.removeAttribute("class"):u?r.className=l:r.setAttribute("class",l)),r.__className=i}else if(g&&t!==g)for(var b in g){var c=!!g[b];(t==null||c!==!!t[b])&&r.classList.toggle(b,c)}return g}export{S as a,q as c,B as s,z as t};
