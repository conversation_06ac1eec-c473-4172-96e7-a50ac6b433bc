import{w as d}from"./D477uqMC.js";import{n as f}from"./DKwX7yNB.js";import{w as p}from"./BvpDAKCq.js";const y=(e,t)=>{const n=d(e),r=(o,s)=>{n.update(i=>{const c=o(i);let u=c;return t&&(u=t({curr:i,next:c})),s==null||s(u),u})};return{...n,update:r,set:o=>{r(()=>o)}}};function l(){return f(10)}function g(e){return e.reduce((t,n)=>(t[n]=l(),t),{})}function v(e){const t={};return Object.keys(e).forEach(n=>{const r=n,a=e[r];t[r]=d(p(a))}),t}function k(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=r)}return t}function x(e){return function(t,n){if(n===void 0)return;const r=e[t];r&&r.set(n)}}export{l as a,x as b,g,y as o,k as r,v as t};
