import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{o as zt}from"./CfBaWyh2.js";import{p as he,l as we,k as E,m as re,aM as _,h as Pe,f as ae,i as $,j as h,c as le,r as ie,a as f,g as ve,e as de,n as Z,v as q,t as Y,s as A,d as xe,b as Ae,aR as pe}from"./wnqW1tdD.js";import{i as W}from"./BjRbZGyQ.js";import{s as Le}from"./BDqVm3Gq.js";import{s as It,c as Mt}from"./Bz0_kaay.js";import{i as me}from"./BxG_UISn.js";import{l as fe,p as v}from"./Cmdkv-7M.js";import{e as Ie,s as J}from"./CDPCzm7q.js";import{a as Me,r as Tt}from"./rh_XW2Tv.js";import{b as ft}from"./B2uh23P-.js";import{b as Rt}from"./CJ-FD9ng.js";import{e as Wt,i as Nt}from"./CsnEE4l9.js";function Ue(a){return function(...e){var t=e[0];return t.preventDefault(),a==null?void 0:a.apply(this,e)}}var it,te="colors",ge="sizes",y="space",Vt={gap:y,gridGap:y,columnGap:y,gridColumnGap:y,rowGap:y,gridRowGap:y,inset:y,insetBlock:y,insetBlockEnd:y,insetBlockStart:y,insetInline:y,insetInlineEnd:y,insetInlineStart:y,margin:y,marginTop:y,marginRight:y,marginBottom:y,marginLeft:y,marginBlock:y,marginBlockEnd:y,marginBlockStart:y,marginInline:y,marginInlineEnd:y,marginInlineStart:y,padding:y,paddingTop:y,paddingRight:y,paddingBottom:y,paddingLeft:y,paddingBlock:y,paddingBlockEnd:y,paddingBlockStart:y,paddingInline:y,paddingInlineEnd:y,paddingInlineStart:y,top:y,right:y,bottom:y,left:y,scrollMargin:y,scrollMarginTop:y,scrollMarginRight:y,scrollMarginBottom:y,scrollMarginLeft:y,scrollMarginX:y,scrollMarginY:y,scrollMarginBlock:y,scrollMarginBlockEnd:y,scrollMarginBlockStart:y,scrollMarginInline:y,scrollMarginInlineEnd:y,scrollMarginInlineStart:y,scrollPadding:y,scrollPaddingTop:y,scrollPaddingRight:y,scrollPaddingBottom:y,scrollPaddingLeft:y,scrollPaddingX:y,scrollPaddingY:y,scrollPaddingBlock:y,scrollPaddingBlockEnd:y,scrollPaddingBlockStart:y,scrollPaddingInline:y,scrollPaddingInlineEnd:y,scrollPaddingInlineStart:y,fontSize:"fontSizes",background:te,backgroundColor:te,backgroundImage:te,borderImage:te,border:te,borderBlock:te,borderBlockEnd:te,borderBlockStart:te,borderBottom:te,borderBottomColor:te,borderColor:te,borderInline:te,borderInlineEnd:te,borderInlineStart:te,borderLeft:te,borderLeftColor:te,borderRight:te,borderRightColor:te,borderTop:te,borderTopColor:te,caretColor:te,color:te,columnRuleColor:te,fill:te,outline:te,outlineColor:te,stroke:te,textDecorationColor:te,fontFamily:"fonts",fontWeight:"fontWeights",lineHeight:"lineHeights",letterSpacing:"letterSpacings",blockSize:ge,minBlockSize:ge,maxBlockSize:ge,inlineSize:ge,minInlineSize:ge,maxInlineSize:ge,width:ge,minWidth:ge,maxWidth:ge,height:ge,minHeight:ge,maxHeight:ge,flexBasis:ge,gridTemplateColumns:ge,gridTemplateRows:ge,borderWidth:"borderWidths",borderTopWidth:"borderWidths",borderRightWidth:"borderWidths",borderBottomWidth:"borderWidths",borderLeftWidth:"borderWidths",borderStyle:"borderStyles",borderTopStyle:"borderStyles",borderRightStyle:"borderStyles",borderBottomStyle:"borderStyles",borderLeftStyle:"borderStyles",borderRadius:"radii",borderTopLeftRadius:"radii",borderTopRightRadius:"radii",borderBottomRightRadius:"radii",borderBottomLeftRadius:"radii",boxShadow:"shadows",textShadow:"shadows",transition:"transitions",zIndex:"zIndices"},Et=(a,e)=>typeof e=="function"?{"()":Function.prototype.toString.call(e)}:e,Ze=()=>{const a=Object.create(null);return(e,t,...r)=>{const l=(i=>JSON.stringify(i,Et))(e);return l in a?a[l]:a[l]=t(e,...r)}},Ye=Symbol.for("sxs.internal"),lt=(a,e)=>Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)),st=a=>{for(const e in a)return!0;return!1},{hasOwnProperty:Dt}=Object.prototype,nt=a=>a.includes("-")?a:a.replace(/[A-Z]/g,e=>"-"+e.toLowerCase()),Ft=/\s+(?![^()]*\))/,De=a=>e=>a(...typeof e=="string"?String(e).split(Ft):[e]),ct={appearance:a=>({WebkitAppearance:a,appearance:a}),backfaceVisibility:a=>({WebkitBackfaceVisibility:a,backfaceVisibility:a}),backdropFilter:a=>({WebkitBackdropFilter:a,backdropFilter:a}),backgroundClip:a=>({WebkitBackgroundClip:a,backgroundClip:a}),boxDecorationBreak:a=>({WebkitBoxDecorationBreak:a,boxDecorationBreak:a}),clipPath:a=>({WebkitClipPath:a,clipPath:a}),content:a=>({content:a.includes('"')||a.includes("'")||/^([A-Za-z]+\([^]*|[^]*-quote|inherit|initial|none|normal|revert|unset)$/.test(a)?a:`"${a}"`}),hyphens:a=>({WebkitHyphens:a,hyphens:a}),maskImage:a=>({WebkitMaskImage:a,maskImage:a}),maskSize:a=>({WebkitMaskSize:a,maskSize:a}),tabSize:a=>({MozTabSize:a,tabSize:a}),textSizeAdjust:a=>({WebkitTextSizeAdjust:a,textSizeAdjust:a}),userSelect:a=>({WebkitUserSelect:a,userSelect:a}),marginBlock:De((a,e)=>({marginBlockStart:a,marginBlockEnd:e||a})),marginInline:De((a,e)=>({marginInlineStart:a,marginInlineEnd:e||a})),maxSize:De((a,e)=>({maxBlockSize:a,maxInlineSize:e||a})),minSize:De((a,e)=>({minBlockSize:a,minInlineSize:e||a})),paddingBlock:De((a,e)=>({paddingBlockStart:a,paddingBlockEnd:e||a})),paddingInline:De((a,e)=>({paddingInlineStart:a,paddingInlineEnd:e||a}))},at=/([\d.]+)([^]*)/,At=(a,e)=>a.length?a.reduce((t,r)=>(t.push(...e.map(l=>l.includes("&")?l.replace(/&/g,/[ +>|~]/.test(r)&&/&.*&/.test(l)?`:is(${r})`:r):r+" "+l)),t),[]):e,jt=(a,e)=>a in Ht&&typeof e=="string"?e.replace(/^((?:[^]*[^\w-])?)(fit-content|stretch)((?:[^\w-][^]*)?)$/,(t,r,l,i)=>r+(l==="stretch"?`-moz-available${i};${nt(a)}:${r}-webkit-fill-available`:`-moz-fit-content${i};${nt(a)}:${r}fit-content`)+i):String(e),Ht={blockSize:1,height:1,inlineSize:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,width:1},ke=a=>a?a+"-":"",ht=(a,e,t)=>a.replace(/([+-])?((?:\d+(?:\.\d*)?|\.\d+)(?:[Ee][+-]?\d+)?)?(\$|--)([$\w-]+)/g,(r,l,i,c,s)=>c=="$"==!!i?r:(l||c=="--"?"calc(":"")+"var(--"+(c==="$"?ke(e)+(s.includes("$")?"":ke(t))+s.replace(/\$/g,"-"):s)+")"+(l||c=="--"?"*"+(l||"")+(i||"1")+")":"")),Ot=/\s*,\s*(?![^()]*\))/,Gt=Object.prototype.toString,Fe=(a,e,t,r,l)=>{let i,c,s;const o=(g,n,d)=>{let u,m;const C=b=>{for(u in b){const j=u.charCodeAt(0)===64,H=j&&Array.isArray(b[u])?b[u]:[b[u]];for(m of H){const D=/[A-Z]/.test(k=u)?k:k.replace(/-[^]/g,ee=>ee[1].toUpperCase()),Q=typeof m=="object"&&m&&m.toString===Gt&&(!r.utils[D]||!n.length);if(D in r.utils&&!Q){const ee=r.utils[D];if(ee!==c){c=ee,C(ee(m)),c=null;continue}}else if(D in ct){const ee=ct[D];if(ee!==s){s=ee,C(ee(m)),s=null;continue}}if(j&&(N=u.slice(1)in r.media?"@media "+r.media[u.slice(1)]:u,u=N.replace(/\(\s*([\w-]+)\s*(=|<|<=|>|>=)\s*([\w-]+)\s*(?:(<|<=|>|>=)\s*([\w-]+)\s*)?\)/g,(ee,R,M,w,B,L)=>{const z=at.test(R),K=.0625*(z?-1:1),[G,ne]=z?[w,R]:[R,w];return"("+(M[0]==="="?"":M[0]===">"===z?"max-":"min-")+G+":"+(M[0]!=="="&&M.length===1?ne.replace(at,(X,T,S)=>Number(T)+K*(M===">"?1:-1)+S):ne)+(B?") and ("+(B[0]===">"?"min-":"max-")+G+":"+(B.length===1?L.replace(at,(X,T,S)=>Number(T)+K*(B===">"?-1:1)+S):L):"")+")"})),Q){const ee=j?d.concat(u):[...d],R=j?[...n]:At(n,u.split(Ot));i!==void 0&&l(dt(...i)),i=void 0,o(m,R,ee)}else i===void 0&&(i=[[],n,d]),u=j||u.charCodeAt(0)!==36?u:`--${ke(r.prefix)}${u.slice(1).replace(/\$/g,"-")}`,m=Q?m:typeof m=="number"?m&&D in Ut?String(m)+"px":String(m):ht(jt(D,m??""),r.prefix,r.themeMap[D]),i[0].push(`${j?`${u} `:`${nt(u)}:`}${m}`)}}var N,k};C(g),i!==void 0&&l(dt(...i)),i=void 0};o(a,e,t)},dt=(a,e,t)=>`${t.map(r=>`${r}{`).join("")}${e.length?`${e.join(",")}{`:""}${a.join(";")}${e.length?"}":""}${Array(t.length?t.length+1:0).join("}")}`,Ut={animationDelay:1,animationDuration:1,backgroundSize:1,blockSize:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockEndWidth:1,borderBlockStart:1,borderBlockStartWidth:1,borderBlockWidth:1,borderBottom:1,borderBottomLeftRadius:1,borderBottomRightRadius:1,borderBottomWidth:1,borderEndEndRadius:1,borderEndStartRadius:1,borderInlineEnd:1,borderInlineEndWidth:1,borderInlineStart:1,borderInlineStartWidth:1,borderInlineWidth:1,borderLeft:1,borderLeftWidth:1,borderRadius:1,borderRight:1,borderRightWidth:1,borderSpacing:1,borderStartEndRadius:1,borderStartStartRadius:1,borderTop:1,borderTopLeftRadius:1,borderTopRightRadius:1,borderTopWidth:1,borderWidth:1,bottom:1,columnGap:1,columnRule:1,columnRuleWidth:1,columnWidth:1,containIntrinsicSize:1,flexBasis:1,fontSize:1,gap:1,gridAutoColumns:1,gridAutoRows:1,gridTemplateColumns:1,gridTemplateRows:1,height:1,inlineSize:1,inset:1,insetBlock:1,insetBlockEnd:1,insetBlockStart:1,insetInline:1,insetInlineEnd:1,insetInlineStart:1,left:1,letterSpacing:1,margin:1,marginBlock:1,marginBlockEnd:1,marginBlockStart:1,marginBottom:1,marginInline:1,marginInlineEnd:1,marginInlineStart:1,marginLeft:1,marginRight:1,marginTop:1,maxBlockSize:1,maxHeight:1,maxInlineSize:1,maxWidth:1,minBlockSize:1,minHeight:1,minInlineSize:1,minWidth:1,offsetDistance:1,offsetRotate:1,outline:1,outlineOffset:1,outlineWidth:1,overflowClipMargin:1,padding:1,paddingBlock:1,paddingBlockEnd:1,paddingBlockStart:1,paddingBottom:1,paddingInline:1,paddingInlineEnd:1,paddingInlineStart:1,paddingLeft:1,paddingRight:1,paddingTop:1,perspective:1,right:1,rowGap:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginBlockEnd:1,scrollMarginBlockStart:1,scrollMarginBottom:1,scrollMarginInline:1,scrollMarginInlineEnd:1,scrollMarginInlineStart:1,scrollMarginLeft:1,scrollMarginRight:1,scrollMarginTop:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingBlockEnd:1,scrollPaddingBlockStart:1,scrollPaddingBottom:1,scrollPaddingInline:1,scrollPaddingInlineEnd:1,scrollPaddingInlineStart:1,scrollPaddingLeft:1,scrollPaddingRight:1,scrollPaddingTop:1,shapeMargin:1,textDecoration:1,textDecorationThickness:1,textIndent:1,textUnderlineOffset:1,top:1,transitionDelay:1,transitionDuration:1,verticalAlign:1,width:1,wordSpacing:1},ut=a=>String.fromCharCode(a+(a>25?39:97)),ze=a=>(e=>{let t,r="";for(t=Math.abs(e);t>52;t=t/52|0)r=ut(t%52)+r;return ut(t%52)+r})(((e,t)=>{let r=t.length;for(;r;)e=33*e^t.charCodeAt(--r);return e})(5381,JSON.stringify(a))>>>0),Ge=["themed","global","styled","onevar","resonevar","allvar","inline"],Zt=a=>{if(a.href&&!a.href.startsWith(location.origin))return!1;try{return!!a.cssRules}catch{return!1}},Yt=a=>{let e;const t=()=>{const{cssRules:l}=e.sheet;return[].map.call(l,(i,c)=>{const{cssText:s}=i;let o="";if(s.startsWith("--sxs"))return"";if(l[c-1]&&(o=l[c-1].cssText).startsWith("--sxs")){if(!i.cssRules.length)return"";for(const g in e.rules)if(e.rules[g].group===i)return`--sxs{--sxs:${[...e.rules[g].cache].join(" ")}}${s}`;return i.cssRules.length?`${o}${s}`:""}return s}).join("")},r=()=>{if(e){const{rules:s,sheet:o}=e;if(!o.deleteRule){for(;Object(Object(o.cssRules)[0]).type===3;)o.cssRules.splice(0,1);o.cssRules=[]}for(const g in s)delete s[g]}const l=Object(a).styleSheets||[];for(const s of l)if(Zt(s)){for(let o=0,g=s.cssRules;g[o];++o){const n=Object(g[o]);if(n.type!==1)continue;const d=Object(g[o+1]);if(d.type!==4)continue;++o;const{cssText:u}=n;if(!u.startsWith("--sxs"))continue;const m=u.slice(14,-3).trim().split(/\s+/),C=Ge[m[0]];C&&(e||(e={sheet:s,reset:r,rules:{},toString:t}),e.rules[C]={group:d,index:o,cache:new Set(m)})}if(e)break}if(!e){const s=(o,g)=>({type:g,cssRules:[],insertRule(n,d){this.cssRules.splice(d,0,s(n,{import:3,undefined:1}[(n.toLowerCase().match(/^@([a-z]+)/)||[])[1]]||4))},get cssText(){return o==="@media{}"?`@media{${[].map.call(this.cssRules,n=>n.cssText).join("")}}`:o}});e={sheet:a?(a.head||a).appendChild(document.createElement("style")).sheet:s("","text/css"),rules:{},reset:r,toString:t}}const{sheet:i,rules:c}=e;for(let s=Ge.length-1;s>=0;--s){const o=Ge[s];if(!c[o]){const g=Ge[s+1],n=c[g]?c[g].index:i.cssRules.length;i.insertRule("@media{}",n),i.insertRule(`--sxs{--sxs:${s}}`,n),c[o]={group:i.cssRules[n+1],index:n,cache:new Set([s])}}qt(c[o])}};return r(),e},qt=a=>{const e=a.group;let t=e.cssRules.length;a.apply=r=>{try{e.insertRule(r,t),++t}catch{}}},Oe=Symbol(),Jt=Ze(),Kt=(a,e)=>Jt(a,()=>(...t)=>{let r={type:null,composers:new Set};for(const l of t)if(l!=null)if(l[Ye]){r.type==null&&(r.type=l[Ye].type);for(const i of l[Ye].composers)r.composers.add(i)}else l.constructor!==Object||l.$$typeof?r.type==null&&(r.type=l):r.composers.add(Xt(l,a));return r.type==null&&(r.type="span"),r.composers.size||r.composers.add(["PJLV",{},[],[],{},[]]),Qt(a,r,e)}),Xt=({variants:a,compoundVariants:e,defaultVariants:t,...r},l)=>{const i=`${ke(l.prefix)}c-${ze(r)}`,c=[],s=[],o=Object.create(null),g=[];for(const u in t)o[u]=String(t[u]);if(typeof a=="object"&&a)for(const u in a){n=o,d=u,Dt.call(n,d)||(o[u]="undefined");const m=a[u];for(const C in m){const b={[u]:String(C)};String(C)==="undefined"&&g.push(u);const N=m[C],k=[b,N,!st(N)];c.push(k)}}var n,d;if(typeof e=="object"&&e)for(const u of e){let{css:m,...C}=u;m=typeof m=="object"&&m||{};for(const N in C)C[N]=String(C[N]);const b=[C,m,!st(m)];s.push(b)}return[i,r,c,s,o,g]},Qt=(a,e,t)=>{const[r,l,i,c]=ea(e.composers),s=typeof e.type=="function"||e.type.$$typeof?(d=>{function u(){for(let m=0;m<u[Oe].length;m++){const[C,b]=u[Oe][m];d.rules[C].apply(b)}return u[Oe]=[],null}return u[Oe]=[],u.rules={},Ge.forEach(m=>u.rules[m]={apply:C=>u[Oe].push([m,C])}),u})(t):null,o=(s||t).rules,g=`.${r}${l.length>1?`:where(.${l.slice(1).join(".")})`:""}`,n=d=>{d=typeof d=="object"&&d||ta;const{css:u,...m}=d,C={};for(const k in i)if(delete m[k],k in d){let j=d[k];typeof j=="object"&&j?C[k]={"@initial":i[k],...j}:(j=String(j),C[k]=j!=="undefined"||c.has(k)?j:i[k])}else C[k]=i[k];const b=new Set([...l]);for(const[k,j,H,D]of e.composers){t.rules.styled.cache.has(k)||(t.rules.styled.cache.add(k),Fe(j,[`.${k}`],[],a,R=>{o.styled.apply(R)}));const Q=pt(H,C,a.media),ee=pt(D,C,a.media,!0);for(const R of Q)if(R!==void 0)for(const[M,w,B]of R){const L=`${k}-${ze(w)}-${M}`;b.add(L);const z=(B?t.rules.resonevar:t.rules.onevar).cache,K=B?o.resonevar:o.onevar;z.has(L)||(z.add(L),Fe(w,[`.${L}`],[],a,G=>{K.apply(G)}))}for(const R of ee)if(R!==void 0)for(const[M,w]of R){const B=`${k}-${ze(w)}-${M}`;b.add(B),t.rules.allvar.cache.has(B)||(t.rules.allvar.cache.add(B),Fe(w,[`.${B}`],[],a,L=>{o.allvar.apply(L)}))}}if(typeof u=="object"&&u){const k=`${r}-i${ze(u)}-css`;b.add(k),t.rules.inline.cache.has(k)||(t.rules.inline.cache.add(k),Fe(u,[`.${k}`],[],a,j=>{o.inline.apply(j)}))}for(const k of String(d.className||"").trim().split(/\s+/))k&&b.add(k);const N=m.className=[...b].join(" ");return{type:e.type,className:N,selector:g,props:m,toString:()=>N,deferredInjector:s}};return lt(n,{className:r,selector:g,[Ye]:e,toString:()=>(t.rules.styled.cache.has(r)||n(),r)})},ea=a=>{let e="";const t=[],r={},l=[];for(const[i,,,,c,s]of a){e===""&&(e=i),t.push(i),l.push(...s);for(const o in c){const g=c[o];(r[o]===void 0||g!=="undefined"||s.includes(g))&&(r[o]=g)}}return[e,t,r,new Set(l)]},pt=(a,e,t,r)=>{const l=[];e:for(let[i,c,s]of a){if(s)continue;let o,g=0,n=!1;for(o in i){const d=i[o];let u=e[o];if(u!==d){if(typeof u!="object"||!u)continue e;{let m,C,b=0;for(const N in u){if(d===String(u[N])){if(N!=="@initial"){const k=N.slice(1);(C=C||[]).push(k in t?t[k]:N.replace(/^@media ?/,"")),n=!0}g+=b,m=!0}++b}if(C&&C.length&&(c={["@media "+C.join(", ")]:c}),!m)continue e}}}(l[g]=l[g]||[]).push([r?"cv":`${o}-${i[o]}`,c,n])}return l},ta={},aa=Ze(),ra=(a,e)=>aa(a,()=>(...t)=>{const r=()=>{for(let l of t){l=typeof l=="object"&&l||{};let i=ze(l);if(!e.rules.global.cache.has(i)){if(e.rules.global.cache.add(i),"@import"in l){let c=[].indexOf.call(e.sheet.cssRules,e.rules.themed.group)-1;for(let s of[].concat(l["@import"]))s=s.includes('"')||s.includes("'")?s:`"${s}"`,e.sheet.insertRule(`@import ${s};`,c++);delete l["@import"]}Fe(l,[],[],a,c=>{e.rules.global.apply(c)})}}return""};return lt(r,{toString:r})}),na=Ze(),oa=(a,e)=>na(a,()=>t=>{const r=`${ke(a.prefix)}k-${ze(t)}`,l=()=>{if(!e.rules.global.cache.has(r)){e.rules.global.cache.add(r);const i=[];Fe(t,[],[],a,s=>i.push(s));const c=`@keyframes ${r}{${i.join("")}}`;e.rules.global.apply(c)}return r};return lt(l,{get name(){return l()},toString:l})}),la=class{constructor(a,e,t,r){this.token=a==null?"":String(a),this.value=e==null?"":String(e),this.scale=t==null?"":String(t),this.prefix=r==null?"":String(r)}get computedValue(){return"var("+this.variable+")"}get variable(){return"--"+ke(this.prefix)+ke(this.scale)+this.token}toString(){return this.computedValue}},ia=Ze(),sa=(a,e)=>ia(a,()=>(t,r)=>{r=typeof t=="object"&&t||Object(r);const l=`.${t=(t=typeof t=="string"?t:"")||`${ke(a.prefix)}t-${ze(r)}`}`,i={},c=[];for(const o in r){i[o]={};for(const g in r[o]){const n=`--${ke(a.prefix)}${o}-${g}`,d=ht(String(r[o][g]),a.prefix,o);i[o][g]=new la(g,d,o,a.prefix),c.push(`${n}:${d}`)}}const s=()=>{if(c.length&&!e.rules.themed.cache.has(t)){e.rules.themed.cache.add(t);const o=`${r===a.theme?":root,":""}.${t}{${c.join(";")}}`;e.rules.themed.apply(o)}return t};return{...i,get className(){return s()},selector:l,toString:s}}),ca=Ze(),vt=a=>{let e=!1;const t=ca(a,r=>{e=!0;const l="prefix"in(r=typeof r=="object"&&r||{})?String(r.prefix):"",i=typeof r.media=="object"&&r.media||{},c=typeof r.root=="object"?r.root||null:globalThis.document||null,s=typeof r.theme=="object"&&r.theme||{},o={prefix:l,media:i,theme:s,themeMap:typeof r.themeMap=="object"&&r.themeMap||{...Vt},utils:typeof r.utils=="object"&&r.utils||{}},g=Yt(c),n={css:Kt(o,g),globalCss:ra(o,g),keyframes:oa(o,g),createTheme:sa(o,g),reset(){g.reset(),n.theme.toString()},theme:{},sheet:g,config:o,prefix:l,getCssText:g.toString,toString:g.toString};return String(n.theme=n.createTheme(s)),n});return e||t.reset(),t},mt=()=>it||(it=vt()),da=(...a)=>mt().createTheme(...a),je=(...a)=>mt().css(...a),ua={default:{colors:{brand:"hsl(153 60.0% 53.0%)",brandAccent:"hsl(154 54.8% 45.1%)",brandButtonText:"white",defaultButtonBackground:"white",defaultButtonBackgroundHover:"#eaeaea",defaultButtonBorder:"lightgray",defaultButtonText:"gray",dividerBackground:"#eaeaea",inputBackground:"transparent",inputBorder:"lightgray",inputBorderHover:"gray",inputBorderFocus:"gray",inputText:"black",inputLabelText:"gray",inputPlaceholder:"darkgray",messageText:"#2b805a",messageBackground:"#e7fcf1",messageBorder:"#d0f3e1",messageTextDanger:"#ff6369",messageBackgroundDanger:"#fff8f8",messageBorderDanger:"#822025",anchorTextColor:"gray",anchorTextHoverColor:"darkgray"},space:{spaceSmall:"4px",spaceMedium:"8px",spaceLarge:"16px",labelBottomMargin:"8px",anchorBottomMargin:"4px",emailInputSpacing:"4px",socialAuthSpacing:"4px",buttonPadding:"10px 15px",inputPadding:"10px 15px"},fontSizes:{baseBodySize:"13px",baseInputSize:"14px",baseLabelSize:"14px",baseButtonSize:"14px"},fonts:{bodyFontFamily:"ui-sans-serif, sans-serif",buttonFontFamily:"ui-sans-serif, sans-serif",inputFontFamily:"ui-sans-serif, sans-serif",labelFontFamily:"ui-sans-serif, sans-serif"},borderWidths:{buttonBorderWidth:"1px",inputBorderWidth:"1px"},radii:{borderRadiusButton:"4px",buttonBorderRadius:"4px",inputBorderRadius:"4px"}},dark:{colors:{brandButtonText:"white",defaultButtonBackground:"#2e2e2e",defaultButtonBackgroundHover:"#3e3e3e",defaultButtonBorder:"#3e3e3e",defaultButtonText:"white",dividerBackground:"#2e2e2e",inputBackground:"#1e1e1e",inputBorder:"#3e3e3e",inputBorderHover:"gray",inputBorderFocus:"gray",inputText:"white",inputPlaceholder:"darkgray",messageText:"#85e0b7",messageBackground:"#072719",messageBorder:"#2b805a",messageBackgroundDanger:"#1f1315"}}},U={SIGN_IN:"sign_in",SIGN_UP:"sign_up",FORGOTTEN_PASSWORD:"forgotten_password",MAGIC_LINK:"magic_link",UPDATE_PASSWORD:"update_password",VERIFY_OTP:"verify_otp"},pa="supabase-auth-ui",ga={ROOT:"root",SIGN_IN:U.SIGN_IN,SIGN_UP:U.SIGN_UP,FORGOTTEN_PASSWORD:U.FORGOTTEN_PASSWORD,MAGIC_LINK:U.MAGIC_LINK,UPDATE_PASSWORD:U.UPDATE_PASSWORD,anchor:"ui-anchor",button:"ui-button",container:"ui-container",divider:"ui-divider",input:"ui-input",label:"ui-label",loader:"ui-loader",message:"ui-message"};function Te(a,e,t){var r,l;const i=[],c=ga[a];return i.push(t!=null&&t.prependedClassName?(t==null?void 0:t.prependedClassName)+"_"+c:pa+"_"+c),(r=t==null?void 0:t.className)!=null&&r[a]&&i.push((l=t==null?void 0:t.className)==null?void 0:l[a]),((t==null?void 0:t.extend)===void 0||(t==null?void 0:t.extend)===!0)&&i.push(e),i}function ot(a,e){let t;if(a&&e&&typeof a=="object"&&typeof e=="object"){if(Array.isArray(e))for(t=0;t<e.length;t++)a[t]=ot(a[t],e[t]);else for(t in e)a[t]=ot(a[t],e[t]);return a}return e}function rt(a,...e){let t=e.length;for(let r=0;r<t;r++)a=ot(a,e[r]);return a}function fa(a,e){return a.replace(/{{(\w+)}}/g,(t,r)=>e.hasOwnProperty(r)?e[r]:t)}var ha={sign_up:{email_label:"Email address",password_label:"Create a Password",email_input_placeholder:"Your email address",password_input_placeholder:"Your password",button_label:"Sign up",loading_button_label:"Signing up ...",social_provider_text:"Sign in with {{provider}}",link_text:"Don't have an account? Sign up",confirmation_text:"Check your email for the confirmation link"},sign_in:{email_label:"Email address",password_label:"Your Password",email_input_placeholder:"Your email address",password_input_placeholder:"Your password",button_label:"Sign in",loading_button_label:"Signing in ...",social_provider_text:"Sign in with {{provider}}",link_text:"Already have an account? Sign in"},magic_link:{email_input_label:"Email address",email_input_placeholder:"Your email address",button_label:"Send Magic Link",loading_button_label:"Sending Magic Link ...",link_text:"Send a magic link email",confirmation_text:"Check your email for the magic link"},forgotten_password:{email_label:"Email address",password_label:"Your Password",email_input_placeholder:"Your email address",button_label:"Send reset password instructions",loading_button_label:"Sending reset instructions ...",link_text:"Forgot your password?",confirmation_text:"Check your email for the password reset link"},update_password:{password_label:"New password",password_input_placeholder:"Your new password",button_label:"Update password",loading_button_label:"Updating password ...",confirmation_text:"Your password has been updated"},verify_otp:{email_input_label:"Email address",email_input_placeholder:"Your email address",phone_input_label:"Phone number",phone_input_placeholder:"Your phone number",token_input_label:"Token",token_input_placeholder:"Your Otp token",button_label:"Verify token",loading_button_label:"Signing in ..."}},va=ae("<a><!></a>");function Ce(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["href","appearance"]);he(e,!1);const l=re(),i=je({fontFamily:"$bodyFontFamily",fontSize:"$baseBodySize",marginBottom:"$anchorBottomMargin",color:"$anchorTextColor",display:"block",textAlign:"center",textDecoration:"underline","&:hover":{color:"$anchorTextHoverColor"}});let c=v(e,"href",8),s=v(e,"appearance",24,()=>({}));we(()=>_(s()),()=>{E(l,Te("anchor",i(),s()))}),Pe(),me();var o=va();Me(o,n=>({href:c(),...r,style:(_(s()),$(()=>{var d,u;return(u=(d=s())==null?void 0:d.style)==null?void 0:u.anchor})),class:n}),[()=>(h(l),$(()=>h(l).join(" ")))]);var g=le(o);Le(g,e,"default",{},null),ie(o),Ie("click",o,function(n){ft.call(this,e,n)}),f(a,o),ve()}var ma=ae("<button><!></button>");function He(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["color","appearance","loading"]);he(e,!1);const l=re();let i=v(e,"color",8,"default"),c=v(e,"appearance",24,()=>({})),s=v(e,"loading",8,!1);we(()=>(_(i()),_(c())),()=>{E(l,Te("button",i(),c()))}),Pe(),me();var o=ma();Me(o,n=>({...r,disabled:s(),style:(_(c()),$(()=>{var d,u;return(u=(d=c())==null?void 0:d.style)==null?void 0:u.button})),class:n}),[()=>(h(l),$(()=>h(l).join(" ")))],"svelte-ll2s9h");var g=le(o);Le(g,e,"default",{},null),ie(o),Ie("click",o,function(n){ft.call(this,e,n)}),f(a,o),ve()}var _a=ae("<div><!></div>");function ye(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["direction","gap","appearance"]);he(e,!1);const l=re(),i=je({display:"flex",gap:"4px",variants:{direction:{horizontal:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(48px, 1fr))"},vertical:{flexDirection:"column",margin:"8px 0"}},gap:{small:{gap:"4px"},medium:{gap:"8px"},large:{gap:"16px"}}}});let c=v(e,"direction",8,"horizontal"),s=v(e,"gap",8,"small"),o=v(e,"appearance",24,()=>({}));we(()=>(_(c()),_(s()),_(o())),()=>{E(l,Te("container",i({direction:c(),gap:s()}),o()))}),Pe(),me();var g=_a();Me(g,d=>({...r,style:(_(o()),$(()=>{var u,m;return(m=(u=o())==null?void 0:u.style)==null?void 0:m.container})),class:d}),[()=>(h(l),$(()=>h(l).join(" ")))]);var n=le(g);Le(n,e,"default",{},null),ie(g),f(a,g),ve()}var ba=ae("<input/>");function $e(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["value","appearance"]);he(e,!1);const l=re(),i=je({fontFamily:"$inputFontFamily",background:"$inputBackground",borderRadius:"$inputBorderRadius",padding:"$inputPadding",cursor:"text",borderWidth:"$inputBorderWidth",borderColor:"$inputBorder",borderStyle:"solid",fontSize:"$baseInputSize",width:"100%",color:"$inputText",boxSizing:"border-box","&:hover":{borderColor:"$inputBorderHover",outline:"none"},"&:focus":{borderColor:"$inputBorderFocus",outline:"none"},"&::placeholder":{color:"$inputPlaceholder",letterSpacing:"initial"},transitionProperty:"background-color, border",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionDuration:"100ms",variants:{type:{default:{letterSpacing:"0px"},password:{letterSpacing:"0px"}}}});let c=v(e,"value",12,void 0),s=v(e,"appearance",24,()=>({}));we(()=>_(s()),()=>{E(l,Te("input",i({type:"default"}),s()))}),Pe(),me();var o=ba();Tt(o),Me(o,g=>({...r,style:(_(s()),$(()=>{var n,d;return(d=(n=s())==null?void 0:n.style)==null?void 0:d.input})),class:g}),[()=>(h(l),$(()=>h(l).join(" ")))]),Rt(o,c),f(a,o),ve()}var xa=ae("<label><!></label>");function Be(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["appearance"]);he(e,!1);const l=re(),i=je({fontFamily:"$labelFontFamily",fontSize:"$baseLabelSize",marginBottom:"$labelBottomMargin",color:"$inputLabelText",display:"block"});let c=v(e,"appearance",24,()=>({}));we(()=>_(c()),()=>{E(l,Te("label",i(),c()))}),Pe(),me();var s=xa();Me(s,g=>({...r,style:(_(c()),$(()=>{var n,d;return(d=(n=c())==null?void 0:n.style)==null?void 0:d.label})),class:g}),[()=>(h(l),$(()=>h(l).join(" ")))]);var o=le(s);Le(o,e,"default",{},null),ie(s),f(a,s),ve()}var ya=ae("<span><!></span>");function Se(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["color","appearance"]);he(e,!1);const l=re(),i=je({fontFamily:"$bodyFontFamily",fontSize:"$baseInputSize",marginBottom:"$labelBottomMargin",display:"block",textAlign:"center",borderRadius:"0.375rem",padding:"1.5rem 1rem",lineHeight:"1rem",variants:{color:{default:{color:"$messageText",backgroundColor:"$messageBackground",border:"1px solid $messageBorder"},danger:{color:"$messageTextDanger",backgroundColor:"$messageBackgroundDanger",border:"1px solid $messageBorderDanger"}}}});let c=v(e,"color",8,"default"),s=v(e,"appearance",24,()=>({}));we(()=>(_(c()),_(s())),()=>{E(l,Te("message",i({color:c()}),s()))}),Pe(),me();var o=ya();Me(o,n=>({...r,style:(_(s()),$(()=>{var d,u;return(u=(d=s())==null?void 0:d.style)==null?void 0:u.message})),class:n}),[()=>(h(l),$(()=>h(l).join(" ")))]);var g=le(o);Le(g,e,"default",{},null),ie(o),f(a,o),ve()}var wa=ae("<div><!> <!></div> <div><!> <!></div> <!>",1),Sa=ae("<!> <!>",1),ka=ae("<!> <!>",1),Ca=ae("<!> <!> <!>",1),$a=ae('<form method="post" class="svelte-nm5p4o"><!> <!> <!></form>');function gt(a,e){he(e,!1);let t=v(e,"authView",12,"sign_in"),r=v(e,"email",12,""),l=v(e,"password",12,""),i=v(e,"supabaseClient",8),c=v(e,"redirectTo",8,void 0),s=v(e,"additionalData",8,void 0),o=v(e,"showLinks",8,!1),g=v(e,"magicLink",8,!0),n=v(e,"i18n",8),d=v(e,"appearance",8),u=re(""),m=re(""),C=re(!1),b=t()==="sign_in"?"sign_in":"sign_up";async function N(){var R;switch(E(C,!0),E(m,""),E(u,""),t()){case U.SIGN_IN:const{error:M}=await i().auth.signInWithPassword({email:r(),password:l()});M&&E(m,M.message),E(C,!1);break;case U.SIGN_UP:let w={emailRedirectTo:c()};s()&&(w.data=s());const{data:{user:B,session:L},error:z}=await i().auth.signUp({email:r(),password:l(),options:w});z?E(m,z.message):B&&!L&&E(u,(R=n().sign_up)==null?void 0:R.confirmation_text);break}E(C,!1)}me();var k=$a(),j=le(k);ye(j,{direction:"vertical",gap:"large",get appearance(){return d()},children:(R,M)=>{var w=Ca(),B=de(w);ye(B,{direction:"vertical",gap:"large",get appearance(){return d()},children:(G,ne)=>{var X=wa(),T=de(X),S=le(T);Be(S,{for:"email",get appearance(){return d()},children:(V,ce)=>{Z();var _e=q();Y(()=>J(_e,(_(n()),$(()=>{var be,ue;return(ue=(be=n())==null?void 0:be[b])==null?void 0:ue.email_label})))),f(V,_e)},$$slots:{default:!0}});var p=A(S,2);const x=xe(()=>(_(n()),$(()=>{var V,ce;return(ce=(V=n())==null?void 0:V[b])==null?void 0:ce.email_input_placeholder})));$e(p,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return h(x)},autocomplete:"email",get appearance(){return d()},get value(){return r()},set value(V){r(V)},$$legacy:!0}),ie(T);var I=A(T,2),F=le(I);Be(F,{for:"password",get appearance(){return d()},children:(V,ce)=>{Z();var _e=q();Y(()=>J(_e,(_(n()),$(()=>{var be,ue;return(ue=(be=n())==null?void 0:be[b])==null?void 0:ue.password_label})))),f(V,_e)},$$slots:{default:!0}});var P=A(F,2);const O=xe(()=>(_(n()),$(()=>{var V,ce;return(ce=(V=n())==null?void 0:V[b])==null?void 0:ce.password_input_placeholder}))),se=xe(()=>(_(t()),_(U),$(()=>t()===U.SIGN_IN?"current-password":"new-password")));$e(P,{id:"password",type:"password",name:"password",get placeholder(){return h(O)},get autocomplete(){return h(se)},get appearance(){return d()},get value(){return l()},set value(V){l(V)},$$legacy:!0}),ie(I);var oe=A(I,2);Le(oe,e,"default",{},null),f(G,X)},$$slots:{default:!0}});var L=A(B,2);He(L,{type:"submit",color:"primary",get loading(){return h(C)},get appearance(){return d()},children:(G,ne)=>{Z();var X=q();Y(()=>J(X,(h(C),_(n()),$(()=>{var T,S,p,x;return h(C)?(S=(T=n())==null?void 0:T[b])==null?void 0:S.loading_button_label:(x=(p=n())==null?void 0:p[b])==null?void 0:x.button_label})))),f(G,X)},$$slots:{default:!0}});var z=A(L,2);{var K=G=>{ye(G,{direction:"vertical",gap:"small",get appearance(){return d()},children:(ne,X)=>{var T=ka(),S=de(T);{var p=P=>{Ce(P,{href:"#auth-magic-link",get appearance(){return d()},$$events:{click:O=>{O.preventDefault(),t(U.MAGIC_LINK)}},children:(O,se)=>{Z();var oe=q();Y(()=>J(oe,(_(n()),$(()=>{var V,ce;return(ce=(V=n())==null?void 0:V.magic_link)==null?void 0:ce.link_text})))),f(O,oe)},$$slots:{default:!0}})};W(S,P=>{_(t()),_(U),_(g()),$(()=>t()===U.SIGN_IN&&g())&&P(p)})}var x=A(S,2);{var I=P=>{var O=Sa(),se=de(O);Ce(se,{href:"#auth-forgot-password",get appearance(){return d()},$$events:{click:V=>{V.preventDefault(),t(U.FORGOTTEN_PASSWORD)}},children:(V,ce)=>{Z();var _e=q();Y(()=>J(_e,(_(n()),$(()=>{var be,ue;return(ue=(be=n())==null?void 0:be.forgotten_password)==null?void 0:ue.link_text})))),f(V,_e)},$$slots:{default:!0}});var oe=A(se,2);Ce(oe,{href:"#auth-sign-up",get appearance(){return d()},$$events:{click:V=>{V.preventDefault(),t(U.SIGN_UP)}},children:(V,ce)=>{Z();var _e=q();Y(()=>J(_e,(_(n()),$(()=>{var be,ue;return(ue=(be=n())==null?void 0:be.sign_up)==null?void 0:ue.link_text})))),f(V,_e)},$$slots:{default:!0}}),f(P,O)},F=P=>{Ce(P,{href:"#auth-sign-in",get appearance(){return d()},$$events:{click:O=>{O.preventDefault(),t(U.SIGN_IN)}},children:(O,se)=>{Z();var oe=q();Y(()=>J(oe,(_(n()),$(()=>{var V,ce;return(ce=(V=n())==null?void 0:V.sign_in)==null?void 0:ce.link_text})))),f(O,oe)},$$slots:{default:!0}})};W(x,P=>{_(t()),_(U),$(()=>t()===U.SIGN_IN)?P(I):P(F,!1)})}f(ne,T)},$$slots:{default:!0}})};W(z,G=>{o()&&G(K)})}f(R,w)},$$slots:{default:!0}});var H=A(j,2);{var D=R=>{Se(R,{get appearance(){return d()},children:(M,w)=>{Z();var B=q();Y(()=>J(B,h(u))),f(M,B)},$$slots:{default:!0}})};W(H,R=>{h(u)&&R(D)})}var Q=A(H,2);{var ee=R=>{Se(R,{color:"danger",get appearance(){return d()},children:(M,w)=>{Z();var B=q();Y(()=>J(B,h(m))),f(M,B)},$$slots:{default:!0}})};W(Q,R=>{h(m)&&R(ee)})}ie(k),Ie("submit",k,Ue(N)),f(a,k),ve()}var Ba=ae("<div><!> <!></div> <!>",1),Pa=ae("<!> <!> <!> <!>",1),La=ae('<form id="auth-forgot-password" method="post" class="svelte-nm5p4o"><!></form>');function za(a,e){he(e,!1);let t=v(e,"i18n",8),r=v(e,"supabaseClient",8),l=v(e,"authView",12),i=v(e,"redirectTo",8,void 0),c=v(e,"email",12,""),s=v(e,"showLinks",8,!1),o=v(e,"appearance",8),g=re(""),n=re(""),d=re(!1);async function u(){var N;E(d,!0),E(n,""),E(g,"");const{error:b}=await r().auth.resetPasswordForEmail(c(),{redirectTo:i()});b?E(n,b.message):E(g,(N=t().forgotten_password)==null?void 0:N.confirmation_text),E(d,!1)}me();var m=La(),C=le(m);ye(C,{direction:"vertical",gap:"large",get appearance(){return o()},children:(b,N)=>{var k=Pa(),j=de(k);ye(j,{direction:"vertical",gap:"large",get appearance(){return o()},children:(w,B)=>{var L=Ba(),z=de(L),K=le(z);Be(K,{for:"email",get appearance(){return o()},children:(T,S)=>{Z();var p=q();Y(()=>J(p,(_(t()),$(()=>{var x,I;return(I=(x=t())==null?void 0:x.forgotten_password)==null?void 0:I.email_label})))),f(T,p)},$$slots:{default:!0}});var G=A(K,2);const ne=xe(()=>(_(t()),$(()=>{var T,S;return(S=(T=t())==null?void 0:T.forgotten_password)==null?void 0:S.email_input_placeholder})));$e(G,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return h(ne)},autocomplete:"email",get appearance(){return o()},get value(){return c()},set value(T){c(T)},$$legacy:!0}),ie(z);var X=A(z,2);He(X,{type:"submit",color:"primary",get loading(){return h(d)},get appearance(){return o()},children:(T,S)=>{Z();var p=q();Y(()=>J(p,(h(d),_(t()),$(()=>{var x,I,F,P;return h(d)?(I=(x=t())==null?void 0:x.forgotten_password)==null?void 0:I.loading_button_label:(P=(F=t())==null?void 0:F.forgotten_password)==null?void 0:P.button_label})))),f(T,p)},$$slots:{default:!0}}),f(w,L)},$$slots:{default:!0}});var H=A(j,2);{var D=w=>{Ce(w,{href:"#auth-magic-link",get appearance(){return o()},$$events:{click:B=>{B.preventDefault(),l(U.SIGN_IN)}},children:(B,L)=>{Z();var z=q();Y(()=>J(z,(_(t()),$(()=>{var K,G;return(G=(K=t())==null?void 0:K.sign_in)==null?void 0:G.link_text})))),f(B,z)},$$slots:{default:!0}})};W(H,w=>{s()&&w(D)})}var Q=A(H,2);{var ee=w=>{Se(w,{get appearance(){return o()},children:(B,L)=>{Z();var z=q();Y(()=>J(z,h(g))),f(B,z)},$$slots:{default:!0}})};W(Q,w=>{h(g)&&w(ee)})}var R=A(Q,2);{var M=w=>{Se(w,{color:"danger",get appearance(){return o()},children:(B,L)=>{Z();var z=q();Y(()=>J(z,h(n))),f(B,z)},$$slots:{default:!0}})};W(R,w=>{h(n)&&w(M)})}f(b,k)},$$slots:{default:!0}}),ie(m),Ie("submit",m,Ue(u)),f(a,m),ve()}var Ia=ae("<div><!> <!></div> <!>",1),Ma=ae("<!> <!> <!> <!>",1),Ta=ae('<form id="auth-magic-link" method="post" class="svelte-nm5p4o"><!></form>');function Ra(a,e){he(e,!1);let t=v(e,"i18n",8),r=v(e,"supabaseClient",8),l=v(e,"authView",12),i=v(e,"redirectTo",8,void 0),c=v(e,"appearance",8),s=v(e,"showLinks",8,!1),o=v(e,"email",12,""),g=re(""),n=re(""),d=re(!1);async function u(){var N;E(d,!0),E(n,""),E(g,"");const{error:b}=await r().auth.signInWithOtp({email:o(),options:{emailRedirectTo:i()}});b?E(n,b.message):E(g,(N=t().magic_link)==null?void 0:N.confirmation_text),E(d,!1)}me();var m=Ta(),C=le(m);ye(C,{direction:"vertical",gap:"large",get appearance(){return c()},children:(b,N)=>{var k=Ma(),j=de(k);ye(j,{direction:"vertical",gap:"large",get appearance(){return c()},children:(w,B)=>{var L=Ia(),z=de(L),K=le(z);Be(K,{for:"email",get appearance(){return c()},children:(T,S)=>{Z();var p=q();Y(()=>J(p,(_(t()),$(()=>{var x,I;return(I=(x=t())==null?void 0:x.magic_link)==null?void 0:I.email_input_label})))),f(T,p)},$$slots:{default:!0}});var G=A(K,2);const ne=xe(()=>(_(t()),$(()=>{var T,S;return(S=(T=t())==null?void 0:T.magic_link)==null?void 0:S.email_input_placeholder})));$e(G,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return h(ne)},autocomplete:"email",get appearance(){return c()},get value(){return o()},set value(T){o(T)},$$legacy:!0}),ie(z);var X=A(z,2);He(X,{type:"submit",color:"primary",get loading(){return h(d)},get appearance(){return c()},children:(T,S)=>{Z();var p=q();Y(()=>J(p,(h(d),_(t()),$(()=>{var x,I,F,P;return h(d)?(I=(x=t())==null?void 0:x.magic_link)==null?void 0:I.loading_button_label:(P=(F=t())==null?void 0:F.magic_link)==null?void 0:P.button_label})))),f(T,p)},$$slots:{default:!0}}),f(w,L)},$$slots:{default:!0}});var H=A(j,2);{var D=w=>{Ce(w,{href:"#auth-sign-in",get appearance(){return c()},$$events:{click:B=>{B.preventDefault(),l(U.SIGN_IN)}},children:(B,L)=>{Z();var z=q();Y(()=>J(z,(_(t()),$(()=>{var K,G;return(G=(K=t())==null?void 0:K.sign_in)==null?void 0:G.link_text})))),f(B,z)},$$slots:{default:!0}})};W(H,w=>{s()&&w(D)})}var Q=A(H,2);{var ee=w=>{Se(w,{get appearance(){return c()},children:(B,L)=>{Z();var z=q();Y(()=>J(z,h(g))),f(B,z)},$$slots:{default:!0}})};W(Q,w=>{h(g)&&w(ee)})}var R=A(Q,2);{var M=w=>{Se(w,{color:"danger",get appearance(){return c()},children:(B,L)=>{Z();var z=q();Y(()=>J(z,h(n))),f(B,z)},$$slots:{default:!0}})};W(R,w=>{h(n)&&w(M)})}f(b,k)},$$slots:{default:!0}}),ie(m),Ie("submit",m,Ue(u)),f(a,m),ve()}var Wa=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path></svg>'),Na=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#039be5" d="M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z"></path><path fill="#fff" d="M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z"></path></svg>'),Va=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#03A9F4" d="M42,12.429c-1.323,0.586-2.746,0.977-4.247,1.162c1.526-0.906,2.7-2.351,3.251-4.058c-1.428,0.837-3.01,1.452-4.693,1.776C34.967,9.884,33.05,9,30.926,9c-4.08,0-7.387,3.278-7.387,7.32c0,0.572,0.067,1.129,0.193,1.67c-6.138-0.308-11.582-3.226-15.224-7.654c-0.64,1.082-1,2.349-1,3.686c0,2.541,1.301,4.778,3.285,6.096c-1.211-0.037-2.351-0.374-3.349-0.914c0,0.022,0,0.055,0,0.086c0,3.551,2.547,6.508,5.923,7.181c-0.617,0.169-1.269,0.263-1.941,0.263c-0.477,0-0.942-0.054-1.392-0.135c0.94,2.902,3.667,5.023,6.898,5.086c-2.528,1.96-5.712,3.134-9.174,3.134c-0.598,0-1.183-0.034-1.761-0.104C9.268,36.786,13.152,38,17.321,38c13.585,0,21.017-11.156,21.017-20.834c0-0.317-0.01-0.633-0.025-0.945C39.763,15.197,41.013,13.905,42,12.429"></path></svg>'),Ea=pe('<svg fill="gray" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="21px" height="21px" class="svelte-10a6av0"> <path d="M 15.904297 1.078125 C 15.843359 1.06875 15.774219 1.0746094 15.699219 1.0996094 C 14.699219 1.2996094 13.600391 1.8996094 12.900391 2.5996094 C 12.300391 3.1996094 11.800781 4.1996094 11.800781 5.0996094 C 11.800781 5.2996094 11.999219 5.5 12.199219 5.5 C 13.299219 5.4 14.399609 4.7996094 15.099609 4.0996094 C 15.699609 3.2996094 16.199219 2.4 16.199219 1.5 C 16.199219 1.275 16.087109 1.10625 15.904297 1.078125 z M 16.199219 5.4003906 C 14.399219 5.4003906 13.600391 6.5 12.400391 6.5 C 11.100391 6.5 9.9003906 5.5 8.4003906 5.5 C 6.3003906 5.5 3.0996094 7.4996094 3.0996094 12.099609 C 2.9996094 16.299609 6.8 21 9 21 C 10.3 21 10.600391 20.199219 12.400391 20.199219 C 14.200391 20.199219 14.600391 21 15.900391 21 C 17.400391 21 18.500391 19.399609 19.400391 18.099609 C 19.800391 17.399609 20.100391 17.000391 20.400391 16.400391 C 20.600391 16.000391 20.4 15.600391 20 15.400391 C 17.4 14.100391 16.900781 9.9003906 19.800781 8.4003906 C 20.300781 8.1003906 20.4 7.4992188 20 7.1992188 C 18.9 6.1992187 17.299219 5.4003906 16.199219 5.4003906 z"></path></svg>'),Da=pe('<svg fill="gray" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="21px" height="21px" class="svelte-10a6av0"> <path d="M15,3C8.373,3,3,8.373,3,15c0,5.623,3.872,10.328,9.092,11.63C12.036,26.468,12,26.28,12,26.047v-2.051 c-0.487,0-1.303,0-1.508,0c-0.821,0-1.551-0.353-1.905-1.009c-0.393-0.729-0.461-1.844-1.435-2.526 c-0.289-0.227-0.069-0.486,0.264-0.451c0.615,0.174,1.125,0.596,1.605,1.222c0.478,0.627,0.703,0.769,1.596,0.769 c0.433,0,1.081-0.025,1.691-0.121c0.328-0.833,0.895-1.6,1.588-1.962c-3.996-0.411-5.903-2.399-5.903-5.098 c0-1.162,0.495-2.286,1.336-3.233C9.053,10.647,8.706,8.73,9.435,8c1.798,0,2.885,1.166,3.146,1.481C13.477,9.174,14.461,9,15.495,9 c1.036,0,2.024,0.174,2.922,0.483C18.675,9.17,19.763,8,21.565,8c0.732,0.731,0.381,2.656,0.102,3.594 c0.836,0.945,1.328,2.066,1.328,3.226c0,2.697-1.904,4.684-5.894,5.097C18.199,20.49,19,22.1,19,23.313v2.734 c0,0.104-0.023,0.179-0.035,0.268C23.641,24.676,27,20.236,27,15C27,8.373,21.627,3,15,3z"></path></svg>'),Fa=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#e53935" d="M24 43L16 20 32 20z"></path><path fill="#ff7043" d="M24 43L42 20 32 20z"></path><path fill="#e53935" d="M37 5L42 20 32 20z"></path><path fill="#ffa726" d="M24 43L42 20 45 28z"></path><path fill="#ff7043" d="M24 43L6 20 16 20z"></path><path fill="#e53935" d="M11 5L6 20 16 20z"></path><path fill="#ffa726" d="M24 43L6 20 3 28z"></path></svg>'),Aa=pe('<svg xmlns="http://www.w3.org/2000/svg" width="21px" height="21px" viewBox="0 0 62.42 62.42" class="svelte-10a6av0"><defs><linearGradient id="New_Gradient_Swatch_1" x1="64.01" y1="30.27" x2="32.99" y2="54.48" gradientUnits="userSpaceOnUse"><stop offset="0.18" stop-color="#0052cc"></stop><stop offset="1" stop-color="#2684ff"></stop></linearGradient></defs><title>Bitbucket-blue</title><g id="Layer_2" data-name="Layer 2"><g id="Blue" transform="translate(0 -3.13)"><path d="M2,6.26A2,2,0,0,0,0,8.58L8.49,60.12a2.72,2.72,0,0,0,2.66,2.27H51.88a2,2,0,0,0,2-1.68L62.37,8.59a2,2,0,0,0-2-2.32ZM37.75,43.51h-13L21.23,25.12H40.9Z" fill="#2684ff"></path><path d="M59.67,25.12H40.9L37.75,43.51h-13L9.4,61.73a2.71,2.71,0,0,0,1.75.66H51.89a2,2,0,0,0,2-1.68Z" fill="url(#New_Gradient_Swatch_1)"></path></g></g></svg>'),ja=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#536dfe" d="M39.248,10.177c-2.804-1.287-5.812-2.235-8.956-2.778c-0.057-0.01-0.114,0.016-0.144,0.068	c-0.387,0.688-0.815,1.585-1.115,2.291c-3.382-0.506-6.747-0.506-10.059,0c-0.3-0.721-0.744-1.603-1.133-2.291	c-0.03-0.051-0.087-0.077-0.144-0.068c-3.143,0.541-6.15,1.489-8.956,2.778c-0.024,0.01-0.045,0.028-0.059,0.051	c-5.704,8.522-7.267,16.835-6.5,25.044c0.003,0.04,0.026,0.079,0.057,0.103c3.763,2.764,7.409,4.442,10.987,5.554	c0.057,0.017,0.118-0.003,0.154-0.051c0.846-1.156,1.601-2.374,2.248-3.656c0.038-0.075,0.002-0.164-0.076-0.194	c-1.197-0.454-2.336-1.007-3.432-1.636c-0.087-0.051-0.094-0.175-0.014-0.234c0.231-0.173,0.461-0.353,0.682-0.534	c0.04-0.033,0.095-0.04,0.142-0.019c7.201,3.288,14.997,3.288,22.113,0c0.047-0.023,0.102-0.016,0.144,0.017	c0.22,0.182,0.451,0.363,0.683,0.536c0.08,0.059,0.075,0.183-0.012,0.234c-1.096,0.641-2.236,1.182-3.434,1.634	c-0.078,0.03-0.113,0.12-0.075,0.196c0.661,1.28,1.415,2.498,2.246,3.654c0.035,0.049,0.097,0.07,0.154,0.052	c3.595-1.112,7.241-2.79,11.004-5.554c0.033-0.024,0.054-0.061,0.057-0.101c0.917-9.491-1.537-17.735-6.505-25.044	C39.293,10.205,39.272,10.187,39.248,10.177z M16.703,30.273c-2.168,0-3.954-1.99-3.954-4.435s1.752-4.435,3.954-4.435	c2.22,0,3.989,2.008,3.954,4.435C20.658,28.282,18.906,30.273,16.703,30.273z M31.324,30.273c-2.168,0-3.954-1.99-3.954-4.435	s1.752-4.435,3.954-4.435c2.22,0,3.989,2.008,3.954,4.435C35.278,28.282,33.544,30.273,31.324,30.273z"></path></svg>'),Ha=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><linearGradient id="k8yl7~hDat~FaoWq8WjN6a" x1="-1254.397" x2="-1261.911" y1="877.268" y2="899.466" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#114a8b"></stop><stop offset="1" stop-color="#0669bc"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6a)" d="M17.634,6h11.305L17.203,40.773c-0.247,0.733-0.934,1.226-1.708,1.226H6.697 c-0.994,0-1.8-0.806-1.8-1.8c0-0.196,0.032-0.39,0.094-0.576L15.926,7.227C16.173,6.494,16.86,6,17.634,6L17.634,6z"></path><path fill="#0078d4" d="M34.062,29.324H16.135c-0.458-0.001-0.83,0.371-0.831,0.829c0,0.231,0.095,0.451,0.264,0.608 l11.52,10.752C27.423,41.826,27.865,42,28.324,42h10.151L34.062,29.324z"></path><linearGradient id="k8yl7~hDat~FaoWq8WjN6b" x1="-1252.05" x2="-1253.788" y1="887.612" y2="888.2" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-opacity=".3"></stop><stop offset=".071" stop-opacity=".2"></stop><stop offset=".321" stop-opacity=".1"></stop><stop offset=".623" stop-opacity=".05"></stop><stop offset="1" stop-opacity="0"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6b)" d="M17.634,6c-0.783-0.003-1.476,0.504-1.712,1.25L5.005,39.595 c-0.335,0.934,0.151,1.964,1.085,2.299C6.286,41.964,6.493,42,6.702,42h9.026c0.684-0.122,1.25-0.603,1.481-1.259l2.177-6.416 l7.776,7.253c0.326,0.27,0.735,0.419,1.158,0.422h10.114l-4.436-12.676l-12.931,0.003L28.98,6H17.634z"></path><linearGradient id="k8yl7~hDat~FaoWq8WjN6c" x1="-1252.952" x2="-1244.704" y1="876.6" y2="898.575" gradientTransform="translate(1981.75 -1362.063) scale(1.5625)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#3ccbf4"></stop><stop offset="1" stop-color="#2892df"></stop></linearGradient><path fill="url(#k8yl7~hDat~FaoWq8WjN6c)" d="M32.074,7.225C31.827,6.493,31.141,6,30.368,6h-12.6c0.772,0,1.459,0.493,1.705,1.224 l10.935,32.399c0.318,0.942-0.188,1.963-1.13,2.281C29.093,41.968,28.899,42,28.703,42h12.6c0.994,0,1.8-0.806,1.8-1.801 c0-0.196-0.032-0.39-0.095-0.575L32.074,7.225z"></path></svg>'),Oa=pe('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M472.136 163.959H408.584C407.401 163.959 406.218 163.327 405.666 162.3L354.651 73.6591C354.02 72.632 352.916 72 351.654 72H143.492C142.309 72 141.126 72.632 140.574 73.6591L87.5084 165.618L36.414 254.259C35.862 255.286 35.862 256.55 36.414 257.656L87.5084 346.297L140.495 438.335C141.047 439.362 142.23 440.073 143.413 439.994H351.654C352.837 439.994 354.02 439.362 354.651 438.335L405.745 349.694C406.297 348.667 407.48 347.956 408.663 348.035H472.215C474.344 348.035 476 346.297 476 344.243V167.83C475.921 165.697 474.186 163.959 472.136 163.959ZM228.728 349.694L212.721 377.345C212.485 377.74 212.091 378.135 211.696 378.372C211.223 378.609 210.75 378.767 210.198 378.767H178.422C177.318 378.767 176.293 378.214 175.82 377.187L128.431 294.787L123.779 286.65L106.748 257.498C106.511 257.103 106.353 256.629 106.432 256.076C106.432 255.602 106.59 255.049 106.827 254.654L123.937 224.949L175.899 134.886C176.451 133.938 177.476 133.306 178.501 133.306H210.198C210.75 133.306 211.302 133.464 211.854 133.701C212.248 133.938 212.643 134.254 212.879 134.728L228.886 162.537C229.359 163.485 229.28 164.67 228.728 165.539L177.397 254.654C177.16 255.049 177.081 255.523 177.081 255.918C177.081 256.392 177.239 256.787 177.397 257.182L228.728 346.218C229.438 347.403 229.359 348.667 228.728 349.694V349.694ZM388.083 257.498L371.051 286.65L366.399 294.787L319.011 377.187C318.459 378.135 317.512 378.767 316.409 378.767H284.632C284.08 378.767 283.607 378.609 283.134 378.372C282.74 378.135 282.346 377.819 282.109 377.345L266.103 349.694C265.393 348.667 265.393 347.403 266.024 346.376L317.355 257.34C317.591 256.945 317.67 256.471 317.67 256.076C317.67 255.602 317.513 255.207 317.355 254.812L266.024 165.697C265.472 164.749 265.393 163.643 265.866 162.695L281.873 134.886C282.109 134.491 282.503 134.096 282.898 133.859C283.371 133.543 283.923 133.464 284.553 133.464H316.409C317.512 133.464 318.538 134.017 319.011 135.044L370.972 225.107L388.083 254.812C388.319 255.286 388.477 255.76 388.477 256.234C388.477 256.55 388.319 257.024 388.083 257.498V257.498Z" fill="#008AAA"></path></svg>'),Ga=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#0288D1" d="M42,37c0,2.762-2.238,5-5,5H11c-2.761,0-5-2.238-5-5V11c0-2.762,2.239-5,5-5h26c2.762,0,5,2.238,5,5V37z"></path><path fill="#FFF" d="M12 19H17V36H12zM14.485 17h-.028C12.965 17 12 15.888 12 14.499 12 13.08 12.995 12 14.514 12c1.521 0 2.458 1.08 2.486 2.499C17 15.887 16.035 17 14.485 17zM36 36h-5v-9.099c0-2.198-1.225-3.698-3.192-3.698-1.501 0-2.313 1.012-2.707 1.99C24.957 25.543 25 26.511 25 27v9h-5V19h5v2.616C25.721 20.5 26.85 19 29.738 19c3.578 0 6.261 2.25 6.261 7.274L36 36 36 36z"></path></svg>'),Ua=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" fill-rule="evenodd" clip-rule="evenodd" class="svelte-10a6av0"><path fill="#fff" fill-rule="evenodd" d="M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z" clip-rule="evenodd"></path><path fill="#fff" fill-rule="evenodd" d="M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619 l23.971-1.387c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463 C13.171,14.718,12.862,15.181,12.862,16.182L12.862,16.182z" clip-rule="evenodd"></path><path fill="#424242" fill-rule="evenodd" d="M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619l23.971-1.387 c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463C13.171,14.718,12.862,15.181,12.862,16.182 L12.862,16.182z M36.526,17.413c0.154,0.694,0,1.387-0.695,1.465l-1.155,0.23v16.943c-1.003,0.539-1.928,0.847-2.698,0.847 c-1.234,0-1.543-0.385-2.467-1.54l-7.555-11.86v11.475l2.391,0.539c0,0,0,1.386-1.929,1.386l-5.317,0.308 c-0.154-0.308,0-1.078,0.539-1.232l1.388-0.385V20.418l-1.927-0.154c-0.155-0.694,0.23-1.694,1.31-1.772l5.704-0.385l7.862,12.015 V19.493l-2.005-0.23c-0.154-0.848,0.462-1.464,1.233-1.54L36.526,17.413z M7.389,5.862l21.968-1.618 c2.698-0.231,3.392-0.076,5.087,1.155l7.013,4.929C42.614,11.176,43,11.407,43,12.33v27.032c0,1.694-0.617,2.696-2.775,2.849 l-25.512,1.541c-1.62,0.077-2.391-0.154-3.239-1.232l-5.164-6.7C5.385,34.587,5,33.664,5,32.585V8.556 C5,7.171,5.617,6.015,7.389,5.862z" clip-rule="evenodd"></path></svg>'),Za=pe('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="21px" height="21px" class="svelte-10a6av0"><path fill="#33d375" d="M33,8c0-2.209-1.791-4-4-4s-4,1.791-4,4c0,1.254,0,9.741,0,11c0,2.209,1.791,4,4,4s4-1.791,4-4	C33,17.741,33,9.254,33,8z"></path><path fill="#33d375" d="M43,19c0,2.209-1.791,4-4,4c-1.195,0-4,0-4,0s0-2.986,0-4c0-2.209,1.791-4,4-4S43,16.791,43,19z"></path><path fill="#40c4ff" d="M8,14c-2.209,0-4,1.791-4,4s1.791,4,4,4c1.254,0,9.741,0,11,0c2.209,0,4-1.791,4-4s-1.791-4-4-4	C17.741,14,9.254,14,8,14z"></path><path fill="#40c4ff" d="M19,4c2.209,0,4,1.791,4,4c0,1.195,0,4,0,4s-2.986,0-4,0c-2.209,0-4-1.791-4-4S16.791,4,19,4z"></path><path fill="#e91e63" d="M14,39.006C14,41.212,15.791,43,18,43s4-1.788,4-3.994c0-1.252,0-9.727,0-10.984	c0-2.206-1.791-3.994-4-3.994s-4,1.788-4,3.994C14,29.279,14,37.754,14,39.006z"></path><path fill="#e91e63" d="M4,28.022c0-2.206,1.791-3.994,4-3.994c1.195,0,4,0,4,0s0,2.981,0,3.994c0,2.206-1.791,3.994-4,3.994	S4,30.228,4,28.022z"></path><path fill="#ffc107" d="M39,33c2.209,0,4-1.791,4-4s-1.791-4-4-4c-1.254,0-9.741,0-11,0c-2.209,0-4,1.791-4,4s1.791,4,4,4	C29.258,33,37.746,33,39,33z"></path><path fill="#ffc107" d="M28,43c-2.209,0-4-1.791-4-4c0-1.195,0-4,0-4s2.986,0,4,0c2.209,0,4,1.791,4,4S30.209,43,28,43z"></path></svg>'),Ya=pe('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M255.498 31.0034C131.513 31.0034 31 131.515 31 255.502C31 379.492 131.513 480 255.498 480C379.497 480 480 379.495 480 255.502C480 131.522 379.497 31.0135 255.495 31.0135L255.498 31V31.0034ZM358.453 354.798C354.432 361.391 345.801 363.486 339.204 359.435C286.496 327.237 220.139 319.947 141.993 337.801C134.463 339.516 126.957 334.798 125.24 327.264C123.516 319.731 128.217 312.225 135.767 310.511C221.284 290.972 294.639 299.384 353.816 335.549C360.413 339.596 362.504 348.2 358.453 354.798ZM385.932 293.67C380.864 301.903 370.088 304.503 361.858 299.438C301.512 262.345 209.528 251.602 138.151 273.272C128.893 276.067 119.118 270.851 116.309 261.61C113.521 252.353 118.74 242.597 127.981 239.782C209.512 215.044 310.87 227.026 380.17 269.612C388.4 274.68 391 285.456 385.935 293.676V293.673L385.932 293.67ZM388.293 230.016C315.935 187.039 196.56 183.089 127.479 204.055C116.387 207.42 104.654 201.159 101.293 190.063C97.9326 178.964 104.189 167.241 115.289 163.87C194.59 139.796 326.418 144.446 409.723 193.902C419.722 199.826 422.995 212.71 417.068 222.675C411.168 232.653 398.247 235.943 388.303 230.016H388.293V230.016Z" fill="#1ED760"></path></svg>'),qa=pe('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M416 240L352 304H288L232 360V304H160V64H416V240Z" fill="white"></path><path d="M144 32L64 112V400H160V480L240 400H304L448 256V32H144ZM416 240L352 304H288L232 360V304H160V64H416V240Z" fill="#9146FF"></path><path d="M368 120H336V216H368V120Z" fill="#9146FF"></path><path d="M280 120H248V216H280V120Z" fill="#9146FF"></path></svg>'),Ja=pe('<svg width="21px" height="21px" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-10a6av0"><path d="M33 256.043C33 264.556 35.3159 273.069 39.4845 280.202L117.993 415.493C126.098 429.298 138.373 440.572 153.657 445.634C183.764 455.528 214.797 442.873 229.618 417.333L248.609 384.661L173.806 256.043L252.777 119.831L271.768 87.1591C277.557 77.2654 284.968 69.4424 294 63H285.894H172.185C150.878 63 131.193 74.2742 120.54 92.6812L39.7161 231.884C35.3159 239.016 33 247.53 33 256.043Z" fill="#6363F1"></path><path d="M480 256.058C480 247.539 477.684 239.021 473.516 231.883L393.849 94.6596C379.028 69.3331 347.995 56.4396 317.888 66.34C302.603 71.4053 290.329 82.6871 282.224 96.5015L264.391 127.354L339.194 256.058L260.223 392.131L241.232 424.825C235.443 434.495 228.032 442.553 219 449H227.106H340.815C362.122 449 381.807 437.718 392.46 419.299L473.284 280.003C477.684 272.866 480 264.577 480 256.058Z" fill="#6363F1"></path></svg>'),Ka=pe('<svg xmlns="http://www.w3.org/2000/svg" width="21px" height="21px" viewBox="0 0 256 256" class="svelte-10a6av0"><path fill="#FFE812" d="M256 236c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20V20C0 8.954 8.954 0 20 0h216c11.046 0 20 8.954 20 20v216z"></path><path d="M128 36C70.562 36 24 72.713 24 118c0 29.279 19.466 54.97 48.748 69.477-1.593 5.494-10.237 35.344-10.581 37.689 0 0-.207 1.762.934 2.434s2.483.15 2.483.15c3.272-.457 37.943-24.811 43.944-29.04 5.995.849 12.168 1.29 18.472 1.29 57.438 0 104-36.712 104-82 0-45.287-46.562-82-104-82z"></path><path fill="#FFE812" d="M70.5 146.625c-3.309 0-6-2.57-6-5.73V105.25h-9.362c-3.247 0-5.888-2.636-5.888-5.875s2.642-5.875 5.888-5.875h30.724c3.247 0 5.888 2.636 5.888 5.875s-2.642 5.875-5.888 5.875H76.5v35.645c0 3.16-2.691 5.73-6 5.73zM123.112 146.547c-2.502 0-4.416-1.016-4.993-2.65l-2.971-7.778-18.296-.001-2.973 7.783c-.575 1.631-2.488 2.646-4.99 2.646a9.155 9.155 0 0 1-3.814-.828c-1.654-.763-3.244-2.861-1.422-8.52l14.352-37.776c1.011-2.873 4.082-5.833 7.99-5.922 3.919.088 6.99 3.049 8.003 5.928l14.346 37.759c1.826 5.672.236 7.771-1.418 8.532a9.176 9.176 0 0 1-3.814.827c-.001 0 0 0 0 0zm-11.119-21.056L106 108.466l-5.993 17.025h11.986zM138 145.75c-3.171 0-5.75-2.468-5.75-5.5V99.5c0-3.309 2.748-6 6.125-6s6.125 2.691 6.125 6v35.25h12.75c3.171 0 5.75 2.468 5.75 5.5s-2.579 5.5-5.75 5.5H138zM171.334 146.547c-3.309 0-6-2.691-6-6V99.5c0-3.309 2.691-6 6-6s6 2.691 6 6v12.896l16.74-16.74c.861-.861 2.044-1.335 3.328-1.335 1.498 0 3.002.646 4.129 1.772 1.051 1.05 1.678 2.401 1.764 3.804.087 1.415-.384 2.712-1.324 3.653l-13.673 13.671 14.769 19.566a5.951 5.951 0 0 1 1.152 4.445 5.956 5.956 0 0 1-2.328 3.957 5.94 5.94 0 0 1-3.609 1.211 5.953 5.953 0 0 1-4.793-2.385l-14.071-18.644-2.082 2.082v13.091a6.01 6.01 0 0 1-6.002 6.003z"></path></svg>');function Xa(a,e){let t=v(e,"provider",8);var r=Ae(),l=de(r);{var i=s=>{var o=Wa();f(s,o)},c=(s,o)=>{{var g=d=>{var u=Na();f(d,u)},n=(d,u)=>{{var m=b=>{var N=Va();f(b,N)},C=(b,N)=>{{var k=H=>{var D=Ea(),Q=le(D,!0);Q.nodeValue=" ",Z(),ie(D),f(H,D)},j=(H,D)=>{{var Q=R=>{var M=Da(),w=le(M,!0);w.nodeValue=" ",Z(),ie(M),f(R,M)},ee=(R,M)=>{{var w=L=>{var z=Fa();f(L,z)},B=(L,z)=>{{var K=ne=>{var X=Aa();f(ne,X)},G=(ne,X)=>{{var T=p=>{var x=ja();f(p,x)},S=(p,x)=>{{var I=P=>{var O=Ha();f(P,O)},F=(P,O)=>{{var se=V=>{var ce=Oa();f(V,ce)},oe=(V,ce)=>{{var _e=ue=>{var qe=Ga();f(ue,qe)},be=(ue,qe)=>{{var _t=Re=>{var Je=Ua();f(Re,Je)},bt=(Re,Je)=>{{var xt=We=>{var Ke=Za();f(We,Ke)},yt=(We,Ke)=>{{var wt=Ne=>{var Xe=Ya();f(Ne,Xe)},St=(Ne,Xe)=>{{var kt=Ve=>{var Qe=qa();f(Ve,Qe)},Ct=(Ve,Qe)=>{{var $t=Ee=>{var et=Ja();f(Ee,et)},Bt=(Ee,et)=>{{var Pt=tt=>{var Lt=Ka();f(tt,Lt)};W(Ee,tt=>{t()==="kakao"&&tt(Pt)},et)}};W(Ve,Ee=>{t()==="workos"?Ee($t):Ee(Bt,!1)},Qe)}};W(Ne,Ve=>{t()==="twitch"?Ve(kt):Ve(Ct,!1)},Xe)}};W(We,Ne=>{t()==="spotify"?Ne(wt):Ne(St,!1)},Ke)}};W(Re,We=>{t()==="slack"?We(xt):We(yt,!1)},Je)}};W(ue,Re=>{t()==="notion"?Re(_t):Re(bt,!1)},qe)}};W(V,ue=>{t()==="linkedin"?ue(_e):ue(be,!1)},ce)}};W(P,V=>{t()==="keycloak"?V(se):V(oe,!1)},O)}};W(p,P=>{t()==="azure"?P(I):P(F,!1)},x)}};W(ne,p=>{t()==="discord"?p(T):p(S,!1)},X)}};W(L,ne=>{t()==="bitbucket"?ne(K):ne(G,!1)},z)}};W(R,L=>{t()==="gitlab"?L(w):L(B,!1)},M)}};W(H,R=>{t()==="github"?R(Q):R(ee,!1)},D)}};W(b,H=>{t()==="apple"?H(k):H(j,!1)},N)}};W(d,b=>{t()==="twitter"?b(m):b(C,!1)},u)}};W(s,d=>{t()==="facebook"?d(g):d(n,!1)},o)}};W(l,s=>{t()==="google"?s(i):s(c,!1)})}f(a,r)}var Qa=ae("<div><!></div>");function er(a,e){const t=fe(e,["children","$$slots","$$events","$$legacy"]),r=fe(t,["appearance"]);he(e,!1);const l=je({background:"$dividerBackground",display:"block",margin:"16px 0",height:"1px",width:"100%"});let i=v(e,"appearance",24,()=>({}));const c=Te("divider",l(),i());me();var s=Qa();Me(s,g=>({...r,style:(_(i()),$(()=>{var n,d;return(d=(n=i())==null?void 0:n.style)==null?void 0:d.divider})),class:g}),[()=>$(()=>c.join(" "))]);var o=le(s);Le(o,e,"default",{},null),ie(s),f(a,s),ve()}var tr=ae("<!> <!>",1),ar=ae("<!> <!>",1);function rr(a,e){he(e,!1);const t=re();let r=v(e,"supabaseClient",8),l=v(e,"socialLayout",8),i=v(e,"redirectTo",8,void 0),c=v(e,"onlyThirdPartyProviders",8),s=v(e,"i18n",8),o=v(e,"providers",24,()=>[]),g=v(e,"providerScopes",8),n=v(e,"queryParams",8),d=v(e,"appearance",8),u=re(!1);async function m(H){var Q;E(u,!0);const{error:D}=await r().auth.signInWithOAuth({provider:H,options:{redirectTo:i(),scopes:(Q=g())==null?void 0:Q[H],queryParams:n()}});D&&D.message,E(u,!1)}function C(H){return H[0].toUpperCase()+H.slice(1).toLowerCase()}let b=H=>{var D;return fa((D=s().sign_in)==null?void 0:D.social_provider_text,{provider:C(H)})};we(()=>_(l()),()=>{E(t,l()==="vertical")}),Pe(),me();var N=Ae(),k=de(N);{var j=H=>{var D=ar(),Q=de(D);ye(Q,{direction:"vertical",gap:"large",get appearance(){return d()},children:(M,w)=>{const B=xe(()=>h(t)?"vertical":"horizontal"),L=xe(()=>h(t)?"small":"medium");ye(M,{get direction(){return h(B)},get gap(){return h(L)},get appearance(){return d()},children:(z,K)=>{var G=Ae(),ne=de(G);Wt(ne,1,o,Nt,(X,T)=>{const S=xe(()=>(h(T),$(()=>b(h(T)))));He(X,{get"aria-label"(){return h(S)},type:"submit",color:"default",get loading(){return h(u)},get appearance(){return d()},$$events:{click:()=>m(h(T))},children:(p,x)=>{var I=tr(),F=de(I);Xa(F,{get provider(){return h(T)}});var P=A(F,2);{var O=se=>{var oe=q();Y(V=>J(oe,V),[()=>(h(T),$(()=>b(h(T))))],xe),f(se,oe)};W(P,se=>{h(t)&&se(O)})}f(p,I)},$$slots:{default:!0}})}),f(z,G)},$$slots:{default:!0}})},$$slots:{default:!0}});var ee=A(Q,2);{var R=M=>{er(M,{get appearance(){return d()}})};W(ee,M=>{c()||M(R)})}f(H,D)};W(k,H=>{_(o()),$(()=>o().length)&&H(j)})}f(a,N),ve()}var nr=ae("<div><!> <!></div> <!>",1),or=ae("<!> <!> <!> <!>",1),lr=ae('<form id="auth-update-password" method="post" class="svelte-nm5p4o"><!></form>');function ir(a,e){he(e,!1);let t=v(e,"i18n",8),r=v(e,"supabaseClient",8),l=v(e,"authView",12),i=v(e,"appearance",8),c=v(e,"showLinks",8,!1),s=re(""),o=re(""),g=re(""),n=re(!1);async function d(){var N;E(n,!0),E(g,""),E(o,"");const{data:C,error:b}=await r().auth.updateUser({password:h(s)});b?E(g,b.message):E(o,(N=t().update_password)==null?void 0:N.confirmation_text),E(n,!1)}me();var u=lr(),m=le(u);ye(m,{direction:"vertical",gap:"large",get appearance(){return i()},children:(C,b)=>{var N=or(),k=de(N);ye(k,{direction:"vertical",gap:"large",get appearance(){return i()},children:(M,w)=>{var B=nr(),L=de(B),z=le(L);Be(z,{for:"password",get appearance(){return i()},children:(X,T)=>{Z();var S=q();Y(()=>J(S,(_(t()),$(()=>{var p,x;return(x=(p=t())==null?void 0:p.update_password)==null?void 0:x.password_label})))),f(X,S)},$$slots:{default:!0}});var K=A(z,2);const G=xe(()=>(_(t()),$(()=>{var X,T;return(T=(X=t())==null?void 0:X.update_password)==null?void 0:T.password_input_placeholder})));$e(K,{id:"password",type:"password",name:"password",autofocus:!0,get placeholder(){return h(G)},autocomplete:"password",get appearance(){return i()},get value(){return h(s)},set value(X){E(s,X)},$$legacy:!0}),ie(L);var ne=A(L,2);He(ne,{type:"submit",color:"primary",get loading(){return h(n)},get appearance(){return i()},children:(X,T)=>{Z();var S=q();Y(()=>J(S,(h(n),_(t()),$(()=>{var p,x,I,F;return h(n)?(x=(p=t())==null?void 0:p.update_password)==null?void 0:x.loading_button_label:(F=(I=t())==null?void 0:I.update_password)==null?void 0:F.button_label})))),f(X,S)},$$slots:{default:!0}}),f(M,B)},$$slots:{default:!0}});var j=A(k,2);{var H=M=>{Ce(M,{href:"#auth-magic-link",get appearance(){return i()},$$events:{click:w=>{w.preventDefault(),l(U.SIGN_IN)}},children:(w,B)=>{Z();var L=q();Y(()=>J(L,(_(t()),$(()=>{var z,K;return(K=(z=t())==null?void 0:z.sign_in)==null?void 0:K.link_text})))),f(w,L)},$$slots:{default:!0}})};W(j,M=>{c()&&M(H)})}var D=A(j,2);{var Q=M=>{Se(M,{get appearance(){return i()},children:(w,B)=>{Z();var L=q();Y(()=>J(L,h(o))),f(w,L)},$$slots:{default:!0}})};W(D,M=>{h(o)&&M(Q)})}var ee=A(D,2);{var R=M=>{Se(M,{color:"danger",get appearance(){return i()},children:(w,B)=>{Z();var L=q();Y(()=>J(L,h(g))),f(w,L)},$$slots:{default:!0}})};W(ee,M=>{h(g)&&M(R)})}f(C,N)},$$slots:{default:!0}}),ie(u),Ie("submit",u,Ue(d)),f(a,u),ve()}var sr=ae("<div><!> <!></div>"),cr=ae("<div><!> <!></div>"),dr=ae("<!> <div><!> <!></div> <!> <!> <!> <!>",1),ur=ae('<form id="auth-magic-link" method="post" class="svelte-nm5p4o"><!></form>');function pr(a,e){he(e,!1);let t=v(e,"i18n",8),r=v(e,"supabaseClient",8),l=v(e,"authView",12),i=v(e,"otpType",8,"email"),c=v(e,"appearance",8),s=v(e,"showLinks",8,!1),o=v(e,"email",12,""),g=v(e,"phone",12,""),n=v(e,"token",12,""),d=re(""),u=re(""),m=re(!1);async function C(){E(m,!0),E(u,""),E(d,"");let k={email:o(),token:n(),type:i()};["sms","phone_change"].includes(i())&&(k={phone:g(),token:n(),type:i()});const{error:j}=await r().auth.verifyOtp(k);j&&E(u,j.message),E(m,!1)}me();var b=ur(),N=le(b);ye(N,{direction:"vertical",gap:"large",get appearance(){return c()},children:(k,j)=>{var H=dr(),D=de(H);{var Q=S=>{var p=sr(),x=le(p);Be(x,{for:"phone",get appearance(){return c()},children:(P,O)=>{Z();var se=q();Y(()=>J(se,(_(t()),$(()=>{var oe,V;return(V=(oe=t())==null?void 0:oe.verify_otp)==null?void 0:V.phone_input_label})))),f(P,se)},$$slots:{default:!0}});var I=A(x,2);const F=xe(()=>(_(t()),$(()=>{var P,O;return(O=(P=t())==null?void 0:P.verify_otp)==null?void 0:O.phone_input_placeholder})));$e(I,{id:"phone",type:"text",name:"phone",autofocus:!0,get placeholder(){return h(F)},autocomplete:"phone",get appearance(){return c()},get value(){return g()},set value(P){g(P)},$$legacy:!0}),ie(p),f(S,p)},ee=S=>{var p=cr(),x=le(p);Be(x,{for:"email",get appearance(){return c()},children:(P,O)=>{Z();var se=q();Y(()=>J(se,(_(t()),$(()=>{var oe,V;return(V=(oe=t())==null?void 0:oe.verify_otp)==null?void 0:V.email_input_label})))),f(P,se)},$$slots:{default:!0}});var I=A(x,2);const F=xe(()=>(_(t()),$(()=>{var P,O;return(O=(P=t())==null?void 0:P.verify_otp)==null?void 0:O.email_input_placeholder})));$e(I,{id:"email",type:"email",name:"email",autofocus:!0,get placeholder(){return h(F)},autocomplete:"email",get appearance(){return c()},get value(){return o()},set value(P){o(P)},$$legacy:!0}),ie(p),f(S,p)};W(D,S=>{_(i()),$(()=>["sms","phone_change"].includes(i()))?S(Q):S(ee,!1)})}var R=A(D,2),M=le(R);Be(M,{for:"token",get appearance(){return c()},children:(S,p)=>{Z();var x=q();Y(()=>J(x,(_(t()),$(()=>{var I,F;return(F=(I=t())==null?void 0:I.verify_otp)==null?void 0:F.token_input_label})))),f(S,x)},$$slots:{default:!0}});var w=A(M,2);const B=xe(()=>(_(t()),$(()=>{var S,p;return(p=(S=t())==null?void 0:S.verify_otp)==null?void 0:p.token_input_placeholder})));$e(w,{id:"token",type:"text",name:"token",get placeholder(){return h(B)},autocomplete:"token",get appearance(){return c()},get value(){return n()},set value(S){n(S)},$$legacy:!0}),ie(R);var L=A(R,2);He(L,{type:"submit",color:"primary",get loading(){return h(m)},get appearance(){return c()},children:(S,p)=>{Z();var x=q();Y(()=>J(x,(h(m),_(t()),$(()=>{var I,F,P,O;return h(m)?(F=(I=t())==null?void 0:I.verify_otp)==null?void 0:F.loading_button_label:(O=(P=t())==null?void 0:P.verify_otp)==null?void 0:O.button_label})))),f(S,x)},$$slots:{default:!0}});var z=A(L,2);{var K=S=>{Ce(S,{href:"#auth-sign-in",get appearance(){return c()},$$events:{click:p=>{p.preventDefault(),l(U.SIGN_IN)}},children:(p,x)=>{Z();var I=q();Y(()=>J(I,(_(t()),$(()=>{var F,P;return(P=(F=t())==null?void 0:F.sign_in)==null?void 0:P.link_text})))),f(p,I)},$$slots:{default:!0}})};W(z,S=>{s()&&S(K)})}var G=A(z,2);{var ne=S=>{Se(S,{get appearance(){return c()},children:(p,x)=>{Z();var I=q();Y(()=>J(I,h(d))),f(p,I)},$$slots:{default:!0}})};W(G,S=>{h(d)&&S(ne)})}var X=A(G,2);{var T=S=>{Se(S,{color:"danger",get appearance(){return c()},children:(p,x)=>{Z();var I=q();Y(()=>J(I,h(u))),f(p,I)},$$slots:{default:!0}})};W(X,S=>{h(u)&&S(T)})}f(k,H)},$$slots:{default:!0}}),ie(b),Ie("submit",b,Ue(C)),f(a,b),ve()}var gr=ae("<div><!> <!> <!> <!> <!> <!> <!></div>");function Pr(a,e){he(e,!1);const t=re(),r=re(),l=re();let i=v(e,"supabaseClient",8),c=v(e,"socialLayout",8,"vertical"),s=v(e,"providers",24,()=>[]),o=v(e,"providerScopes",8,void 0),g=v(e,"queryParams",8,void 0),n=v(e,"view",12,"sign_in"),d=v(e,"redirectTo",8,void 0),u=v(e,"onlyThirdPartyProviders",8,!1),m=v(e,"magicLink",8,!1),C=v(e,"showLinks",8,!0),b=v(e,"appearance",24,()=>({})),N=v(e,"theme",8,"default"),k=v(e,"localization",24,()=>({})),j=v(e,"otpType",8,"email"),H=v(e,"additionalData",8,void 0);zt(()=>{const{data:p}=i().auth.onAuthStateChange(x=>{x==="PASSWORD_RECOVERY"?n("update_password"):x==="USER_UPDATED"&&n("sign_in")})}),we(()=>_(k()),()=>{E(t,rt(ha,k().variables??{}))}),we(()=>_(b()),()=>{var p,x,I,F;vt({theme:rt(((x=(p=b())==null?void 0:p.theme)==null?void 0:x.default)??{},((F=(I=b())==null?void 0:I.variables)==null?void 0:F.default)??{})})}),we(()=>(_(b()),_(N())),()=>{var p,x,I,F;E(r,da(rt((x=(p=b())==null?void 0:p.theme)==null?void 0:x[N()],((F=(I=b())==null?void 0:I.variables)==null?void 0:F[N()])??{})))}),we(()=>_(n()),()=>{E(l,n()==="sign_in"||n()==="sign_up"||n()==="magic_link")}),Pe(),me();var D=gr(),Q=le(D);{var ee=p=>{rr(p,{get appearance(){return b()},get supabaseClient(){return i()},get providers(){return s()},get providerScopes(){return o()},get queryParams(){return g()},get socialLayout(){return c()},get redirectTo(){return d()},get onlyThirdPartyProviders(){return u()},get i18n(){return h(t)}})};W(Q,p=>{h(l)&&p(ee)})}var R=A(Q,2);{var M=p=>{var x=Ae(),I=de(x);{var F=P=>{gt(P,{get appearance(){return b()},get supabaseClient(){return i()},get redirectTo(){return d()},get magicLink(){return m()},get showLinks(){return C()},get i18n(){return h(t)},get additionalData(){return H()},get authView(){return n()},set authView(O){n(O)},$$legacy:!0})};W(I,P=>{u()||P(F)})}f(p,x)};W(R,p=>{_(n()),_(U),$(()=>n()===U.SIGN_IN)&&p(M)})}var w=A(R,2);{var B=p=>{var x=Ae(),I=de(x);{var F=P=>{gt(P,{get appearance(){return b()},get supabaseClient(){return i()},get redirectTo(){return d()},get magicLink(){return m()},get showLinks(){return C()},get additionalData(){return H()},get i18n(){return h(t)},get authView(){return n()},set authView(O){n(O)},children:(O,se)=>{var oe=Ae(),V=de(oe);Le(V,e,"default",{},null),f(O,oe)},$$slots:{default:!0},$$legacy:!0})};W(I,P=>{u()||P(F)})}f(p,x)};W(w,p=>{_(n()),_(U),$(()=>n()===U.SIGN_UP)&&p(B)})}var L=A(w,2);{var z=p=>{za(p,{get i18n(){return h(t)},get supabaseClient(){return i()},get showLinks(){return C()},get appearance(){return b()},get redirectTo(){return d()},get authView(){return n()},set authView(x){n(x)},$$legacy:!0})};W(L,p=>{_(n()),_(U),$(()=>n()===U.FORGOTTEN_PASSWORD)&&p(z)})}var K=A(L,2);{var G=p=>{Ra(p,{get i18n(){return h(t)},get supabaseClient(){return i()},get appearance(){return b()},get redirectTo(){return d()},get showLinks(){return C()},get authView(){return n()},set authView(x){n(x)},$$legacy:!0})};W(K,p=>{_(n()),_(U),$(()=>n()===U.MAGIC_LINK)&&p(G)})}var ne=A(K,2);{var X=p=>{ir(p,{get i18n(){return h(t)},get supabaseClient(){return i()},get appearance(){return b()},get showLinks(){return C()},get authView(){return n()},set authView(x){n(x)},$$legacy:!0})};W(ne,p=>{_(n()),_(U),$(()=>n()===U.UPDATE_PASSWORD)&&p(X)})}var T=A(ne,2);{var S=p=>{pr(p,{get i18n(){return h(t)},get supabaseClient(){return i()},get appearance(){return b()},get showLinks(){return C()},get otpType(){return j()},get authView(){return n()},set authView(x){n(x)},$$legacy:!0})};W(T,p=>{_(n()),_(U),$(()=>n()===U.VERIFY_OTP)&&p(S)})}ie(D),Y(()=>It(D,1,Mt(N()!=="default"?h(r):""))),f(a,D),ve()}const Lr=[],zr={theme:ua,variables:{default:{colors:{brand:"hsl(var(--primary))",brandAccent:"hsl(var(--primary))",inputText:"hsl(var(--primary))",brandButtonText:"hsl(var(--primary-foreground))",messageText:"hsl(var(--foreground))",dividerBackground:"hsl(var(--foreground))",inputLabelText:"hsl(var(--foreground))",defaultButtonText:"hsl(var(--primary-foreground))",anchorTextColor:"hsl(var(--foreground))"},fontSizes:{baseInputSize:"16px"}}},className:{button:"authBtn"}};export{Pr as A,Lr as o,zr as s};
