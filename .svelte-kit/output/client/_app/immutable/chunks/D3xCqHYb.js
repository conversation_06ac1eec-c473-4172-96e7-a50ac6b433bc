import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as a,e as l,a as i}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as $,s as p}from"./Cmdkv-7M.js";import{I as m}from"./CX_t0Ed_.js";function N(t,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),r=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m9 12 2 2 4-4"}]];m(t,p({name:"circle-check"},()=>n,{get iconNode(){return r},children:(s,u)=>{var o=a(),c=l(o);d(c,e,"default",{},null),i(s,o)},$$slots:{default:!0}}))}function x(t,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),r=[["circle",{cx:"12",cy:"12",r:"10"}]];m(t,p({name:"circle"},()=>n,{get iconNode(){return r},children:(s,u)=>{var o=a(),c=l(o);d(c,e,"default",{},null),i(s,o)},$$slots:{default:!0}}))}function z(t,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["polyline",{points:"7 10 12 15 17 10"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3"}]];m(t,p({name:"download"},()=>n,{get iconNode(){return r},children:(s,u)=>{var o=a(),c=l(o);d(c,e,"default",{},null),i(s,o)},$$slots:{default:!0}}))}export{N as C,z as D,x as a};
