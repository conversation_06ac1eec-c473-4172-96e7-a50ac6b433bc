import"../chunks/CWj6FrbW.js";import{p as u,g as v,u as y,f as p,e as _,a as l,s as f}from"../chunks/wnqW1tdD.js";import{s as S}from"../chunks/yk44OJLy.js";import{i as x}from"../chunks/BjRbZGyQ.js";import{t as E}from"../chunks/sBNKiCwy.js";import{a as C,s as b}from"../chunks/D5ITLM2v.js";import{n as B}from"../chunks/D0xeg8nZ.js";import{e as I}from"../chunks/BCJ65Txv.js";import{s as T}from"../chunks/CiB29Aqe.js";import{s as c}from"../chunks/DyGaIYLH.js";import{o as k}from"../chunks/CfBaWyh2.js";function w(r,t){u(t,!0),k(()=>{const n=document.getElementById("dynamic-theme-styles");n&&n.remove();const e=document.createElement("style");return e.id="dynamic-theme-styles",e.textContent=t.themeCSS,document.head.appendChild(e),()=>{const a=document.getElementById("dynamic-theme-styles");a&&a.remove()}}),v()}var z=p('<div class="fixed w-full top-0 right-0 left-0 h-1 z-50 bg-primary"></div>'),M=p("<!> <!> <!>",1);function L(r,t){u(t,!0);const[n,e]=C(),a=()=>b(B,"$navigating",n);c(t.data.environment),y(()=>{c(t.data.environment)});var i=M(),s=_(i);w(s,{get themeCSS(){return t.data.themeCSS}});var m=f(s,2);{var g=o=>{var d=z();E(1,d,()=>T,()=>({delay:100,duration:12e3,axis:"x",easing:I})),l(o,d)};x(m,o=>{a()&&o(g)})}var h=f(m,2);S(h,()=>t.children),l(r,i),v(),e()}export{L as component};
