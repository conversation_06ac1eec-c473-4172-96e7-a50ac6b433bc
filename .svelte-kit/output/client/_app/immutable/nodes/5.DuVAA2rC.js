import"../chunks/CWj6FrbW.js";import{b6 as Ve,ar as ro,as as io,p as ve,l as nt,k as Ue,m as Xe,aM as H,j as I,h as Qe,b as Pt,e as ht,a as K,g as be,f as ut,c as wt,r as xt,Y as ce,d as Wn,i as Bn,t as nn,s as zt,o as on,q as so}from"../chunks/wnqW1tdD.js";import{e as At,s as ao}from"../chunks/CDPCzm7q.js";import{s as lo}from"../chunks/yk44OJLy.js";import{i as Dt}from"../chunks/BjRbZGyQ.js";import{c as se}from"../chunks/ojdN50pv.js";import{a as ue,s as xn}from"../chunks/rh_XW2Tv.js";import{s as Ae,a as We}from"../chunks/D5ITLM2v.js";import"../chunks/DpzY6icx.js";import"../chunks/Cvx8ZW61.js";import{l as de,i as A,w as Ot,n as fe,u as Nn,e as jt,m as Et,j as Ht,a as V,F as On,k as kt,p as co,s as De,f as uo,S as ln,q as Gt,r as _n,d as ae,g as rn,o as Vn}from"../chunks/D477uqMC.js";import{s as It}from"../chunks/BDqVm3Gq.js";import{i as Ce}from"../chunks/BxG_UISn.js";import{l as Xt,p as k,s as Kn}from"../chunks/Cmdkv-7M.js";import{d as Oe,w as yt,g as fo}from"../chunks/BvpDAKCq.js";import{t as Fe,g as An,o as je,r as go,b as mo}from"../chunks/CuJHdp1M.js";import{s as he,w as ho,a as zn,c as po,u as vo,g as Tn,h as bo,r as yo,S as wo,M as xo}from"../chunks/CLUknB3d.js";import{c as Oo,a as fn,d as _o}from"../chunks/DPaidA8O.js";import{a as ge}from"../chunks/Cet13GU7.js";import{b as me}from"../chunks/D5U2DSnR.js";import{g as Ao}from"../chunks/DyGaIYLH.js";import{p as To}from"../chunks/D0xeg8nZ.js";import{W as Co}from"../chunks/QNe64B_9.js";import{B as Eo}from"../chunks/BFIy0mTe.js";import{b as Jt}from"../chunks/B2uh23P-.js";import{t as Ie}from"../chunks/sBNKiCwy.js";import{c as Ye,f as ko}from"../chunks/BMdVdstb.js";import{e as Po}from"../chunks/CM6X1Z2I.js";function So(e){e.setAttribute("data-highlighted","")}function xe(e){e.removeAttribute("data-highlighted")}function Do(e,t=500){let o=null;return function(...n){const r=()=>{o=null,e(...n)};o&&clearTimeout(o),o=setTimeout(r,t)}}function Cn(e){const{open:t,forceVisible:o,activeTrigger:n}=e;return Oe([t,o,n],([r,s,i])=>(r||s)&&i!==null)}function bt(e){de&&he(1).then(()=>{const t=document.activeElement;!A(t)||t===e||(t.tabIndex=-1,e&&(e.tabIndex=0,e.focus()))})}function Hn(){return Array.from(document.querySelectorAll('a[href]:not([tabindex="-1"]), button:not([disabled]):not([tabindex="-1"]), input:not([disabled]):not([tabindex="-1"]), select:not([disabled]):not([tabindex="-1"]), textarea:not([disabled]):not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])'))}function Io(e){const t=Hn(),n=t.indexOf(e)+1,r=t[n];return n<t.length&&A(r)?r:null}function Ro(e){const t=Hn(),n=t.indexOf(e)-1,r=t[n];return n>=0&&A(r)?r:null}const Mo=new Set(["Shift","Control","Alt","Meta","CapsLock","NumLock"]),Fo={onMatch:bt,getCurrentItem:()=>document.activeElement};function Lo(e={}){const t={...Fo,...e},o=Ot(yt([])),n=Do(()=>{o.update(()=>[])});return{typed:o,resetTyped:n,handleTypeaheadSearch:(s,i)=>{if(Mo.has(s))return;const a=t.getCurrentItem(),l=fo(o);if(!Array.isArray(l))return;l.push(s.toLowerCase()),o.set(l);const u=i.filter(f=>!(f.getAttribute("disabled")==="true"||f.getAttribute("aria-disabled")==="true"||f.hasAttribute("data-disabled"))),g=l.length>1&&l.every(f=>f===l[0])?l[0]:l.join(""),b=A(a)?u.indexOf(a):-1;let m=ho(u,Math.max(b,0));g.length===1&&(m=m.filter(f=>f!==a));const w=m.find(f=>(f==null?void 0:f.innerText)&&f.innerText.toLowerCase().startsWith(g.toLowerCase()));A(w)&&w!==a&&t.onMatch(w),n()}}}const ne=Math.min,Rt=Math.max,qe=Math.round,Ke=Math.floor,Ut=e=>({x:e,y:e}),Wo={left:"right",right:"left",bottom:"top",top:"bottom"},Bo={start:"end",end:"start"};function cn(e,t,o){return Rt(e,ne(t,o))}function Ee(e,t){return typeof e=="function"?e(t):e}function oe(e){return e.split("-")[0]}function ke(e){return e.split("-")[1]}function jn(e){return e==="x"?"y":"x"}function gn(e){return e==="y"?"height":"width"}const No=new Set(["top","bottom"]);function Qt(e){return No.has(oe(e))?"y":"x"}function mn(e){return jn(Qt(e))}function Vo(e,t,o){o===void 0&&(o=!1);const n=ke(e),r=mn(e),s=gn(r);let i=r==="x"?n===(o?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Ge(i)),[i,Ge(i)]}function Ko(e){const t=Ge(e);return[un(e),t,un(t)]}function un(e){return e.replace(/start|end/g,t=>Bo[t])}const En=["left","right"],kn=["right","left"],zo=["top","bottom"],Ho=["bottom","top"];function jo(e,t,o){switch(e){case"top":case"bottom":return o?t?kn:En:t?En:kn;case"left":case"right":return t?zo:Ho;default:return[]}}function Uo(e,t,o,n){const r=ke(e);let s=jo(oe(e),o==="start",n);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(un)))),s}function Ge(e){return e.replace(/left|right|bottom|top/g,t=>Wo[t])}function Xo(e){return{top:0,right:0,bottom:0,left:0,...e}}function Un(e){return typeof e!="number"?Xo(e):{top:e,right:e,bottom:e,left:e}}function Je(e){const{x:t,y:o,width:n,height:r}=e;return{width:n,height:r,top:o,left:t,right:t+n,bottom:o+r,x:t,y:o}}function Pn(e,t,o){let{reference:n,floating:r}=e;const s=Qt(t),i=mn(t),a=gn(i),l=oe(t),u=s==="y",d=n.x+n.width/2-r.width/2,g=n.y+n.height/2-r.height/2,b=n[a]/2-r[a]/2;let m;switch(l){case"top":m={x:d,y:n.y-r.height};break;case"bottom":m={x:d,y:n.y+n.height};break;case"right":m={x:n.x+n.width,y:g};break;case"left":m={x:n.x-r.width,y:g};break;default:m={x:n.x,y:n.y}}switch(ke(t)){case"start":m[i]-=b*(o&&u?-1:1);break;case"end":m[i]+=b*(o&&u?-1:1);break}return m}const Yo=async(e,t,o)=>{const{placement:n="bottom",strategy:r="absolute",middleware:s=[],platform:i}=o,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:d,y:g}=Pn(u,n,l),b=n,m={},y=0;for(let w=0;w<a.length;w++){const{name:f,fn:x}=a[w],{x:P,y:O,data:T,reset:_}=await x({x:d,y:g,initialPlacement:n,placement:b,strategy:r,middlewareData:m,rects:u,platform:i,elements:{reference:e,floating:t}});d=P??d,g=O??g,m={...m,[f]:{...m[f],...T}},_&&y<=50&&(y++,typeof _=="object"&&(_.placement&&(b=_.placement),_.rects&&(u=_.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):_.rects),{x:d,y:g}=Pn(u,b,l)),w=-1)}return{x:d,y:g,placement:b,strategy:r,middlewareData:m}};async function hn(e,t){var o;t===void 0&&(t={});const{x:n,y:r,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:g="floating",altBoundary:b=!1,padding:m=0}=Ee(t,e),y=Un(m),f=a[b?g==="floating"?"reference":"floating":g],x=Je(await s.getClippingRect({element:(o=await(s.isElement==null?void 0:s.isElement(f)))==null||o?f:f.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),P=g==="floating"?{x:n,y:r,width:i.floating.width,height:i.floating.height}:i.reference,O=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),T=await(s.isElement==null?void 0:s.isElement(O))?await(s.getScale==null?void 0:s.getScale(O))||{x:1,y:1}:{x:1,y:1},_=Je(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:P,offsetParent:O,strategy:l}):P);return{top:(x.top-_.top+y.top)/T.y,bottom:(_.bottom-x.bottom+y.bottom)/T.y,left:(x.left-_.left+y.left)/T.x,right:(_.right-x.right+y.right)/T.x}}const qo=e=>({name:"arrow",options:e,async fn(t){const{x:o,y:n,placement:r,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=Ee(e,t)||{};if(u==null)return{};const g=Un(d),b={x:o,y:n},m=mn(r),y=gn(m),w=await i.getDimensions(u),f=m==="y",x=f?"top":"left",P=f?"bottom":"right",O=f?"clientHeight":"clientWidth",T=s.reference[y]+s.reference[m]-b[m]-s.floating[y],_=b[m]-s.reference[m],F=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let S=F?F[O]:0;(!S||!await(i.isElement==null?void 0:i.isElement(F)))&&(S=a.floating[O]||s.floating[y]);const z=T/2-_/2,j=S/2-w[y]/2-1,tt=ne(g[x],j),Z=ne(g[P],j),L=tt,it=S-w[y]-Z,ot=S/2-w[y]/2+z,X=cn(L,ot,it),Y=!l.arrow&&ke(r)!=null&&ot!==X&&s.reference[y]/2-(ot<L?tt:Z)-w[y]/2<0,rt=Y?ot<L?ot-L:ot-it:0;return{[m]:b[m]+rt,data:{[m]:X,centerOffset:ot-X-rt,...Y&&{alignmentOffset:rt}},reset:Y}}}),Go=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var o,n;const{placement:r,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:g=!0,fallbackPlacements:b,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:w=!0,...f}=Ee(e,t);if((o=s.arrow)!=null&&o.alignmentOffset)return{};const x=oe(r),P=Qt(a),O=oe(a)===a,T=await(l.isRTL==null?void 0:l.isRTL(u.floating)),_=b||(O||!w?[Ge(a)]:Ko(a)),F=y!=="none";!b&&F&&_.push(...Uo(a,w,y,T));const S=[a,..._],z=await hn(t,f),j=[];let tt=((n=s.flip)==null?void 0:n.overflows)||[];if(d&&j.push(z[x]),g){const ot=Vo(r,i,T);j.push(z[ot[0]],z[ot[1]])}if(tt=[...tt,{placement:r,overflows:j}],!j.every(ot=>ot<=0)){var Z,L;const ot=(((Z=s.flip)==null?void 0:Z.index)||0)+1,X=S[ot];if(X&&(!(g==="alignment"?P!==Qt(X):!1)||tt.every(pt=>pt.overflows[0]>0&&Qt(pt.placement)===P)))return{data:{index:ot,overflows:tt},reset:{placement:X}};let Y=(L=tt.filter(rt=>rt.overflows[0]<=0).sort((rt,pt)=>rt.overflows[1]-pt.overflows[1])[0])==null?void 0:L.placement;if(!Y)switch(m){case"bestFit":{var it;const rt=(it=tt.filter(pt=>{if(F){const vt=Qt(pt.placement);return vt===P||vt==="y"}return!0}).map(pt=>[pt.placement,pt.overflows.filter(vt=>vt>0).reduce((vt,Zt)=>vt+Zt,0)]).sort((pt,vt)=>pt[1]-vt[1])[0])==null?void 0:it[0];rt&&(Y=rt);break}case"initialPlacement":Y=a;break}if(r!==Y)return{reset:{placement:Y}}}return{}}}},Jo=new Set(["left","top"]);async function Qo(e,t){const{placement:o,platform:n,elements:r}=e,s=await(n.isRTL==null?void 0:n.isRTL(r.floating)),i=oe(o),a=ke(o),l=Qt(o)==="y",u=Jo.has(i)?-1:1,d=s&&l?-1:1,g=Ee(t,e);let{mainAxis:b,crossAxis:m,alignmentAxis:y}=typeof g=="number"?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return a&&typeof y=="number"&&(m=a==="end"?y*-1:y),l?{x:m*d,y:b*u}:{x:b*u,y:m*d}}const Zo=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var o,n;const{x:r,y:s,placement:i,middlewareData:a}=t,l=await Qo(t,e);return i===((o=a.offset)==null?void 0:o.placement)&&(n=a.arrow)!=null&&n.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},$o=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:n,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:f=>{let{x,y:P}=f;return{x,y:P}}},...l}=Ee(e,t),u={x:o,y:n},d=await hn(t,l),g=Qt(oe(r)),b=jn(g);let m=u[b],y=u[g];if(s){const f=b==="y"?"top":"left",x=b==="y"?"bottom":"right",P=m+d[f],O=m-d[x];m=cn(P,m,O)}if(i){const f=g==="y"?"top":"left",x=g==="y"?"bottom":"right",P=y+d[f],O=y-d[x];y=cn(P,y,O)}const w=a.fn({...t,[b]:m,[g]:y});return{...w,data:{x:w.x-o,y:w.y-n,enabled:{[b]:s,[g]:i}}}}}},tr=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var o,n;const{placement:r,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=Ee(e,t),d=await hn(t,u),g=oe(r),b=ke(r),m=Qt(r)==="y",{width:y,height:w}=s.floating;let f,x;g==="top"||g==="bottom"?(f=g,x=b===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=g,f=b==="end"?"top":"bottom");const P=w-d.top-d.bottom,O=y-d.left-d.right,T=ne(w-d[f],P),_=ne(y-d[x],O),F=!t.middlewareData.shift;let S=T,z=_;if((o=t.middlewareData.shift)!=null&&o.enabled.x&&(z=O),(n=t.middlewareData.shift)!=null&&n.enabled.y&&(S=P),F&&!b){const tt=Rt(d.left,0),Z=Rt(d.right,0),L=Rt(d.top,0),it=Rt(d.bottom,0);m?z=y-2*(tt!==0||Z!==0?tt+Z:Rt(d.left,d.right)):S=w-2*(L!==0||it!==0?L+it:Rt(d.top,d.bottom))}await l({...t,availableWidth:z,availableHeight:S});const j=await i.getDimensions(a.floating);return y!==j.width||w!==j.height?{reset:{rects:!0}}:{}}}};function Ze(){return typeof window<"u"}function Pe(e){return Xn(e)?(e.nodeName||"").toLowerCase():"#document"}function Mt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function qt(e){var t;return(t=(Xn(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Xn(e){return Ze()?e instanceof Node||e instanceof Mt(e).Node:!1}function Wt(e){return Ze()?e instanceof Element||e instanceof Mt(e).Element:!1}function Yt(e){return Ze()?e instanceof HTMLElement||e instanceof Mt(e).HTMLElement:!1}function Sn(e){return!Ze()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Mt(e).ShadowRoot}const er=new Set(["inline","contents"]);function Be(e){const{overflow:t,overflowX:o,overflowY:n,display:r}=Bt(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+o)&&!er.has(r)}const nr=new Set(["table","td","th"]);function or(e){return nr.has(Pe(e))}const rr=[":popover-open",":modal"];function $e(e){return rr.some(t=>{try{return e.matches(t)}catch{return!1}})}const ir=["transform","translate","scale","rotate","perspective"],sr=["transform","translate","scale","rotate","perspective","filter"],ar=["paint","layout","strict","content"];function pn(e){const t=vn(),o=Wt(e)?Bt(e):e;return ir.some(n=>o[n]?o[n]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!t&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!t&&(o.filter?o.filter!=="none":!1)||sr.some(n=>(o.willChange||"").includes(n))||ar.some(n=>(o.contain||"").includes(n))}function lr(e){let t=re(e);for(;Yt(t)&&!Te(t);){if(pn(t))return t;if($e(t))return null;t=re(t)}return null}function vn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const cr=new Set(["html","body","#document"]);function Te(e){return cr.has(Pe(e))}function Bt(e){return Mt(e).getComputedStyle(e)}function tn(e){return Wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function re(e){if(Pe(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Sn(e)&&e.host||qt(e);return Sn(t)?t.host:t}function Yn(e){const t=re(e);return Te(t)?e.ownerDocument?e.ownerDocument.body:e.body:Yt(t)&&Be(t)?t:Yn(t)}function Le(e,t,o){var n;t===void 0&&(t=[]),o===void 0&&(o=!0);const r=Yn(e),s=r===((n=e.ownerDocument)==null?void 0:n.body),i=Mt(r);if(s){const a=dn(i);return t.concat(i,i.visualViewport||[],Be(r)?r:[],a&&o?Le(a):[])}return t.concat(r,Le(r,[],o))}function dn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function qn(e){const t=Bt(e);let o=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const r=Yt(e),s=r?e.offsetWidth:o,i=r?e.offsetHeight:n,a=qe(o)!==s||qe(n)!==i;return a&&(o=s,n=i),{width:o,height:n,$:a}}function bn(e){return Wt(e)?e:e.contextElement}function _e(e){const t=bn(e);if(!Yt(t))return Ut(1);const o=t.getBoundingClientRect(),{width:n,height:r,$:s}=qn(t);let i=(s?qe(o.width):o.width)/n,a=(s?qe(o.height):o.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const ur=Ut(0);function Gn(e){const t=Mt(e);return!vn()||!t.visualViewport?ur:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function dr(e,t,o){return t===void 0&&(t=!1),!o||t&&o!==Mt(e)?!1:t}function pe(e,t,o,n){t===void 0&&(t=!1),o===void 0&&(o=!1);const r=e.getBoundingClientRect(),s=bn(e);let i=Ut(1);t&&(n?Wt(n)&&(i=_e(n)):i=_e(e));const a=dr(s,o,n)?Gn(s):Ut(0);let l=(r.left+a.x)/i.x,u=(r.top+a.y)/i.y,d=r.width/i.x,g=r.height/i.y;if(s){const b=Mt(s),m=n&&Wt(n)?Mt(n):n;let y=b,w=dn(y);for(;w&&n&&m!==y;){const f=_e(w),x=w.getBoundingClientRect(),P=Bt(w),O=x.left+(w.clientLeft+parseFloat(P.paddingLeft))*f.x,T=x.top+(w.clientTop+parseFloat(P.paddingTop))*f.y;l*=f.x,u*=f.y,d*=f.x,g*=f.y,l+=O,u+=T,y=Mt(w),w=dn(y)}}return Je({width:d,height:g,x:l,y:u})}function yn(e,t){const o=tn(e).scrollLeft;return t?t.left+o:pe(qt(e)).left+o}function Jn(e,t,o){o===void 0&&(o=!1);const n=e.getBoundingClientRect(),r=n.left+t.scrollLeft-(o?0:yn(e,n)),s=n.top+t.scrollTop;return{x:r,y:s}}function fr(e){let{elements:t,rect:o,offsetParent:n,strategy:r}=e;const s=r==="fixed",i=qt(n),a=t?$e(t.floating):!1;if(n===i||a&&s)return o;let l={scrollLeft:0,scrollTop:0},u=Ut(1);const d=Ut(0),g=Yt(n);if((g||!g&&!s)&&((Pe(n)!=="body"||Be(i))&&(l=tn(n)),Yt(n))){const m=pe(n);u=_e(n),d.x=m.x+n.clientLeft,d.y=m.y+n.clientTop}const b=i&&!g&&!s?Jn(i,l,!0):Ut(0);return{width:o.width*u.x,height:o.height*u.y,x:o.x*u.x-l.scrollLeft*u.x+d.x+b.x,y:o.y*u.y-l.scrollTop*u.y+d.y+b.y}}function gr(e){return Array.from(e.getClientRects())}function mr(e){const t=qt(e),o=tn(e),n=e.ownerDocument.body,r=Rt(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),s=Rt(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let i=-o.scrollLeft+yn(e);const a=-o.scrollTop;return Bt(n).direction==="rtl"&&(i+=Rt(t.clientWidth,n.clientWidth)-r),{width:r,height:s,x:i,y:a}}function hr(e,t){const o=Mt(e),n=qt(e),r=o.visualViewport;let s=n.clientWidth,i=n.clientHeight,a=0,l=0;if(r){s=r.width,i=r.height;const u=vn();(!u||u&&t==="fixed")&&(a=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:a,y:l}}const pr=new Set(["absolute","fixed"]);function vr(e,t){const o=pe(e,!0,t==="fixed"),n=o.top+e.clientTop,r=o.left+e.clientLeft,s=Yt(e)?_e(e):Ut(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=r*s.x,u=n*s.y;return{width:i,height:a,x:l,y:u}}function Dn(e,t,o){let n;if(t==="viewport")n=hr(e,o);else if(t==="document")n=mr(qt(e));else if(Wt(t))n=vr(t,o);else{const r=Gn(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return Je(n)}function Qn(e,t){const o=re(e);return o===t||!Wt(o)||Te(o)?!1:Bt(o).position==="fixed"||Qn(o,t)}function br(e,t){const o=t.get(e);if(o)return o;let n=Le(e,[],!1).filter(a=>Wt(a)&&Pe(a)!=="body"),r=null;const s=Bt(e).position==="fixed";let i=s?re(e):e;for(;Wt(i)&&!Te(i);){const a=Bt(i),l=pn(i);!l&&a.position==="fixed"&&(r=null),(s?!l&&!r:!l&&a.position==="static"&&!!r&&pr.has(r.position)||Be(i)&&!l&&Qn(e,i))?n=n.filter(d=>d!==i):r=a,i=re(i)}return t.set(e,n),n}function yr(e){let{element:t,boundary:o,rootBoundary:n,strategy:r}=e;const i=[...o==="clippingAncestors"?$e(t)?[]:br(t,this._c):[].concat(o),n],a=i[0],l=i.reduce((u,d)=>{const g=Dn(t,d,r);return u.top=Rt(g.top,u.top),u.right=ne(g.right,u.right),u.bottom=ne(g.bottom,u.bottom),u.left=Rt(g.left,u.left),u},Dn(t,a,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function wr(e){const{width:t,height:o}=qn(e);return{width:t,height:o}}function xr(e,t,o){const n=Yt(t),r=qt(t),s=o==="fixed",i=pe(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=Ut(0);function u(){l.x=yn(r)}if(n||!n&&!s)if((Pe(t)!=="body"||Be(r))&&(a=tn(t)),n){const m=pe(t,!0,s,t);l.x=m.x+t.clientLeft,l.y=m.y+t.clientTop}else r&&u();s&&!n&&r&&u();const d=r&&!n&&!s?Jn(r,a):Ut(0),g=i.left+a.scrollLeft-l.x-d.x,b=i.top+a.scrollTop-l.y-d.y;return{x:g,y:b,width:i.width,height:i.height}}function sn(e){return Bt(e).position==="static"}function In(e,t){if(!Yt(e)||Bt(e).position==="fixed")return null;if(t)return t(e);let o=e.offsetParent;return qt(e)===o&&(o=o.ownerDocument.body),o}function Zn(e,t){const o=Mt(e);if($e(e))return o;if(!Yt(e)){let r=re(e);for(;r&&!Te(r);){if(Wt(r)&&!sn(r))return r;r=re(r)}return o}let n=In(e,t);for(;n&&or(n)&&sn(n);)n=In(n,t);return n&&Te(n)&&sn(n)&&!pn(n)?o:n||lr(e)||o}const Or=async function(e){const t=this.getOffsetParent||Zn,o=this.getDimensions,n=await o(e.floating);return{reference:xr(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function _r(e){return Bt(e).direction==="rtl"}const Ar={convertOffsetParentRelativeRectToViewportRelativeRect:fr,getDocumentElement:qt,getClippingRect:yr,getOffsetParent:Zn,getElementRects:Or,getClientRects:gr,getDimensions:wr,getScale:_e,isElement:Wt,isRTL:_r};function $n(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Tr(e,t){let o=null,n;const r=qt(e);function s(){var a;clearTimeout(n),(a=o)==null||a.disconnect(),o=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:d,top:g,width:b,height:m}=u;if(a||t(),!b||!m)return;const y=Ke(g),w=Ke(r.clientWidth-(d+b)),f=Ke(r.clientHeight-(g+m)),x=Ke(d),O={rootMargin:-y+"px "+-w+"px "+-f+"px "+-x+"px",threshold:Rt(0,ne(1,l))||1};let T=!0;function _(F){const S=F[0].intersectionRatio;if(S!==l){if(!T)return i();S?i(!1,S):n=setTimeout(()=>{i(!1,1e-7)},1e3)}S===1&&!$n(u,e.getBoundingClientRect())&&i(),T=!1}try{o=new IntersectionObserver(_,{...O,root:r.ownerDocument})}catch{o=new IntersectionObserver(_,O)}o.observe(e)}return i(!0),s}function Cr(e,t,o,n){n===void 0&&(n={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,u=bn(e),d=r||s?[...u?Le(u):[],...Le(t)]:[];d.forEach(x=>{r&&x.addEventListener("scroll",o,{passive:!0}),s&&x.addEventListener("resize",o)});const g=u&&a?Tr(u,o):null;let b=-1,m=null;i&&(m=new ResizeObserver(x=>{let[P]=x;P&&P.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(b),b=requestAnimationFrame(()=>{var O;(O=m)==null||O.observe(t)})),o()}),u&&!l&&m.observe(u),m.observe(t));let y,w=l?pe(e):null;l&&f();function f(){const x=pe(e);w&&!$n(w,x)&&o(),w=x,y=requestAnimationFrame(f)}return o(),()=>{var x;d.forEach(P=>{r&&P.removeEventListener("scroll",o),s&&P.removeEventListener("resize",o)}),g==null||g(),(x=m)==null||x.disconnect(),m=null,l&&cancelAnimationFrame(y)}}const Er=Zo,kr=$o,Pr=Go,Sr=tr,Dr=qo,Ir=(e,t,o)=>{const n=new Map,r={platform:Ar,...o},s={...r.platform,_c:n};return Yo(e,t,{...r,platform:s})},Rr={strategy:"absolute",placement:"top",gutter:5,flip:!0,sameWidth:!1,overflowPadding:8},Mr={bottom:"rotate(45deg)",left:"rotate(135deg)",top:"rotate(225deg)",right:"rotate(315deg)"};function Fr(e,t,o={}){if(!t||!e||o===null)return{destroy:fe};const n={...Rr,...o},r=t.querySelector("[data-arrow=true]"),s=[];n.flip&&s.push(Pr({boundary:n.boundary,padding:n.overflowPadding}));const i=A(r)?r.offsetHeight/2:0;if(n.gutter||n.offset){const l=n.gutter?{mainAxis:n.gutter}:n.offset;(l==null?void 0:l.mainAxis)!=null&&(l.mainAxis+=i),s.push(Er(l))}s.push(kr({boundary:n.boundary,crossAxis:n.overlap,padding:n.overflowPadding})),r&&s.push(Dr({element:r,padding:8})),s.push(Sr({padding:n.overflowPadding,apply({rects:l,availableHeight:u,availableWidth:d}){n.sameWidth&&Object.assign(t.style,{width:`${Math.round(l.reference.width)}px`,minWidth:"unset"}),n.fitViewport&&Object.assign(t.style,{maxWidth:`${d}px`,maxHeight:`${u}px`})}}));function a(){if(!e||!t||A(e)&&!e.ownerDocument.documentElement.contains(e))return;const{placement:l,strategy:u}=n;Ir(e,t,{placement:l,middleware:s,strategy:u}).then(d=>{const g=Math.round(d.x),b=Math.round(d.y),[m,y]=Lr(d.placement);if(t.setAttribute("data-side",m),t.setAttribute("data-align",y),Object.assign(t.style,{position:n.strategy,top:`${b}px`,left:`${g}px`}),A(r)&&d.middlewareData.arrow){const{x:w,y:f}=d.middlewareData.arrow,x=d.placement.split("-")[0];r.setAttribute("data-side",x),Object.assign(r.style,{position:"absolute",left:w!=null?`${w}px`:"",top:f!=null?`${f}px`:"",[x]:`calc(100% - ${i}px)`,transform:Mr[x],backgroundColor:"inherit",zIndex:"inherit"})}return d})}return Object.assign(t.style,{position:n.strategy}),{destroy:Cr(e,t,a)}}function Lr(e){const[t,o="center"]=e.split("-");return[t,o]}const Wr={floating:{},focusTrap:{},modal:{},escapeKeydown:{},portal:"body"},Rn=(e,t)=>{e.dataset.escapee="";const{anchorElement:o,open:n,options:r}=t;if(!o||!n||!r)return{destroy:fe};const s={...Wr,...r},i=[];if(s.portal!==null&&i.push(zn(e,s.portal).destroy),i.push(Fr(o,e,s.floating).destroy),s.focusTrap!==null){const{useFocusTrap:l}=po({immediate:!0,escapeDeactivates:!1,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:e,...s.focusTrap});i.push(l(e).destroy)}s.modal!==null&&i.push(vo(e,{onClose:()=>{A(o)&&(n.set(!1),o.focus())},shouldCloseOnInteractOutside:l=>!(l.defaultPrevented||A(o)&&o.contains(l.target)),...s.modal}).destroy),s.escapeKeydown!==null&&i.push(Nn(e,{enabled:n,handler:()=>{n.set(!1)},...s.escapeKeydown}).destroy);const a=jt(...i);return{destroy(){a()}}},Br={ltr:[...ln,kt.ARROW_RIGHT]},Nr={ltr:[kt.ARROW_LEFT]},Mn=["menu","trigger"],Vr={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function Kr(e){const{name:t,selector:o}=uo(e.selector),{preventScroll:n,arrowSize:r,positioning:s,closeOnEscape:i,closeOnOutsideClick:a,portal:l,forceVisible:u,typeahead:d,loop:g,closeFocus:b,disableFocusFirstItem:m,closeOnItemClick:y,onOutsideClick:w}=e.rootOptions,f=e.rootOpen,x=e.rootActiveTrigger,P=e.nextFocusable,O=e.prevFocusable,T=Ot.writable(!1),_=Ot(yt(0)),F=Ot(yt(null)),S=Ot(yt("right")),z=Ot(yt(null)),j=Ot(Oe([S,F],([c,p])=>h=>c===(p==null?void 0:p.side)&&zr(h,p==null?void 0:p.area))),{typed:tt,handleTypeaheadSearch:Z}=Lo(),L=Fe({...An(Mn),...e.ids}),it=Cn({open:f,forceVisible:u,activeTrigger:x}),ot=Et(t(),{stores:[it,l,L.menu,L.trigger],returned:([c,p,h,v])=>({role:"menu",hidden:c?void 0:!0,style:De({display:c?void 0:"none"}),id:h,"aria-labelledby":v,"data-state":c?"open":"closed","data-portal":co(p),tabindex:-1}),action:c=>{let p=fe;const h=Ht([it,x,s,a,l,i],([E,J,mt,Q,R,B])=>{p(),!(!E||!J)&&Ve().then(()=>{p(),Me(c,o),p=Rn(c,{anchorElement:J,open:f,options:{floating:mt,modal:{closeOnInteractOutside:Q,shouldCloseOnInteractOutside:D=>{var M;return(M=w.get())==null||M(D),!(D.defaultPrevented||A(J)&&J.contains(D.target))},onClose:()=>{f.set(!1),J.focus()},open:E},portal:Tn(c,R),escapeKeydown:B?void 0:null}}).destroy})}),v=jt(V(c,"keydown",E=>{const J=E.target,mt=E.currentTarget;if(!A(J)||!A(mt)||!(J.closest('[role="menu"]')===mt))return;if(On.includes(E.key)&&Ln(E,g.get()??!1),E.key===kt.TAB){E.preventDefault(),f.set(!1),Fn(E,P,O);return}const R=E.key.length===1;!(E.ctrlKey||E.altKey||E.metaKey)&&R&&d.get()===!0&&Z(E.key,le(mt))}));return{destroy(){h(),v(),p()}}}}),X=Et(t("trigger"),{stores:[f,L.menu,L.trigger],returned:([c,p,h])=>({"aria-controls":p,"aria-expanded":c,"data-state":c?"open":"closed",id:h,tabindex:0}),action:c=>(ze(c),x.update(h=>h||c),{destroy:jt(V(c,"click",h=>{const v=f.get(),E=h.currentTarget;A(E)&&(Tt(E),v||h.preventDefault())}),V(c,"keydown",h=>{const v=h.currentTarget;if(!A(v)||!(ln.includes(h.key)||h.key===kt.ARROW_DOWN))return;h.preventDefault(),Tt(v);const E=v.getAttribute("aria-controls");if(!E)return;const J=document.getElementById(E);if(!J)return;const mt=le(J);mt.length&&bt(mt[0])}))})}),Y=Et(t("arrow"),{stores:r,returned:c=>({"data-arrow":!0,style:De({position:"absolute",width:`var(--arrow-size, ${c}px)`,height:`var(--arrow-size, ${c}px)`})})}),rt=Et(t("overlay"),{stores:[it],returned:([c])=>({hidden:c?void 0:!0,tabindex:-1,style:De({display:c?void 0:"none"}),"aria-hidden":"true","data-state":Ur(c)}),action:c=>{let p=fe;if(i.get()){const v=Nn(c,{handler:()=>{f.set(!1);const E=x.get();E&&E.focus()}});v&&v.destroy&&(p=v.destroy)}const h=Ht([l],([v])=>{if(v===null)return fe;const E=Tn(c,v);return E===null?fe:zn(c,E).destroy});return{destroy(){p(),h()}}}}),pt=Et(t("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:c=>(Me(c,o),ze(c),{destroy:jt(V(c,"pointerdown",h=>{const v=h.currentTarget;if(A(v)&&Gt(v)){h.preventDefault();return}}),V(c,"click",h=>{const v=h.currentTarget;if(A(v)){if(Gt(v)){h.preventDefault();return}if(h.defaultPrevented){bt(v);return}y.get()&&he(1).then(()=>{f.set(!1)})}}),V(c,"keydown",h=>{G(h)}),V(c,"pointermove",h=>{gt(h)}),V(c,"pointerleave",h=>{lt(h)}),V(c,"focusin",h=>{st(h)}),V(c,"focusout",h=>{Vt(h)}))})}),vt=Et(t("group"),{returned:()=>c=>({role:"group","aria-labelledby":c})}),Zt=Et(t("group-label"),{returned:()=>c=>({id:c})}),Se={defaultChecked:!1,disabled:!1},ie=c=>{const p={...Se,...c},h=p.checked??yt(p.defaultChecked??null),v=je(h,p.onCheckedChange),E=yt(p.disabled),J=Et(t("checkbox-item"),{stores:[v,E],returned:([R,B])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":St(R)?"mixed":R?"true":"false","data-disabled":rn(B),"data-state":Lt(R)}),action:R=>(Me(R,o),ze(R),{destroy:jt(V(R,"pointerdown",D=>{const M=D.currentTarget;if(A(M)&&Gt(M)){D.preventDefault();return}}),V(R,"click",D=>{const M=D.currentTarget;if(A(M)){if(Gt(M)){D.preventDefault();return}if(D.defaultPrevented){bt(M);return}v.update(ee=>St(ee)?!0:!ee),y.get()&&Ve().then(()=>{f.set(!1)})}}),V(R,"keydown",D=>{G(D)}),V(R,"pointermove",D=>{const M=D.currentTarget;if(A(M)){if(Gt(M)){_t(D);return}gt(D,M)}}),V(R,"pointerleave",D=>{lt(D)}),V(R,"focusin",D=>{st(D)}),V(R,"focusout",D=>{Vt(D)}))})}),mt=Oe(v,R=>R===!0),Q=Oe(v,R=>R==="indeterminate");return{elements:{checkboxItem:J},states:{checked:v},helpers:{isChecked:mt,isIndeterminate:Q},options:{disabled:E}}},Ft=(c={})=>{const p=c.value??yt(c.defaultValue??null),h=je(p,c.onValueChange),v=Et(t("radio-group"),{returned:()=>({role:"group"})}),E={disabled:!1},J=Et(t("radio-item"),{stores:[h],returned:([Q])=>R=>{const{value:B,disabled:D}={...E,...R},M=Q===B;return{disabled:D,role:"menuitemradio","data-state":M?"checked":"unchecked","aria-checked":M,"data-disabled":rn(D),"data-value":B,"data-orientation":"vertical",tabindex:-1}},action:Q=>(Me(Q,o),{destroy:jt(V(Q,"pointerdown",B=>{const D=B.currentTarget;if(!A(D))return;const M=Q.dataset.value;if(Q.dataset.disabled||M===void 0){B.preventDefault();return}}),V(Q,"click",B=>{const D=B.currentTarget;if(!A(D))return;const M=Q.dataset.value;if(Q.dataset.disabled||M===void 0){B.preventDefault();return}if(B.defaultPrevented){if(!A(D))return;bt(D);return}h.set(M),y.get()&&Ve().then(()=>{f.set(!1)})}),V(Q,"keydown",B=>{G(B)}),V(Q,"pointermove",B=>{const D=B.currentTarget;if(!A(D))return;const M=Q.dataset.value;if(Q.dataset.disabled||M===void 0){_t(B);return}gt(B,D)}),V(Q,"pointerleave",B=>{lt(B)}),V(Q,"focusin",B=>{st(B)}),V(Q,"focusout",B=>{Vt(B)}))})}),mt=Oe(h,Q=>R=>Q===R);return{elements:{radioGroup:v,radioItem:J},states:{value:h},helpers:{isChecked:mt}}},{elements:{root:Nt}}=Gr({orientation:"horizontal"}),$t={...Vr,disabled:!1,positioning:{placement:"right-start",gutter:8}},te=c=>{const p={...$t,...c},h=p.open??yt(!1),v=je(h,p==null?void 0:p.onOpenChange),E=Fe(Vn(p,"ids")),{positioning:J,arrowSize:mt,disabled:Q}=E,R=Ot(yt(null)),B=Ot(yt(null)),D=Ot(yt(0)),M=Fe({...An(Mn),...p.ids});_n(()=>{const W=document.getElementById(M.trigger.get());W&&R.set(W)});const ee=Cn({open:v,forceVisible:u,activeTrigger:R}),eo=Et(t("submenu"),{stores:[ee,M.menu,M.trigger],returned:([W,ft,Ct])=>({role:"menu",hidden:W?void 0:!0,style:De({display:W?void 0:"none"}),id:ft,"aria-labelledby":Ct,"data-state":W?"open":"closed","data-id":ft,tabindex:-1}),action:W=>{let ft=fe;const Ct=Ht([ee,J],([C,U])=>{if(ft(),!C)return;const et=R.get();et&&Ve().then(()=>{ft();const at=$(et);ft=Rn(W,{anchorElement:et,open:v,options:{floating:U,portal:A(at)?at:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy})}),N=jt(V(W,"keydown",C=>{if(C.key===kt.ESCAPE)return;const U=C.target,et=C.currentTarget;if(!A(U)||!A(et)||!(U.closest('[role="menu"]')===et))return;if(On.includes(C.key)){C.stopImmediatePropagation(),Ln(C,g.get()??!1);return}const ye=Nr.ltr.includes(C.key),we=C.ctrlKey||C.altKey||C.metaKey,Ne=C.key.length===1;if(ye){const wn=R.get();C.preventDefault(),v.update(()=>(wn&&bt(wn),!1));return}if(C.key===kt.TAB){C.preventDefault(),f.set(!1),Fn(C,P,O);return}!we&&Ne&&d.get()===!0&&Z(C.key,le(et))}),V(W,"pointermove",C=>{dt(C)}),V(W,"focusout",C=>{const U=R.get();if(T.get()){const et=C.target,at=document.getElementById(M.menu.get());if(!A(at)||!A(et))return;!at.contains(et)&&et!==U&&v.set(!1)}else{const et=C.currentTarget,at=C.relatedTarget;if(!A(at)||!A(et))return;!et.contains(at)&&at!==U&&v.set(!1)}}));return{destroy(){Ct(),ft(),N()}}}}),no=Et(t("subtrigger"),{stores:[v,Q,M.menu,M.trigger],returned:([W,ft,Ct,N])=>({role:"menuitem",id:N,tabindex:-1,"aria-controls":Ct,"aria-expanded":W,"data-state":W?"open":"closed","data-disabled":rn(ft),"aria-haspopop":"menu"}),action:W=>{Me(W,o),ze(W),R.update(N=>N||W);const ft=()=>{an(B),window.clearTimeout(D.get()),F.set(null)},Ct=jt(V(W,"click",N=>{if(N.defaultPrevented)return;const C=N.currentTarget;!A(C)||Gt(C)||(bt(C),v.get()||v.update(U=>U||(R.set(C),!U)))}),V(W,"keydown",N=>{const C=tt.get(),U=N.currentTarget;if(!(!A(U)||Gt(U)||C.length>0&&N.key===kt.SPACE)&&Br.ltr.includes(N.key)){if(!v.get()){U.click(),N.preventDefault();return}const at=U.getAttribute("aria-controls");if(!at)return;const ye=document.getElementById(at);if(!A(ye))return;const we=le(ye)[0];bt(we)}}),V(W,"pointermove",N=>{if(!Re(N)||(Kt(N),N.defaultPrevented))return;const C=N.currentTarget;if(!A(C))return;jr(M.menu.get())||bt(C);const U=B.get();!v.get()&&!U&&!Gt(C)&&B.set(window.setTimeout(()=>{v.update(()=>(R.set(C),!0)),an(B)},100))}),V(W,"pointerleave",N=>{if(!Re(N))return;an(B);const C=document.getElementById(M.menu.get()),U=C==null?void 0:C.getBoundingClientRect();if(U){const et=C==null?void 0:C.dataset.side,at=et==="right",ye=at?-5:5,we=U[at?"left":"right"],Ne=U[at?"right":"left"];F.set({area:[{x:N.clientX+ye,y:N.clientY},{x:we,y:U.top},{x:Ne,y:U.top},{x:Ne,y:U.bottom},{x:we,y:U.bottom}],side:et}),window.clearTimeout(D.get()),D.set(window.setTimeout(()=>{F.set(null)},300))}else{if(q(N),N.defaultPrevented)return;F.set(null)}}),V(W,"focusout",N=>{const C=N.currentTarget;if(!A(C))return;xe(C);const U=N.relatedTarget;if(!A(U))return;const et=C.getAttribute("aria-controls");if(!et)return;const at=document.getElementById(et);at&&!at.contains(U)&&v.set(!1)}),V(W,"focusin",N=>{st(N)}));return{destroy(){ft(),Ct()}}}}),oo=Et(t("subarrow"),{stores:mt,returned:W=>({"data-arrow":!0,style:De({position:"absolute",width:`var(--arrow-size, ${W}px)`,height:`var(--arrow-size, ${W}px)`})})});return Ht([f],([W])=>{W||(R.set(null),v.set(!1))}),Ht([F],([W])=>{!de||W||window.clearTimeout(D.get())}),Ht([v],([W])=>{if(de&&(W&&T.get()&&he(1).then(()=>{const ft=document.getElementById(M.menu.get());if(!ft)return;const Ct=le(ft);Ct.length&&bt(Ct[0])}),!W)){const ft=z.get(),Ct=document.getElementById(M.trigger.get());if(ft&&he(1).then(()=>{const N=document.getElementById(M.menu.get());N&&N.contains(ft)&&xe(ft)}),!Ct||document.activeElement===Ct)return;xe(Ct)}}),{ids:M,elements:{subTrigger:no,subMenu:eo,subArrow:oo},states:{subOpen:v},options:E}};_n(()=>{const c=document.getElementById(L.trigger.get());A(c)&&f.get()&&x.set(c);const p=[],h=()=>T.set(!1),v=()=>{T.set(!0),p.push(jt(ae(document,"pointerdown",h,{capture:!0,once:!0}),ae(document,"pointermove",h,{capture:!0,once:!0})))},E=J=>{if(J.key===kt.ESCAPE&&i.get()){f.set(!1);return}};return p.push(ae(document,"keydown",v,{capture:!0})),p.push(ae(document,"keydown",E)),()=>{p.forEach(J=>J())}}),Ht([f,z],([c,p])=>{!c&&p&&xe(p)}),Ht([f],([c])=>{if(de&&!c){const p=x.get();if(!p)return;const h=b.get();!c&&p&&bo({prop:h,defaultEl:p})}}),Ht([f,n],([c,p])=>{if(!de)return;const h=[];return c&&p&&h.push(yo()),he(1).then(()=>{const v=document.getElementById(L.menu.get());if(v&&c&&T.get()){if(m.get()){bt(v);return}const E=le(v);if(!E.length)return;bt(E[0])}}),()=>{h.forEach(v=>v())}}),Ht(f,c=>{if(!de)return;const p=()=>T.set(!1),h=v=>{if(T.set(!0),v.key===kt.ESCAPE&&c&&i.get()){f.set(!1);return}};return jt(ae(document,"pointerdown",p,{capture:!0,once:!0}),ae(document,"pointermove",p,{capture:!0,once:!0}),ae(document,"keydown",h,{capture:!0}))});function Tt(c){f.update(p=>{const h=!p;return h&&(P.set(Io(c)),O.set(Ro(c)),x.set(c)),h})}function st(c){const p=c.currentTarget;if(!A(p))return;const h=z.get();h&&xe(h),So(p),z.set(p)}function Vt(c){const p=c.currentTarget;A(p)&&xe(p)}function Kt(c){ct(c)&&c.preventDefault()}function _t(c){if(ct(c))return;const p=c.target;if(!A(p))return;const h=$(p);h&&bt(h)}function q(c){ct(c)&&c.preventDefault()}function dt(c){if(!Re(c))return;const p=c.target,h=c.currentTarget;if(!A(h)||!A(p))return;const v=_.get(),E=v!==c.clientX;if(h.contains(p)&&E){const J=c.clientX>v?"right":"left";S.set(J),_.set(c.clientX)}}function gt(c,p=null){if(!Re(c)||(Kt(c),c.defaultPrevented))return;if(p){bt(p);return}const h=c.currentTarget;A(h)&&bt(h)}function lt(c){Re(c)&&_t(c)}function G(c){if(tt.get().length>0&&c.key===kt.SPACE){c.preventDefault();return}if(ln.includes(c.key)){c.preventDefault();const v=c.currentTarget;if(!A(v))return;v.click()}}function St(c){return c==="indeterminate"}function Lt(c){return St(c)?"indeterminate":c?"checked":"unchecked"}function ct(c){return j.get()(c)}function $(c){const p=c.closest('[role="menu"]');return A(p)?p:null}return{elements:{trigger:X,menu:ot,overlay:rt,item:pt,group:vt,groupLabel:Zt,arrow:Y,separator:Nt},builders:{createCheckboxItem:ie,createSubmenu:te,createMenuRadioGroup:Ft},states:{open:f},helpers:{handleTypeaheadSearch:Z},ids:L,options:e.rootOptions}}function Fn(e,t,o){if(e.shiftKey){const n=o.get();n&&(e.preventDefault(),he(1).then(()=>n.focus()),o.set(null))}else{const n=t.get();n&&(e.preventDefault(),he(1).then(()=>n.focus()),t.set(null))}}function le(e){return Array.from(e.querySelectorAll(`[data-melt-menu-id="${e.id}"]`)).filter(t=>A(t))}function ze(e){!e||!Gt(e)||(e.setAttribute("data-disabled",""),e.setAttribute("aria-disabled","true"))}function an(e){if(!de)return;const t=e.get();t&&(window.clearTimeout(t),e.set(null))}function Re(e){return e.pointerType==="mouse"}function Me(e,t){if(!e)return;const o=e.closest(`${t()}, ${t("submenu")}`);A(o)&&e.setAttribute("data-melt-menu-id",o.id)}function Ln(e,t){e.preventDefault();const o=document.activeElement,n=e.currentTarget;if(!A(o)||!A(n))return;const r=le(n);if(!r.length)return;const s=r.filter(l=>!(l.hasAttribute("data-disabled")||l.getAttribute("disabled")==="true")),i=s.indexOf(o);let a;switch(e.key){case kt.ARROW_DOWN:t?a=i<s.length-1?i+1:0:a=i<s.length-1?i+1:i;break;case kt.ARROW_UP:t?a=i>0?i-1:s.length-1:a=i<0?s.length-1:i>0?i-1:0;break;case kt.HOME:a=0;break;case kt.END:a=s.length-1;break;default:return}bt(s[a])}function zr(e,t){if(!t)return!1;const o={x:e.clientX,y:e.clientY};return Hr(o,t)}function Hr(e,t){const{x:o,y:n}=e;let r=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,u=t[i].x,d=t[i].y;l>n!=d>n&&o<(u-a)*(n-l)/(d-l)+a&&(r=!r)}return r}function jr(e){const t=document.activeElement;if(!A(t))return!1;const o=t.closest(`[data-id="${e}"]`);return A(o)}function Ur(e){return e?"open":"closed"}const Xr={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function Yr(e){const t={...Xr,...e},o=Fe(Vn(t,"ids")),n=t.open??yt(t.defaultOpen),r=je(n,t==null?void 0:t.onOpenChange),s=Ot(yt(null)),i=Ot(yt(null)),a=Ot(yt(null)),{elements:l,builders:u,ids:d,states:g,options:b}=Kr({rootOptions:o,rootOpen:r,rootActiveTrigger:Ot(s),nextFocusable:Ot(i),prevFocusable:Ot(a),selector:"dropdown-menu",ids:t.ids});return{ids:d,elements:l,states:g,builders:u,options:b}}const qr={orientation:"horizontal",decorative:!1},Gr=e=>{const t={...qr,...e},o=Fe(t),{orientation:n,decorative:r}=o;return{elements:{root:Et("separator",{stores:[n,r],returned:([i,a])=>({role:a?"none":"separator","aria-orientation":i==="vertical"?i:void 0,"aria-hidden":a,"data-orientation":i})})},options:o}};function Jr(e){return(t={})=>Qr(e,t)}function Qr(e,t){const n={...{side:"bottom",align:"center",sideOffset:0,alignOffset:0,sameWidth:!1,avoidCollisions:!0,collisionPadding:8,fitViewport:!1,strategy:"absolute",overlap:!1},...t};e.update(r=>({...r,placement:Zr(n.side,n.align),offset:{...r.offset,mainAxis:n.sideOffset,crossAxis:n.alignOffset},gutter:0,sameWidth:n.sameWidth,flip:n.avoidCollisions,overflowPadding:n.collisionPadding,boundary:n.collisionBoundary,fitViewport:n.fitViewport,strategy:n.strategy,overlap:n.overlap}))}function Zr(e,t){return t==="center"?e:`${e}-${t}`}function to(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function en(){const{NAME:e}=to();return ro(e)}function $r(e){const{NAME:t,PARTS:o}=to(),n=Oo("menu",o),r={...Yr({...go(e),forceVisible:!0}),getAttrs:n};return io(t,r),{...r,updateOption:mo(r.options)}}function ti(e){const o={...{side:"bottom",align:"center"},...e},{options:{positioning:n}}=en();Jr(n)(o)}function ei(e,t){const o=Xt(t,["children","$$slots","$$events","$$legacy"]),n=Xt(o,["href","asChild","disabled","el"]);ve(t,!1);const[r,s]=We(),i=()=>Ae(m,"$item",r),a=Xe(),l=Xe();let u=k(t,"href",24,()=>{}),d=k(t,"asChild",8,!1),g=k(t,"disabled",8,!1),b=k(t,"el",28,()=>{});const{elements:{item:m},getAttrs:y}=en(),w=fn();nt(()=>i(),()=>{Ue(a,i())}),nt(()=>H(g()),()=>{Ue(l,{...y("item"),..._o(g())})}),nt(()=>(I(a),I(l)),()=>{Object.assign(I(a),I(l))}),Qe(),Ce();var f=Pt(),x=ht(f);{var P=T=>{var _=Pt(),F=ht(_);It(F,t,"default",{get builder(){return I(a)}},null),K(T,_)},O=T=>{var _=Pt(),F=ht(_);Po(F,()=>u()?"a":"div",!1,(S,z)=>{me(S,Z=>b(Z),()=>b()),ge(S,Z=>{var L,it;return(it=(L=I(a)).action)==null?void 0:it.call(L,Z)}),ue(S,()=>({href:u(),...I(a),...n})),At("m-click",S,w),At("m-focusin",S,w),At("m-focusout",S,w),At("m-keydown",S,w),At("m-pointerdown",S,w),At("m-pointerleave",S,w),At("m-pointermove",S,w),At("pointerenter",S,function(Z){Jt.call(this,t,Z)});var j=Pt(),tt=ht(j);It(tt,t,"default",{get builder(){return I(a)}},null),K(z,j)}),K(T,_)};Dt(x,T=>{d()?T(P):T(O,!1)})}K(e,f),be(),s()}function ni(e,t){ve(t,!1);const[o,n]=We(),r=()=>Ae(_,"$idValues",o);let s=k(t,"closeOnOutsideClick",24,()=>{}),i=k(t,"closeOnEscape",24,()=>{}),a=k(t,"portal",24,()=>{}),l=k(t,"open",28,()=>{}),u=k(t,"onOpenChange",24,()=>{}),d=k(t,"preventScroll",24,()=>{}),g=k(t,"loop",24,()=>{}),b=k(t,"dir",24,()=>{}),m=k(t,"typeahead",24,()=>{}),y=k(t,"closeFocus",24,()=>{}),w=k(t,"disableFocusFirstItem",24,()=>{}),f=k(t,"closeOnItemClick",24,()=>{}),x=k(t,"onOutsideClick",24,()=>{});const{states:{open:P},updateOption:O,ids:T}=$r({closeOnOutsideClick:s(),closeOnEscape:i(),portal:a(),forceVisible:!0,defaultOpen:l(),preventScroll:d(),loop:g(),dir:b(),typeahead:m(),closeFocus:y(),disableFocusFirstItem:w(),closeOnItemClick:f(),onOutsideClick:x(),onOpenChange:({next:z})=>{var j;return l()!==z&&((j=u())==null||j(z),l(z)),z}}),_=Oe([T.menu,T.trigger],([z,j])=>({menu:z,trigger:j}));nt(()=>H(l()),()=>{l()!==void 0&&P.set(l())}),nt(()=>H(s()),()=>{O("closeOnOutsideClick",s())}),nt(()=>H(i()),()=>{O("closeOnEscape",i())}),nt(()=>H(a()),()=>{O("portal",a())}),nt(()=>H(d()),()=>{O("preventScroll",d())}),nt(()=>H(g()),()=>{O("loop",g())}),nt(()=>H(b()),()=>{O("dir",b())}),nt(()=>H(y()),()=>{O("closeFocus",y())}),nt(()=>H(w()),()=>{O("disableFocusFirstItem",w())}),nt(()=>H(m()),()=>{O("typeahead",m())}),nt(()=>H(f()),()=>{O("closeOnItemClick",f())}),nt(()=>H(x()),()=>{O("onOutsideClick",x())}),Qe(),Ce();var F=Pt(),S=ht(F);It(S,t,"default",{get ids(){return r()}},null),K(e,F),be(),n()}var oi=ut("<div><!></div>"),ri=ut("<div><!></div>"),ii=ut("<div><!></div>"),si=ut("<div><!></div>"),ai=ut("<div><!></div>");function li(e,t){const o=Xt(t,["children","$$slots","$$events","$$legacy"]),n=Xt(o,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]);ve(t,!1);const[r,s]=We(),i=()=>Ae(it,"$menu",r),a=()=>Ae(ot,"$open",r),l=Xe();let u=k(t,"transition",24,()=>{}),d=k(t,"transitionConfig",24,()=>{}),g=k(t,"inTransition",24,()=>{}),b=k(t,"inTransitionConfig",24,()=>{}),m=k(t,"outTransition",24,()=>{}),y=k(t,"outTransitionConfig",24,()=>{}),w=k(t,"asChild",8,!1),f=k(t,"id",24,()=>{}),x=k(t,"side",8,"bottom"),P=k(t,"align",8,"center"),O=k(t,"sideOffset",8,0),T=k(t,"alignOffset",8,0),_=k(t,"collisionPadding",8,8),F=k(t,"avoidCollisions",8,!0),S=k(t,"collisionBoundary",24,()=>{}),z=k(t,"sameWidth",8,!1),j=k(t,"fitViewport",8,!1),tt=k(t,"strategy",8,"absolute"),Z=k(t,"overlap",8,!1),L=k(t,"el",28,()=>{});const{elements:{menu:it},states:{open:ot},ids:X,getAttrs:Y}=en(),rt=fn(),pt=Y("content");nt(()=>H(f()),()=>{f()&&X.menu.set(f())}),nt(()=>i(),()=>{Ue(l,i())}),nt(()=>I(l),()=>{Object.assign(I(l),pt)}),nt(()=>(a(),H(x()),H(P()),H(O()),H(T()),H(_()),H(F()),H(S()),H(z()),H(j()),H(tt()),H(Z())),()=>{a()&&ti({side:x(),align:P(),sideOffset:O(),alignOffset:T(),collisionPadding:_(),avoidCollisions:F(),collisionBoundary:S(),sameWidth:z(),fitViewport:j(),strategy:tt(),overlap:Z()})}),Qe(),Ce();var vt=Pt(),Zt=ht(vt);{var Se=Ft=>{var Nt=Pt(),$t=ht(Nt);It($t,t,"default",{get builder(){return I(l)}},null),K(Ft,Nt)},ie=(Ft,Nt)=>{{var $t=Tt=>{var st=oi();ue(st,()=>({...I(l),...n}));var Vt=wt(st);It(Vt,t,"default",{get builder(){return I(l)}},null),xt(st),me(st,Kt=>L(Kt),()=>L()),ge(st,Kt=>{var _t,q;return(q=(_t=I(l)).action)==null?void 0:q.call(_t,Kt)}),ce(()=>At("m-keydown",st,rt)),Ie(3,st,u,d),K(Tt,st)},te=(Tt,st)=>{{var Vt=_t=>{var q=ri();ue(q,()=>({...I(l),...n}));var dt=wt(q);It(dt,t,"default",{get builder(){return I(l)}},null),xt(q),me(q,gt=>L(gt),()=>L()),ge(q,gt=>{var lt,G;return(G=(lt=I(l)).action)==null?void 0:G.call(lt,gt)}),ce(()=>At("m-keydown",q,rt)),Ie(1,q,g,b),Ie(2,q,m,y),K(_t,q)},Kt=(_t,q)=>{{var dt=lt=>{var G=ii();ue(G,()=>({...I(l),...n}));var St=wt(G);It(St,t,"default",{get builder(){return I(l)}},null),xt(G),me(G,Lt=>L(Lt),()=>L()),ge(G,Lt=>{var ct,$;return($=(ct=I(l)).action)==null?void 0:$.call(ct,Lt)}),ce(()=>At("m-keydown",G,rt)),Ie(1,G,g,b),K(lt,G)},gt=(lt,G)=>{{var St=ct=>{var $=si();ue($,()=>({...I(l),...n}));var c=wt($);It(c,t,"default",{get builder(){return I(l)}},null),xt($),me($,p=>L(p),()=>L()),ge($,p=>{var h,v;return(v=(h=I(l)).action)==null?void 0:v.call(h,p)}),ce(()=>At("m-keydown",$,rt)),Ie(2,$,m,y),K(ct,$)},Lt=(ct,$)=>{{var c=p=>{var h=ai();ue(h,()=>({...I(l),...n}));var v=wt(h);It(v,t,"default",{get builder(){return I(l)}},null),xt(h),me(h,E=>L(E),()=>L()),ge(h,E=>{var J,mt;return(mt=(J=I(l)).action)==null?void 0:mt.call(J,E)}),ce(()=>At("m-keydown",h,rt)),K(p,h)};Dt(ct,p=>{a()&&p(c)},$)}};Dt(lt,ct=>{m()&&a()?ct(St):ct(Lt,!1)},G)}};Dt(_t,lt=>{g()&&a()?lt(dt):lt(gt,!1)},q)}};Dt(Tt,_t=>{g()&&m()&&a()?_t(Vt):_t(Kt,!1)},st)}};Dt(Ft,Tt=>{u()&&a()?Tt($t):Tt(te,!1)},Nt)}};Dt(Zt,Ft=>{w()&&a()?Ft(Se):Ft(ie,!1)})}K(e,vt),be(),s()}var ci=ut("<button><!></button>");function ui(e,t){const o=Xt(t,["children","$$slots","$$events","$$legacy"]),n=Xt(o,["asChild","id","el"]);ve(t,!1);const[r,s]=We(),i=()=>Ae(g,"$trigger",r),a=Xe();let l=k(t,"asChild",8,!1),u=k(t,"id",24,()=>{}),d=k(t,"el",28,()=>{});const{elements:{trigger:g},ids:b,getAttrs:m}=en(),y=fn(),w=m("trigger");nt(()=>H(u()),()=>{u()&&b.trigger.set(u())}),nt(()=>i(),()=>{Ue(a,i())}),nt(()=>I(a),()=>{Object.assign(I(a),w)}),Qe(),Ce();var f=Pt(),x=ht(f);{var P=T=>{var _=Pt(),F=ht(_);It(F,t,"default",{get builder(){return I(a)}},null),K(T,_)},O=T=>{var _=ci();ue(_,()=>({...I(a),type:"button",...n}));var F=wt(_);It(F,t,"default",{get builder(){return I(a)}},null),xt(_),me(_,S=>d(S),()=>d()),ge(_,S=>{var z,j;return(j=(z=I(a)).action)==null?void 0:j.call(z,S)}),ce(()=>At("m-keydown",_,y)),ce(()=>At("m-pointerdown",_,y)),K(T,_)};Dt(x,T=>{l()?T(P):T(O,!1)})}K(e,f),be(),s()}function He(e,t){const o=Xt(t,["children","$$slots","$$events","$$legacy"]),n=Xt(o,["class","inset"]);ve(t,!1);let r=k(t,"class",8,void 0),s=k(t,"inset",8,void 0);Ce();const i=Wn(()=>(H(Ye),H(s()),H(r()),Bn(()=>Ye("data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s()&&"pl-8",r()))));ei(e,Kn({get class(){return I(i)}},()=>n,{$$events:{click(a){Jt.call(this,t,a)},keydown(a){Jt.call(this,t,a)},focusin(a){Jt.call(this,t,a)},focusout(a){Jt.call(this,t,a)},pointerdown(a){Jt.call(this,t,a)},pointerleave(a){Jt.call(this,t,a)},pointermove(a){Jt.call(this,t,a)}},children:(a,l)=>{var u=Pt(),d=ht(u);It(d,t,"default",{},null),K(a,u)},$$slots:{default:!0}})),be()}function di(e,t){const o=Xt(t,["children","$$slots","$$events","$$legacy"]),n=Xt(o,["class","sideOffset","transition","transitionConfig"]);ve(t,!1);let r=k(t,"class",8,void 0),s=k(t,"sideOffset",8,4),i=k(t,"transition",8,ko),a=k(t,"transitionConfig",8,void 0);Ce();const l=Wn(()=>(H(Ye),H(r()),Bn(()=>Ye("bg-popover text-popover-foreground z-50 min-w-[8rem] rounded-md border p-1 shadow-md focus:outline-none",r()))));li(e,Kn({get transition(){return i()},get transitionConfig(){return a()},get sideOffset(){return s()},get class(){return I(l)}},()=>n,{$$events:{keydown(u){Jt.call(this,t,u)}},children:(u,d)=>{var g=Pt(),b=ht(g);It(b,t,"default",{},null),K(u,g)},$$slots:{default:!0}})),be()}const fi=ni,gi=ui;var mi=ut('<li><a href="/login" class="text-muted-foreground hover:text-foreground transition-colors">Sign In</a></li>'),hi=ut('<li><a href="/sign_out" class="text-muted-foreground hover:text-foreground transition-colors">Sign Out</a></li>'),pi=ut('<a href="/onboarding" class="btn-primary px-6 py-2">Get Started</a>'),vi=ut('<a class="btn-primary px-6 py-2">Dashboard</a>'),bi=ut('<a href="/pricing" class="w-full">Pricing</a>'),yi=ut('<a href="/login" class="w-full">Sign In</a>'),wi=ut('<a href="/sign_out" class="w-full">Sign Out</a>'),xi=ut('<a href="/onboarding" class="w-full">Get Started</a>'),Oi=ut('<a class="w-full">Dashboard</a>'),_i=ut("<!> <!> <!>",1),Ai=ut("<!> <!>",1),Ti=ut('<div class="flex-none"><ul class="hidden sm:flex items-center gap-8 font-bold"><li><a href="/pricing" class="text-muted-foreground hover:text-foreground transition-colors">Pricing</a></li> <!> <li><!></li></ul> <div class="sm:hidden"><!></div></div>'),Ci=ut('<div class="bg-background border-b-2 border-border sticky top-0 z-50 nav-blur"><div class="max-w-6xl mx-auto px-6 py-4"><div class="flex items-center justify-between"><div class="flex-1 flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"> </a></div> <!></div></div></div> <main class="flex-1"><!></main> <!>',1);function es(e,t){ve(t,!0);const[o,n]=We(),r=()=>Ae(To,"$page",o),s=Ao(),i=on(()=>r().url.pathname.startsWith("/login")||r().url.pathname.startsWith("/sign_up")||r().url.pathname.startsWith("/forgot_password")||r().url.pathname.startsWith("/check_email"));var a=Ci(),l=ht(a),u=wt(l),d=wt(u),g=wt(d),b=zt(wt(g),2),m=wt(b,!0);xt(b),xt(g);var y=zt(g,2);{var w=O=>{var T=Ti(),_=wt(T),F=zt(wt(_),2);{var S=X=>{var Y=mi();K(X,Y)},z=X=>{var Y=hi();K(X,Y)};Dt(F,X=>{var Y;!t.data.auth.user||(Y=t.data.auth.user)!=null&&Y.is_anonymous?X(S):X(z,!1)})}var j=zt(F,2),tt=wt(j);{var Z=X=>{var Y=pi();K(X,Y)},L=X=>{var Y=vi();nn(()=>xn(Y,"href",`/dashboard/${s.value.slug??""}`)),K(X,Y)};Dt(tt,X=>{s.value?X(L,!1):X(Z)})}xt(j),xt(_);var it=zt(_,2),ot=wt(it);se(ot,()=>fi,(X,Y)=>{Y(X,{children:(rt,pt)=>{var vt=Ai(),Zt=ht(vt);se(Zt,()=>gi,(ie,Ft)=>{Ft(ie,{asChild:!0,children:so,$$slots:{default:(Nt,$t)=>{const te=on(()=>$t.builder),Tt=on(()=>[I(te)]);Eo(Nt,{get builders(){return I(Tt)},variant:"ghost",size:"sm",class:"text-muted-foreground",children:(st,Vt)=>{xo(st,{class:"h-5 w-5"})},$$slots:{default:!0}})}}})});var Se=zt(Zt,2);se(Se,()=>di,(ie,Ft)=>{Ft(ie,{class:"w-56 sm:hidden",children:(Nt,$t)=>{var te=_i(),Tt=ht(te);se(Tt,()=>He,(q,dt)=>{dt(q,{children:(gt,lt)=>{var G=bi();K(gt,G)},$$slots:{default:!0}})});var st=zt(Tt,2);{var Vt=q=>{var dt=Pt(),gt=ht(dt);se(gt,()=>He,(lt,G)=>{G(lt,{children:(St,Lt)=>{var ct=yi();K(St,ct)},$$slots:{default:!0}})}),K(q,dt)},Kt=q=>{var dt=Pt(),gt=ht(dt);se(gt,()=>He,(lt,G)=>{G(lt,{children:(St,Lt)=>{var ct=wi();K(St,ct)},$$slots:{default:!0}})}),K(q,dt)};Dt(st,q=>{var dt;!t.data.auth.user||(dt=t.data.auth.user)!=null&&dt.is_anonymous?q(Vt):q(Kt,!1)})}var _t=zt(st,2);se(_t,()=>He,(q,dt)=>{dt(q,{children:(gt,lt)=>{var G=Pt(),St=ht(G);{var Lt=$=>{var c=xi();K($,c)},ct=$=>{var c=Oi();nn(()=>xn(c,"href",`/dashboard/${s.value.slug??""}`)),K($,c)};Dt(St,$=>{s.value?$(ct,!1):$(Lt)})}K(gt,G)},$$slots:{default:!0}})}),K(Nt,te)},$$slots:{default:!0}})}),K(rt,vt)},$$slots:{default:!0}})}),xt(it),xt(T),K(O,T)};Dt(y,O=>{I(i)||O(w)})}xt(d),xt(u),xt(l);var f=zt(l,2),x=wt(f);lo(x,()=>t.children),xt(f);var P=zt(f,2);wo(P,{}),nn(()=>ao(m,Co)),K(e,a),be(),n()}export{es as component};
