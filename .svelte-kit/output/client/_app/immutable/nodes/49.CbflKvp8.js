import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{b as B,e as N,a as u,b6 as Ke,as as Ze,ar as Ge,p as ie,l as D,h as ye,g as de,aM as O,k as $e,m as Ae,j as n,f as E,c as k,r as S,Y as He,i as re,d as Fe,s as q,$ as Xe,t as _e,n as le,v as Q,aR as Ee}from"../chunks/wnqW1tdD.js";import{e as Ue,h as et,s as Pe}from"../chunks/CDPCzm7q.js";import{i as W}from"../chunks/BjRbZGyQ.js";import{e as tt,i as rt}from"../chunks/CsnEE4l9.js";import{a as ae,s as at}from"../chunks/rh_XW2Tv.js";import{P as st}from"../chunks/B3mdnymm.js";import{W as nt}from"../chunks/QNe64B_9.js";import{s as R}from"../chunks/BDqVm3Gq.js";import{a as se}from"../chunks/Cet13GU7.js";import{b as ne}from"../chunks/D5U2DSnR.js";import{i as ce}from"../chunks/BxG_UISn.js";import{l as V,s as Me,p as x}from"../chunks/Cmdkv-7M.js";import{a as we,s as oe}from"../chunks/D5ITLM2v.js";import{o as ot,m as be,f as lt,g as Se,e as it,a as Be,i as We,s as dt,k as J,h as ct}from"../chunks/D477uqMC.js";import{d as ut,w as Qe}from"../chunks/BvpDAKCq.js";import{t as vt,g as ft,o as gt,a as Le,r as mt,b as ht}from"../chunks/CuJHdp1M.js";import{c as _t,a as bt}from"../chunks/DPaidA8O.js";import{c as ve}from"../chunks/BMdVdstb.js";import{b as pt}from"../chunks/B2uh23P-.js";import{I as xt}from"../chunks/CX_t0Ed_.js";import{t as pe}from"../chunks/sBNKiCwy.js";import{s as yt}from"../chunks/CiB29Aqe.js";function $t(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=[["path",{d:"m6 9 6 6 6-6"}]];xt(g,Me({name:"chevron-down"},()=>h,{get iconNode(){return _},children:(p,A)=>{var y=B(),o=N(y);R(o,e,"default",{},null),u(p,y)},$$slots:{default:!0}}))}const{name:xe,selector:Ye}=lt("accordion"),At={multiple:!1,disabled:!1,forceVisible:!1},wt=g=>{const e={...At,...g},h=vt(ot(e,"value","onValueChange","defaultValue")),_=ft(["root"]),{disabled:p,forceVisible:A}=h,y=e.value??Qe(e.defaultValue),o=gt(y,e==null?void 0:e.onValueChange),d=(t,r)=>r===void 0?!1:typeof r=="string"?r===t:r.includes(t),l=ut(o,t=>r=>d(r,t)),b=be(xe(),{returned:()=>({"data-melt-id":_.root})}),m=t=>typeof t=="string"?{value:t}:t,$=t=>typeof t=="number"?{level:t}:t,T=be(xe("item"),{stores:o,returned:t=>r=>{const{value:a,disabled:s}=m(r);return{"data-state":d(a,t)?"open":"closed","data-disabled":Se(s)}}}),w=be(xe("trigger"),{stores:[o,p],returned:([t,r])=>a=>{const{value:s,disabled:c}=m(a);return{disabled:Se(r||c),"aria-expanded":!!d(s,t),"aria-disabled":!!c,"data-disabled":Se(c),"data-value":s,"data-state":d(s,t)?"open":"closed"}},action:t=>({destroy:it(Be(t,"click",()=>{const a=t.dataset.disabled==="true",s=t.dataset.value;a||!s||f(s)}),Be(t,"keydown",a=>{if(![J.ARROW_DOWN,J.ARROW_UP,J.HOME,J.END].includes(a.key))return;if(a.preventDefault(),a.key===J.SPACE||a.key===J.ENTER){const j=t.dataset.disabled==="true",F=t.dataset.value;if(j||!F)return;f(F);return}const s=a.target,c=ct(_.root);if(!c||!We(s))return;const i=Array.from(c.querySelectorAll(Ye("trigger"))).filter(j=>We(j)?j.dataset.disabled!=="true":!1);if(!i.length)return;const M=i.indexOf(s);a.key===J.ARROW_DOWN&&i[(M+1)%i.length].focus(),a.key===J.ARROW_UP&&i[(M-1+i.length)%i.length].focus(),a.key===J.HOME&&i[0].focus(),a.key===J.END&&i[i.length-1].focus()}))})}),z=be(xe("content"),{stores:[o,p,A],returned:([t,r,a])=>s=>{const{value:c}=m(s),v=d(c,t)||a;return{"data-state":v?"open":"closed","data-disabled":Se(r),"data-value":c,hidden:v?void 0:!0,style:dt({display:v?void 0:"none"})}},action:t=>{Ke().then(()=>{const r=Le(),a=Le(),s=document.querySelector(`${Ye("trigger")}, [data-value="${t.dataset.value}"]`);We(s)&&(t.id=r,s.setAttribute("aria-controls",r),s.id=a)})}}),C=be(xe("heading"),{returned:()=>t=>{const{level:r}=$(t);return{role:"heading","aria-level":r,"data-heading-level":r}}});function f(t){o.update(r=>r===void 0?e.multiple?[t]:t:Array.isArray(r)?r.includes(t)?r.filter(a=>a!==t):(r.push(t),r):r===t?void 0:t)}return{ids:_,elements:{root:b,item:T,trigger:w,content:z,heading:C},states:{value:o},helpers:{isSelected:l},options:h}};function Ne(){return{NAME:"accordion",ITEM_NAME:"accordion-item",PARTS:["root","content","header","item","trigger"]}}function Ct(g){const e=wt(mt(g)),{NAME:h,PARTS:_}=Ne(),p=_t(h,_),A={...e,getAttrs:p,updateOption:ht(e.options)};return Ze(h,A),A}function Oe(){const{NAME:g}=Ne();return Ge(g)}function Pt(g){const{ITEM_NAME:e}=Ne(),h=Qe(g);return Ze(e,{propsStore:h}),{...Oe(),propsStore:h}}function Je(){const{ITEM_NAME:g}=Ne();return Ge(g)}function St(){const g=Oe(),{propsStore:e}=Je();return{...g,propsStore:e}}function It(){const g=Oe(),{propsStore:e}=Je();return{...g,props:e}}function kt(g,e){return g.length!==e.length?!1:g.every((h,_)=>h===e[_])}var Tt=E("<div><!></div>");function Et(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["multiple","value","onValueChange","disabled","asChild","el"]);ie(e,!1);const[p,A]=we(),y=()=>oe(w,"$root",p),o=Ae();let d=x(e,"multiple",8,!1),l=x(e,"value",28,()=>{}),b=x(e,"onValueChange",24,()=>{}),m=x(e,"disabled",8,!1),$=x(e,"asChild",8,!1),T=x(e,"el",28,()=>{});const{elements:{root:w},states:{value:z},updateOption:C,getAttrs:f}=Ct({multiple:d(),disabled:m(),defaultValue:l(),onValueChange:({next:v})=>{var i,M;return Array.isArray(v)?((!Array.isArray(l())||!kt(l(),v))&&((i=b())==null||i(v),l(v)),v):(l()!==v&&((M=b())==null||M(v),l(v)),v)}}),t=f("root");D(()=>O(l()),()=>{l()!==void 0&&z.set(Array.isArray(l())?[...l()]:l())}),D(()=>O(d()),()=>{C("multiple",d())}),D(()=>O(m()),()=>{C("disabled",m())}),D(()=>y(),()=>{$e(o,y())}),D(()=>n(o),()=>{Object.assign(n(o),t)}),ye(),ce();var r=B(),a=N(r);{var s=v=>{var i=B(),M=N(i);R(M,e,"default",{get builder(){return n(o)}},null),u(v,i)},c=v=>{var i=Tt();ae(i,()=>({...n(o),..._}));var M=k(i);R(M,e,"default",{get builder(){return n(o)}},null),S(i),ne(i,j=>T(j),()=>T()),se(i,j=>{var F,I;return(I=(F=n(o)).action)==null?void 0:I.call(F,j)}),u(v,i)};W(a,v=>{$()?v(s):v(c,!1)})}u(g,r),de(),A()}var Mt=E("<div><!></div>");function Nt(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["value","disabled","asChild","el"]);ie(e,!1);const[p,A]=we(),y=()=>oe(T,"$item",p),o=()=>oe(w,"$propsStore",p),d=Ae();let l=x(e,"value",8),b=x(e,"disabled",24,()=>{}),m=x(e,"asChild",8,!1),$=x(e,"el",28,()=>{});const{elements:{item:T},propsStore:w,getAttrs:z}=Pt({value:l(),disabled:b()}),C=z("item");D(()=>(O(l()),O(b())),()=>{w.set({value:l(),disabled:b()})}),D(()=>(y(),o(),O(b())),()=>{$e(d,y()({...o(),disabled:b()}))}),D(()=>n(d),()=>{Object.assign(n(d),C)}),ye(),ce();var f=B(),t=N(f);{var r=s=>{var c=B(),v=N(c);R(v,e,"default",{get builder(){return n(d)}},null),u(s,c)},a=s=>{var c=Mt();ae(c,()=>({...n(d),..._}));var v=k(c);R(v,e,"default",{get builder(){return n(d)}},null),S(c),ne(c,i=>$(i),()=>$()),se(c,i=>{var M,j;return(j=(M=n(d)).action)==null?void 0:j.call(M,i)}),u(s,c)};W(t,s=>{m()?s(r):s(a,!1)})}u(g,f),de(),A()}var Ot=E("<div><!></div>");function Rt(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["level","asChild","el"]);ie(e,!1);const[p,A]=we(),y=()=>oe(m,"$header",p),o=Ae();let d=x(e,"level",8,3),l=x(e,"asChild",8,!1),b=x(e,"el",28,()=>{});const{elements:{heading:m},getAttrs:$}=Oe(),T=$("header");D(()=>(y(),O(d())),()=>{$e(o,y()(d()))}),D(()=>n(o),()=>{Object.assign(n(o),T)}),ye(),ce();var w=B(),z=N(w);{var C=t=>{var r=B(),a=N(r);R(a,e,"default",{get builder(){return n(o)}},null),u(t,r)},f=t=>{var r=Ot();ae(r,()=>({...n(o),..._}));var a=k(r);R(a,e,"default",{get builder(){return n(o)}},null),S(r),ne(r,s=>b(s),()=>b()),se(r,s=>{var c,v;return(v=(c=n(o)).action)==null?void 0:v.call(c,s)}),u(t,r)};W(z,t=>{l()?t(C):t(f,!1)})}u(g,w),de(),A()}var zt=E("<button><!></button>");function Vt(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["asChild","el"]);ie(e,!1);const[p,A]=we(),y=()=>oe(m,"$trigger",p),o=()=>oe($,"$props",p),d=Ae();let l=x(e,"asChild",8,!1),b=x(e,"el",28,()=>{});const{elements:{trigger:m},props:$,getAttrs:T}=It(),w=bt(),z=T("trigger");D(()=>(y(),o()),()=>{$e(d,y()({...o()}))}),D(()=>n(d),()=>{Object.assign(n(d),z)}),ye(),ce();var C=B(),f=N(C);{var t=a=>{var s=B(),c=N(s);R(c,e,"default",{get builder(){return n(d)}},null),u(a,s)},r=a=>{var s=zt();ae(s,()=>({...n(d),type:"button",..._}));var c=k(s);R(c,e,"default",{get builder(){return n(d)}},null),S(s),ne(s,v=>b(v),()=>b()),se(s,v=>{var i,M;return(M=(i=n(d)).action)==null?void 0:M.call(i,v)}),He(()=>Ue("m-keydown",s,w)),He(()=>Ue("m-click",s,w)),u(a,s)};W(f,a=>{l()?a(t):a(r,!1)})}u(g,C),de(),A()}var jt=E("<div><!></div>"),Wt=E("<div><!></div>"),Ft=E("<div><!></div>"),qt=E("<div><!></div>"),Dt=E("<div><!></div>");function Ht(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);ie(e,!1);const[p,A]=we(),y=()=>oe(t,"$content",p),o=()=>oe(a,"$propsStore",p),d=()=>oe(r,"$isSelected",p),l=Ae();let b=x(e,"transition",24,()=>{}),m=x(e,"transitionConfig",24,()=>{}),$=x(e,"inTransition",24,()=>{}),T=x(e,"inTransitionConfig",24,()=>{}),w=x(e,"outTransition",24,()=>{}),z=x(e,"outTransitionConfig",24,()=>{}),C=x(e,"asChild",8,!1),f=x(e,"el",28,()=>{});const{elements:{content:t},helpers:{isSelected:r},propsStore:a,getAttrs:s}=St(),c=s("content");D(()=>(y(),o()),()=>{$e(l,y()({...o()}))}),D(()=>n(l),()=>{Object.assign(n(l),c)}),ye(),ce();var v=B(),i=N(v);{var M=F=>{var I=B(),H=N(I);R(H,e,"default",{get builder(){return n(l)}},null),u(F,I)},j=(F,I)=>{{var H=G=>{var P=jt();ae(P,()=>({...n(l),..._}));var Z=k(P);R(Z,e,"default",{get builder(){return n(l)}},null),S(P),ne(P,ge=>f(ge),()=>f()),se(P,ge=>{var K,U;return(U=(K=n(l)).action)==null?void 0:U.call(K,ge)}),pe(3,P,b,m),u(G,P)},fe=(G,P)=>{{var Z=K=>{var U=Wt();ae(U,()=>({...n(l),..._}));var Re=k(U);R(Re,e,"default",{get builder(){return n(l)}},null),S(U),ne(U,me=>f(me),()=>f()),se(U,me=>{var X,L;return(L=(X=n(l)).action)==null?void 0:L.call(X,me)}),pe(1,U,$,T),pe(2,U,w,z),u(K,U)},ge=(K,U)=>{{var Re=X=>{var L=Ft();ae(L,()=>({...n(l),..._}));var ze=k(L);R(ze,e,"default",{get builder(){return n(l)}},null),S(L),ne(L,he=>f(he),()=>f()),se(L,he=>{var ee,Y;return(Y=(ee=n(l)).action)==null?void 0:Y.call(ee,he)}),pe(1,L,$,T),u(X,L)},me=(X,L)=>{{var ze=ee=>{var Y=qt();ae(Y,()=>({...n(l),..._}));var Ve=k(Y);R(Ve,e,"default",{get builder(){return n(l)}},null),S(Y),ne(Y,ue=>f(ue),()=>f()),se(Y,ue=>{var te,Ce;return(Ce=(te=n(l)).action)==null?void 0:Ce.call(te,ue)}),pe(2,Y,w,z),u(ee,Y)},he=(ee,Y)=>{{var Ve=ue=>{var te=Dt();ae(te,()=>({...n(l),..._}));var Ce=k(te);R(Ce,e,"default",{get builder(){return n(l)}},null),S(te),ne(te,je=>f(je),()=>f()),se(te,je=>{var qe,De;return(De=(qe=n(l)).action)==null?void 0:De.call(qe,je)}),u(ue,te)};W(ee,ue=>{d(),o(),re(()=>d()(o().value))&&ue(Ve)},Y)}};W(X,ee=>{O(w()),d(),o(),re(()=>w()&&d()(o().value))?ee(ze):ee(he,!1)},L)}};W(K,X=>{O($()),d(),o(),re(()=>$()&&d()(o().value))?X(Re):X(me,!1)},U)}};W(G,K=>{O($()),O(w()),d(),o(),re(()=>$()&&w()&&d()(o().value))?K(Z):K(ge,!1)},P)}};W(F,G=>{O(b()),d(),o(),re(()=>b()&&d()(o().value))?G(H):G(fe,!1)},I)}};W(i,F=>{O(C()),d(),o(),re(()=>C()&&d()(o().value))?F(M):F(j,!1)})}u(g,v),de(),A()}const Ut=!0,Er=Object.freeze(Object.defineProperty({__proto__:null,prerender:Ut},Symbol.toStringTag,{value:"Module"}));var Bt=E('<div class="pb-4 pt-0"><!></div>');function Ie(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["class","transition","transitionConfig"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"transition",8,yt),y=x(e,"transitionConfig",24,()=>({duration:200}));ce();const o=Fe(()=>(O(ve),O(p()),re(()=>ve("overflow-hidden text-sm transition-all",p()))));Ht(g,Me({get class(){return n(o)},get transition(){return A()},get transitionConfig(){return y()}},()=>_,{children:(d,l)=>{var b=Bt(),m=k(b);R(m,e,"default",{},null),S(b),u(d,b)},$$slots:{default:!0}})),de()}function ke(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["class","value"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"value",8);ce();const y=Fe(()=>(O(ve),O(p()),re(()=>ve("border-b",p()))));Nt(g,Me({get value(){return A()},get class(){return n(y)}},()=>_,{children:(o,d)=>{var l=B(),b=N(l);R(b,e,"default",{},null),u(o,l)},$$slots:{default:!0}})),de()}var Lt=E("<!> <!>",1);function Te(g,e){const h=V(e,["children","$$slots","$$events","$$legacy"]),_=V(h,["class","level"]);ie(e,!1);let p=x(e,"class",8,void 0),A=x(e,"level",8,3);ce(),Rt(g,{get level(){return A()},class:"flex",children:(y,o)=>{const d=Fe(()=>(O(ve),O(p()),re(()=>ve("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",p()))));Vt(y,Me({get class(){return n(d)}},()=>_,{$$events:{click(l){pt.call(this,e,l)}},children:(l,b)=>{var m=Lt(),$=N(m);R($,e,"default",{},null);var T=q($,2);$t(T,{class:"h-4 w-4 transition-transform duration-200"}),u(l,m)},$$slots:{default:!0}}))},$$slots:{default:!0}}),de()}const Yt=Et;var Zt=E('<meta name="description"/>'),Gt=E("<!> <!>",1),Qt=E("<!> <!>",1),Jt=E("<!> <!>",1),Kt=E("<!> <!>",1),Xt=E("<!> <!> <!> <!>",1),er=E('<tr class="bg-foreground text-background font-bold p-2"><td colspan="3"> </td></tr>'),tr=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),rr=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),ar=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),sr=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),nr=E('<tr class="relative"><td> </td><td class="text-center"><!></td><td class="text-center"><!></td></tr>'),or=E(`<div class="min-h-[70vh] pb-12 pt-16 px-6 bg-white"><div class="max-w-6xl mx-auto text-center"><h1 class="text-4xl md:text-5xl font-light tracking-tight text-gray-900 font-jakarta">Simple, transparent pricing</h1> <p class="text-lg text-gray-600 mt-4 font-light max-w-2xl mx-auto">Choose the plan that's right for your team. Start free and scale as you grow.</p></div> <div class="w-full my-8"><!> <h1 class="text-2xl font-bold text-center mt-24">Pricing FAQ</h1> <div class="flex place-content-center"><!></div> <svg style="display:none" version="2.0"><defs><symbol id="checkcircle" viewBox="0 0 24 24" stroke-width="2" fill="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M16.417 10.283A7.917 7.917 0 1 1 8.5 2.366a7.916 7.916 0 0 1 7.917 7.917zm-4.105-4.498a.791.791 0 0 0-1.082.29l-3.828 6.63-1.733-2.08a.791.791 0 1 0-1.216 1.014l2.459 2.952a.792.792 0 0 0 .608.285.83.83 0 0 0 .068-.003.791.791 0 0 0 .618-.393L12.6 6.866a.791.791 0 0 0-.29-1.081z"></path></symbol></defs></svg> <svg style="display:none" version="2.0"><defs><symbol id="nocircle" viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm4,11H8a1,1,0,0,1,0-2h8a1,1,0,0,1,0,2Z"></path></symbol></defs></svg> <h1 class="text-2xl font-bold text-center mt-16">Plan Features</h1> <h2 class="text-xl text-center mt-1 pb-3">Example feature table</h2> <div class="overflow-visible mx-auto max-w-xl mt-4"><table class="table w-full"><thead class="text-lg sticky top-0 bg-foreground text-background bg-opacity-50 z-10 backdrop-blur"><tr><th></th><th class="text-center">Free</th><th class="text-center">Pro</th></tr></thead><tbody></tbody></table></div></div></div>`);function Mr(g){const e=[{name:"Section 1",header:!0},{name:"Feature 1",freeIncluded:!0,proIncluded:!0},{name:"Feature 2",freeIncluded:!1,proIncluded:!0},{name:"Feature 3",freeString:"3",proString:"Unlimited"},{name:"Section 2",header:!0},{name:"Feature 4",freeIncluded:!0,proIncluded:!0},{name:"Feature 5",freeIncluded:!1,proIncluded:!0}];var h=or();et(b=>{var m=Zt();Xe.title="Pricing",_e(()=>at(m,"content",`Pricing - ${nt}`)),u(b,m)});var _=q(k(h),2),p=k(_);st(p,{callToAction:"Get Started",highlightedPlanId:"pro"});var A=q(p,4),y=k(A);Yt(y,{class:"max-w-xl mx-auto",children:(b,m)=>{var $=Xt(),T=N($);ke(T,{value:"faq1",children:(f,t)=>{var r=Gt(),a=N(r);Te(a,{children:(c,v)=>{le();var i=Q("Is this template free to use?");u(c,i)},$$slots:{default:!0}});var s=q(a,2);Ie(s,{children:(c,v)=>{le();var i=Q("Yup! This template is free to use for any project.");u(c,i)},$$slots:{default:!0}}),u(f,r)},$$slots:{default:!0}});var w=q(T,2);ke(w,{value:"faq2",children:(f,t)=>{var r=Qt(),a=N(r);Te(a,{children:(c,v)=>{le();var i=Q("Why does a free template have a pricing page?");u(c,i)},$$slots:{default:!0}});var s=q(a,2);Ie(s,{children:(c,v)=>{le();var i=Q(`The pricing page is part of the boilerplate. It shows how the
            pricing page integrates into the billing portal and the Stripe
            Checkout flows.`);u(c,i)},$$slots:{default:!0}}),u(f,r)},$$slots:{default:!0}});var z=q(w,2);ke(z,{value:"faq3",children:(f,t)=>{var r=Jt(),a=N(r);Te(a,{children:(c,v)=>{le();var i=Q("What license is the template under?");u(c,i)},$$slots:{default:!0}});var s=q(a,2);Ie(s,{children:(c,v)=>{le();var i=Q("The template is under the MIT license.");u(c,i)},$$slots:{default:!0}}),u(f,r)},$$slots:{default:!0}});var C=q(z,2);ke(C,{value:"Is this template free to use?",children:(f,t)=>{var r=Kt(),a=N(r);Te(a,{children:(c,v)=>{le();var i=Q("Can I try out purchase flows without real a credit card?");u(c,i)},$$slots:{default:!0}});var s=q(a,2);Ie(s,{children:(c,v)=>{le();var i=Q(`You can use the credit card number 4242 4242 4242 4242 with any
            future expiry date to test the payment and upgrade flows.`);u(c,i)},$$slots:{default:!0}}),u(f,r)},$$slots:{default:!0}}),u(b,$)},$$slots:{default:!0}}),S(A);var o=q(A,10),d=k(o),l=q(k(d));tt(l,5,()=>e,rt,(b,m)=>{var $=B(),T=N($);{var w=C=>{var f=er(),t=k(f),r=k(t,!0);S(t),S(f),_e(()=>Pe(r,n(m).name)),u(C,f)},z=C=>{var f=nr(),t=k(f),r=k(t,!0);S(t);var a=q(t),s=k(a);{var c=I=>{var H=Q();_e(()=>Pe(H,n(m).freeString)),u(I,H)},v=(I,H)=>{{var fe=P=>{var Z=tr();u(P,Z)},G=P=>{var Z=rr();u(P,Z)};W(I,P=>{n(m).freeIncluded?P(fe):P(G,!1)},H)}};W(s,I=>{n(m).freeString?I(c):I(v,!1)})}S(a);var i=q(a),M=k(i);{var j=I=>{var H=Q();_e(()=>Pe(H,n(m).proString)),u(I,H)},F=(I,H)=>{{var fe=P=>{var Z=ar();u(P,Z)},G=P=>{var Z=sr();u(P,Z)};W(I,P=>{n(m).proIncluded?P(fe):P(G,!1)},H)}};W(M,I=>{n(m).proString?I(j):I(F,!1)})}S(i),S(f),_e(()=>Pe(r,n(m).name)),u(C,f)};W(T,C=>{n(m).header?C(w):C(z,!1)})}u(b,$)}),S(l),S(d),S(o),S(_),S(h),u(g,h)}export{Mr as component,Er as universal};
