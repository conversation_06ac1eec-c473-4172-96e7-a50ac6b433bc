import"../chunks/CWj6FrbW.js";import{p as r,f as s,s as n,e as c,a as u,g as l,$ as i}from"../chunks/wnqW1tdD.js";import{h as d}from"../chunks/CDPCzm7q.js";import{S as m}from"../chunks/udb0C_eZ.js";import{d as p}from"../chunks/B_9MMhPz.js";var f=s('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function y(o,e){r(e,!0);var t=f();d(g=>{i.title="Delete Account"});var a=n(c(t),2);m(a,{get data(){return e.data.form},get schema(){return p},title:"Delete Account",editable:!0,dangerous:!0,message:"Deleting your account can not be undone.",saveButtonTitle:"Delete Account",successTitle:"Account queued for deletion",successBody:"Your account will be deleted shortly.",formTarget:"/api?/deleteAccount",fields:[{id:"currentPassword",label:"Current Password",initialValue:"",inputType:"password"}]}),u(o,t),l()}export{y as component};
