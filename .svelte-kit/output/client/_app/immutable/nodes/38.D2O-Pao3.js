import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{f as a,a as i,n as o}from"../chunks/wnqW1tdD.js";var r=a(`<p class="lead">How to use this template you to bootstrap your own site.</p> <p>We've written a detailed blog post about how we took this template, and
  created a real <a href="https://criticalmoments.io" target="_blank">App company website</a>. Topics include:</p> <ul><li>Optimizing the stack for performance and developer productivity</li> <li>Creating rich interactive animations with Svelte</li> <li>Creating pixel perfect designs without rasterization</li> <li>Speed measurements: how we kept it small and lightning fast</li></ul> <a href="https://criticalmoments.io/blog/how_we_built_our_marketing_page" target="_blank">Read the Blog Post</a> <p>The blog post is over on <a href="https://criticalmoments.io" target="_blank">criticalmoments.io</a>, a page which uses this boilerplate as a starting point.</p> <p>If you are looking for examples of blog posts with rich content rendered
  inside this template, checkout the other demo posts <a href="/blog" class="link">here</a>.</p>`,1);function p(t){var e=r();o(10),i(t,e)}export{p as component};
