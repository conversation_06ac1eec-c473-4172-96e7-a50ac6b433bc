import"../chunks/CWj6FrbW.js";import{p as o,f as r,s as i,e as n,a as d,g as l,$ as m}from"../chunks/wnqW1tdD.js";import{h as p}from"../chunks/CDPCzm7q.js";import{c as u}from"../chunks/B_9MMhPz.js";import{S as w}from"../chunks/udb0C_eZ.js";var c=r('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function v(a,s){o(s,!0),console.log({...s.data.session});var e=c();p(f=>{m.title="Reset Password"});var t=i(n(e),2);w(t,{get data(){return s.data.form},get schema(){return u},title:"Reset Password",editable:!0,saveButtonTitle:"Reset Password",successTitle:"Password Changed",successBody:"On next sign in, use your new password.",formTarget:"/api?/updatePassword",fields:[{id:"newPassword1",label:"New Password",initialValue:"",inputType:"password"},{id:"newPassword2",label:"Confirm New Password",initialValue:"",inputType:"password"}]}),d(a,e),l()}export{v as component};
