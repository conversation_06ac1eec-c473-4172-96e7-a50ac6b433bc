import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{p as A,f as v,t as o,a as i,g as B,s as d,c as e,$ as I,r as t,n as L,e as O,d as U,j as l}from"../chunks/wnqW1tdD.js";import{h as z,s as n}from"../chunks/CDPCzm7q.js";import{e as E,i as F}from"../chunks/CsnEE4l9.js";import{s as G}from"../chunks/rh_XW2Tv.js";import{i as H}from"../chunks/BxG_UISn.js";import{b,s as J}from"../chunks/n8BDlHG4.js";import{C as K,a as M}from"../chunks/Dk7oig4_.js";var N=v('<meta name="description" content="Our blog posts."/>'),Q=v('<div class="flex-none w-6 md:w-32 bg-secondary"></div> <div class="py-6 px-6"><div class="text-xl"> </div> <div class="text-sm text-accent"> </div> <div class="text-muted-foreground"> </div></div>',1),R=v("<a><!></a>"),T=v('<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto"><div class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"><div class="text-center leading-relaxed font-bold text-primary"> </div> <a href="/blog/rss.xml" target="_blank" rel="noreferrer"><img class="flex-none w-5 h-5 object-contain" src="/images/rss.svg" alt="rss feed"/></a></div> <div class="text-lg text-center">A demo blog with sample content.</div> <!></div>');function ie($,y){A(y,!1),H();var m=T();z(x=>{var a=N();o(()=>I.title=b.name),i(x,a)});var c=e(m),_=e(c),w=e(_,!0);t(_),L(2),t(c);var C=d(c,4);E(C,1,()=>J,F,(x,a)=>{var r=R(),P=e(r);K(P,{class:"my-6",children:(j,V)=>{M(j,{class:"shadow-xl p-6 flex flex-row overflow-hidden",children:(k,W)=>{var g=Q(),u=d(O(g),2),p=e(u),D=e(p,!0);t(p);var f=d(p,2),S=e(f,!0);t(f);var h=d(f,2),q=e(h,!0);t(h),t(u),o(s=>{n(D,l(a).title),n(S,s),n(q,l(a).description)},[()=>{var s;return(s=l(a).parsedDate)==null?void 0:s.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}],U),i(k,g)},$$slots:{default:!0}})},$$slots:{default:!0}}),t(r),o(()=>G(r,"href",l(a).link)),i(x,r)}),t(m),o(()=>n(w,b.name)),i($,m),B()}export{ie as component};
