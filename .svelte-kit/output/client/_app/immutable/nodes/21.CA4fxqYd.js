import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as ht}from"../chunks/CfBaWyh2.js";import{b as We,e as fe,a as y,p as ot,f as h,c as t,r as e,n as V,s as o,t as P,j as r,J as Mr,o as Ie,k as c,g as at,a7 as _t,l as wt,h as kt,$ as $t,m as lr,i as N,d as dt,aU as St}from"../chunks/wnqW1tdD.js";import{s as D,e as K,r as pt,h as Mt}from"../chunks/CDPCzm7q.js";import{i as O}from"../chunks/BjRbZGyQ.js";import{e as pe,i as Ke}from"../chunks/CsnEE4l9.js";import{h as mt}from"../chunks/D8kkBG2G.js";import{c as Ct}from"../chunks/ojdN50pv.js";import{r as me,b as st,c as gr,s as et}from"../chunks/rh_XW2Tv.js";import{s as dr}from"../chunks/Bz0_kaay.js";import{t as Pe}from"../chunks/sBNKiCwy.js";import{b as Hr}from"../chunks/CJ-FD9ng.js";import{i as At}from"../chunks/BxG_UISn.js";import{a as Dt,s as ct}from"../chunks/D5ITLM2v.js";import{w as Lt}from"../chunks/BvpDAKCq.js";import{p as Kt}from"../chunks/D0xeg8nZ.js";import{s as tt,f as ft}from"../chunks/CiB29Aqe.js";import{l as He,s as Ze,p as _e}from"../chunks/Cmdkv-7M.js";import{S as gt}from"../chunks/DrB3LJpu.js";import{C as bt}from"../chunks/ZAWXEYb0.js";import{F as it,C as nt}from"../chunks/BzUNhcOB.js";import{L as Ye}from"../chunks/CDkJ1nAx.js";import{a as Ge,T as Be,S as Et}from"../chunks/Ds9Kfk49.js";import{D as lt,C as yt,a as xt}from"../chunks/D3xCqHYb.js";import{X as Rt}from"../chunks/BAo0SQ6-.js";import{s as qe}from"../chunks/BDqVm3Gq.js";import{I as Je}from"../chunks/CX_t0Ed_.js";import{C as Ot,U as jt,B as vt}from"../chunks/TU_NZaLw.js";function Tt(Cr,F){const _=He(F,["children","$$slots","$$events","$$legacy"]),Ar=[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}]];Je(Cr,Ze({name:"book-open"},()=>_,{get iconNode(){return Ar},children:(J,ar)=>{var G=We(),j=fe(G);qe(j,F,"default",{},null),y(J,G)},$$slots:{default:!0}}))}function ut(Cr,F){const _=He(F,["children","$$slots","$$events","$$legacy"]),Ar=[["circle",{cx:"12",cy:"12",r:"10"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16"}]];Je(Cr,Ze({name:"circle-alert"},()=>_,{get iconNode(){return Ar},children:(J,ar)=>{var G=We(),j=fe(G);qe(j,F,"default",{},null),y(J,G)},$$slots:{default:!0}}))}function Ft(Cr,F){const _=He(F,["children","$$slots","$$events","$$legacy"]),Ar=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M12 18v-6"}],["path",{d:"m9 15 3 3 3-3"}]];Je(Cr,Ze({name:"file-down"},()=>_,{get iconNode(){return Ar},children:(J,ar)=>{var G=We(),j=fe(G);qe(j,F,"default",{},null),y(J,G)},$$slots:{default:!0}}))}function zt(Cr,F){const _=He(F,["children","$$slots","$$events","$$legacy"]),Ar=[["path",{d:"M5 12h14"}],["path",{d:"M12 5v14"}]];Je(Cr,Ze({name:"plus"},()=>_,{get iconNode(){return Ar},children:(J,ar)=>{var G=We(),j=fe(G);qe(j,F,"default",{},null),y(J,G)},$$slots:{default:!0}}))}var Ut=h('<button class="p-3 border-2 text-left hover:border-primary transition-all" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between"><div><p class="font-medium text-sm" style="color: var(--foreground);"> </p> <p class="text-xs mt-1" style="color: var(--muted-foreground);"> </p></div> <!></div></button>'),It=h('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-2 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Industry/Niche</label> <input placeholder="e.g., E-commerce, SaaS, Healthcare" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div></div> <div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),Pt=h("<!> Discovering Keywords...",1),Gt=h("<!> Discover Niche Keywords",1),Nt=h('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),Vt=h('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> </h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competition</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">CPC</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),Bt=h('<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Niche Keyword Discovery</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find untapped long-tail keywords in your specific niche</p></div></div></div> <div class="grid md:grid-cols-2 gap-3"></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Seed Keywords (comma-separated, max 20)</label> <textarea placeholder="e.g., organic coffee, fair trade coffee beans, specialty coffee roasters" class="w-full p-3 border-2 min-h-[80px]" style="background: var(--background); border-color: var(--border); color: var(--foreground);"></textarea></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!></div>');function Yt(Cr,F){ot(F,!0);let _=_e(F,"isLoading",3,!1),Ar=_e(F,"discoveredKeywords",19,()=>[]),J=Mr(""),ar=Mr(""),G=Mr("United States"),j=Mr(100),M=Mr(1e4),Q=Mr(50),cr=Mr(!1);const Zr=[{name:"E-commerce",seeds:"organic skincare, natural beauty products, vegan cosmetics"},{name:"SaaS",seeds:"project management, team collaboration, task tracking"},{name:"Local Business",seeds:"coffee shop, specialty coffee, local cafe"},{name:"Health & Wellness",seeds:"yoga classes, meditation app, wellness coaching"}];function qr(){const f=r(J).split(",").map(L=>L.trim()).filter(L=>L.length>0);if(f.length===0)return;const C={industry:r(ar),location:r(G),volumeRange:{min:r(j),max:r(M)},maxDifficulty:r(Q)};F.onAnalyze(f,C)}function X(f){c(J,f,!0)}function jr(){const f=r(kr).map(C=>C.keyword).join(`
`);navigator.clipboard.writeText(f)}function ge(){const f=["Keyword","Search Volume","Difficulty","Competition","CPC","Opportunity Score"],C=r(kr).map(z=>{var sr;return[z.keyword,z.search_volume,z.difficulty,z.competition,z.cpc.toFixed(2),((sr=z.opportunity_score)==null?void 0:sr.toFixed(2))||""]}),L=[f,...C].map(z=>z.join(",")).join(`
`),rr=new Blob([L],{type:"text/csv"}),tr=URL.createObjectURL(rr),H=document.createElement("a");H.href=tr,H.download=`niche-keywords-${new Date().toISOString().split("T")[0]}.csv`,H.click(),URL.revokeObjectURL(tr)}const Tr=Ie(()=>Ar().map(f=>({...f,opportunity_score:f.search_volume/(f.difficulty+1)*(f.competition==="LOW"?2:f.competition==="MEDIUM"?1:.5)}))),kr=Ie(()=>r(Tr).filter(f=>f.search_volume>=r(j)&&f.search_volume<=r(M)).filter(f=>f.difficulty<=r(Q)).sort((f,C)=>(C.opportunity_score||0)-(f.opportunity_score||0)));var Pr=Bt(),er=t(Pr),we=t(er),ie=t(we),br=t(ie);gt(br,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),e(ie),V(2),e(we),e(er);var Gr=o(er,2);pe(Gr,21,()=>Zr,Ke,(f,C)=>{var L=Ut(),rr=t(L),tr=t(rr),H=t(tr),z=t(H,!0);e(H);var sr=o(H,2),$r=t(sr,!0);e(sr),e(tr);var zr=o(tr,2);bt(zr,{class:"w-4 h-4",style:"color: var(--muted-foreground);"}),e(rr),e(L),P(()=>{L.disabled=_(),D(z,r(C).name),D($r,r(C).seeds)}),K("click",L,()=>X(r(C).seeds)),y(f,L)}),e(Gr);var Fr=o(Gr,2),Nr=o(t(Fr),2);pt(Nr),e(Fr);var Jr=o(Fr,2),Qr=t(Jr),Xr=t(Qr);it(Xr,{class:"w-4 h-4"});var Vr=o(Xr,2);e(Qr);var ke=o(Qr,2);{var $e=f=>{var C=It(),L=t(C),rr=t(L),tr=o(t(rr),2);me(tr),e(rr);var H=o(rr,2),z=o(t(H),2),sr=t(z);sr.value=sr.__value="United States";var $r=o(sr);$r.value=$r.__value="United Kingdom";var zr=o($r);zr.value=zr.__value="Canada";var vr=o(zr);vr.value=vr.__value="Australia";var ee=o(vr);ee.value=ee.__value="Global",e(z),e(H),e(L);var ne=o(L,2),te=t(ne),Z=o(t(te),2);me(Z),e(te);var Dr=o(te,2),ur=o(t(Dr),2);me(ur),e(Dr);var be=o(Dr,2),yr=o(t(be),2);me(yr),e(be),e(ne),e(C),P(()=>{tr.disabled=_(),z.disabled=_(),Z.disabled=_(),ur.disabled=_(),yr.disabled=_()}),Hr(tr,()=>r(ar),xr=>c(ar,xr)),st(z,()=>r(G),xr=>c(G,xr)),Hr(Z,()=>r(j),xr=>c(j,xr)),Hr(ur,()=>r(M),xr=>c(M,xr)),Hr(yr,()=>r(Q),xr=>c(Q,xr)),y(f,C)};O(ke,f=>{r(cr)&&f($e)})}e(Jr);var re=o(Jr,2),Ee=t(re);{var Re=f=>{var C=Pt(),L=fe(C);Ye(L,{class:"w-4 h-4 animate-spin"}),V(),y(f,C)},Te=f=>{var C=Gt(),L=fe(C);Ge(L,{class:"w-4 h-4"}),V(),y(f,C)};O(Ee,f=>{_()?f(Re):f(Te,!1)})}e(re);var Fe=o(re,2);{var Se=f=>{var C=Vt(),L=t(C),rr=t(L),tr=t(rr);e(rr);var H=o(rr,2),z=t(H),sr=t(z);nt(sr,{class:"w-3 h-3"}),V(),e(z);var $r=o(z,2),zr=t($r);lt(zr,{class:"w-3 h-3"}),V(),e($r),e(H),e(L);var vr=o(L,2),ee=t(vr),ne=o(t(ee));pe(ne,21,()=>r(kr),Ke,(te,Z)=>{var Dr=Nt(),ur=t(Dr),be=t(ur,!0);e(ur);var yr=o(ur),xr=t(yr,!0);e(yr);var le=o(yr),Me=t(le),Ce=t(Me,!0);e(Me),e(le);var ye=o(le),i=t(ye),u=t(i,!0);e(i),e(ye);var g=o(ye),E=t(g);e(g);var A=o(g),B=t(A),a=t(B,!0);e(B),e(A),e(Dr),P((s,v,d)=>{D(be,r(Z).keyword),D(xr,s),gr(Me,`background: ${r(Z).difficulty<30?"var(--primary)":r(Z).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),D(Ce,r(Z).difficulty),gr(i,`color: ${r(Z).competition==="LOW"?"var(--primary)":r(Z).competition==="MEDIUM"?"#fbbf24":"#ef4444"};`),D(u,r(Z).competition),D(E,`$${v??""}`),D(a,d)},[()=>r(Z).search_volume.toLocaleString(),()=>r(Z).cpc.toFixed(2),()=>{var s;return((s=r(Z).opportunity_score)==null?void 0:s.toFixed(1))||"-"}]),y(te,Dr)}),e(ne),e(ee),e(vr),e(C),P(()=>D(tr,`Discovered Keywords (${r(kr).length??""})`)),K("click",z,jr),K("click",$r,ge),y(f,C)};O(Fe,f=>{r(kr).length>0&&f(Se)})}e(Pr),P(f=>{Nr.disabled=_(),Qr.disabled=_(),dr(Vr,1,`text-xs ${r(cr)?"rotate-180":""} transition-transform`),re.disabled=f},[()=>!r(J).trim()||_()]),Hr(Nr,()=>r(J),f=>c(J,f)),K("click",Qr,()=>c(cr,!r(cr))),K("click",re,qr),y(Cr,Pr),at()}var Wt=h('<button class="btn-secondary p-3"><!></button>'),Ht=h('<div class="flex gap-2"><input class="flex-1 p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/> <!></div>'),Zt=h('<button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Add Competitor</button>'),qt=h('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),Jt=h("<!> Analyzing Competitor Gaps...",1),Qt=h("<!> Analyze Competitor Gaps",1),Xt=h("<div><!></div>"),ro=h('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),eo=h('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h5 class="text-sm font-bold mb-1"> </h5> <p class="text-xs"> </p> <!></div></div>'),to=h('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><h4 class="font-bold mb-4" style="color: var(--foreground);">Analyzing Competitor Gaps</h4> <div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div>'),oo=h('<div class="flex items-center justify-between p-2 border" style="background: var(--background); border-color: var(--border);"><span class="text-sm font-medium" style="color: var(--foreground);"> </span> <div class="flex items-center gap-3 text-xs"><span style="color: var(--muted-foreground);"> </span> <span class="px-2 py-1 rounded" style="background: var(--primary); color: var(--primary-foreground);"> </span></div></div>'),ao=h('<div class="border-2 p-4" style="background: var(--accent); border-color: var(--border);"><div class="flex items-center gap-2 mb-3"><!> <h4 class="font-bold" style="color: var(--accent-foreground);">Quick Win Opportunities</h4></div> <div class="space-y-2"></div></div>'),so=h(`<div class="border-2 p-4 mb-4" style="background: #fef3c7; border-color: #f59e0b;"><div class="flex items-center gap-2 mb-2"><!> <h4 class="font-bold text-sm" style="color: #78350f;">Demo Data Notice</h4></div> <p class="text-sm" style="color: #78350f;">You're viewing sample data. To get real keyword analysis for your
        domains, please configure your DataForSEO API credentials in the
        environment settings.</p> <p class="text-xs mt-2" style="color: #78350f;">The keywords shown below are generic examples and not specific to your
        actual domain.</p></div>`),io=h('<span class="text-xs font-normal px-2 py-1 rounded ml-2" style="background: #fef3c7; color: #78350f;">DEMO DATA</span>'),no=h('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs px-2 py-1 rounded"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),lo=h('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> <!></h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Gap Type</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competitor Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Your Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),co=h('<div class="border-2 p-6 mt-6" style="background: var(--card); border-color: var(--border);"><h3 class="text-lg font-bold mb-4" style="color: var(--foreground);">📊 Complete SEO Strategy Analysis</h3> <div class="prose prose-sm max-w-none" style="color: var(--muted-foreground);"><div class="ai-response-content"><!></div></div></div>'),vo=h(`<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Competitor Gap Analysis</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find keyword opportunities your competitors are ranking for</p></div></div></div> <div class="space-y-4"><div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Your Domain</label> <input placeholder="example.com" class="w-full p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Competitor Domains (up to 3)</label> <div class="space-y-2"><!> <!></div></div></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Analysis Type</label> <div class="grid md:grid-cols-3 gap-3"><button><p class="font-medium text-sm" style="color: var(--foreground);">Missing Keywords</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords competitors rank for but you don't</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">Lower Rankings</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords where competitors outrank you</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">All Gaps</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Show all keyword opportunities</p></button></div></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!> <!> <!> <!> <!></div>`);function uo(Cr,F){ot(F,!0);let _=_e(F,"isLoading",3,!1),Ar=_e(F,"gapKeywords",19,()=>[]),J=_e(F,"progressSteps",19,()=>[]),ar=_e(F,"currentProgress",3,0),G=_e(F,"isMockData",3,!1),j=_e(F,"aiResponse",3,""),M=Mr(""),Q=Mr(_t([""])),cr=Mr("United States"),Zr=Mr(500),qr=Mr(70),X=Mr("all"),jr=Mr(!1);function ge(){r(Q).length<3&&c(Q,[...r(Q),""],!0)}function Tr(i){c(Q,r(Q).filter((u,g)=>g!==i),!0)}function kr(){const i=r(Q).filter(g=>g.trim().length>0);if(!r(M).trim()||i.length===0)return;const u={yourDomain:r(M).trim(),competitors:i,location:r(cr),minVolume:r(Zr),maxDifficulty:r(qr),gapType:r(X)};F.onAnalyze(u)}function Pr(){const i=r(br).map(u=>u.keyword).join(`
`);navigator.clipboard.writeText(i)}function er(i){return i.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold mb-2 mt-4" style="color: var(--foreground);">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold mb-3 mt-6" style="color: var(--foreground);">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold mb-4" style="color: var(--foreground);">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 list-disc">$1</li>').replace(/\|(.+)\|/g,(g,E)=>{const A=E.split("|").map(s=>s.trim()).filter(s=>s.length>0);return E.includes("---")?"":`<tr>${A.map(s=>`<td class="border px-3 py-2 text-sm" style="border-color: var(--border);">${s}</td>`).join("")}</tr>`}).replace(/(<tr>.*<\/tr>)/gs,'<table class="w-full border-collapse border mt-4 mb-4" style="border-color: var(--border);">$1</table>').replace(/\n/g,"<br>")}function we(){const i=["Keyword","Search Volume","Difficulty","Competition","CPC","Competitor Position","Your Position","Gap Type","Opportunity Score"],u=r(br).map(a=>{var s;return[a.keyword,a.search_volume,a.difficulty,a.competition,a.cpc.toFixed(2),a.competitor_position,a.your_position||"Not ranking",a.gap_type,((s=a.opportunity_score)==null?void 0:s.toFixed(2))||""]}),g=[i,...u].map(a=>a.join(",")).join(`
`),E=new Blob([g],{type:"text/csv"}),A=URL.createObjectURL(E),B=document.createElement("a");B.href=A,B.download=`competitor-gap-analysis-${new Date().toISOString().split("T")[0]}.csv`,B.click(),URL.revokeObjectURL(A)}const ie=Ie(()=>Ar().map(i=>({...i,opportunity_score:i.search_volume/(i.difficulty+1)*(i.gap_type==="missing"?2:1)}))),br=Ie(()=>r(ie).filter(i=>i.search_volume>=r(Zr)).filter(i=>i.difficulty<=r(qr)).filter(i=>r(X)==="all"||i.gap_type===r(X)).sort((i,u)=>(u.opportunity_score||0)-(i.opportunity_score||0))),Gr=Ie(()=>r(br).filter(i=>i.search_volume>1e3&&i.difficulty<30).slice(0,5));var Fr=vo(),Nr=t(Fr),Jr=t(Nr),Qr=t(Jr),Xr=t(Qr);Be(Xr,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),e(Qr),V(2),e(Jr),e(Nr);var Vr=o(Nr,2),ke=t(Vr),$e=o(t(ke),2);me($e),e(ke);var re=o(ke,2),Ee=o(t(re),2),Re=t(Ee);pe(Re,17,()=>r(Q),Ke,(i,u,g)=>{var E=Ht(),A=t(E);me(A),et(A,"placeholder",`competitor${g+1}.com`);var B=o(A,2);{var a=s=>{var v=Wt(),d=t(v);Rt(d,{class:"w-4 h-4"}),e(v),P(()=>v.disabled=_()),K("click",v,()=>Tr(g)),y(s,v)};O(B,s=>{r(Q).length>1&&s(a)})}e(E),P(()=>A.disabled=_()),Hr(A,()=>r(Q)[g],s=>r(Q)[g]=s),y(i,E)});var Te=o(Re,2);{var Fe=i=>{var u=Zt(),g=t(u);zt(g,{class:"w-4 h-4"}),V(),e(u),P(()=>u.disabled=_()),K("click",u,ge),y(i,u)};O(Te,i=>{r(Q).length<3&&i(Fe)})}e(Ee),e(re),e(Vr);var Se=o(Vr,2),f=o(t(Se),2),C=t(f),L=o(C,2),rr=o(L,2);e(f),e(Se);var tr=o(Se,2),H=t(tr),z=t(H);it(z,{class:"w-4 h-4"});var sr=o(z,2);e(H);var $r=o(H,2);{var zr=i=>{var u=qt(),g=t(u),E=t(g),A=o(t(E),2),B=t(A);B.value=B.__value="United States";var a=o(B);a.value=a.__value="United Kingdom";var s=o(a);s.value=s.__value="Canada";var v=o(s);v.value=v.__value="Australia";var d=o(v);d.value=d.__value="Global",e(A),e(E);var n=o(E,2),m=o(t(n),2);me(m),e(n);var p=o(n,2),l=o(t(p),2);me(l),e(p),e(g),e(u),P(()=>{A.disabled=_(),m.disabled=_(),l.disabled=_()}),st(A,()=>r(cr),b=>c(cr,b)),Hr(m,()=>r(Zr),b=>c(Zr,b)),Hr(l,()=>r(qr),b=>c(qr,b)),y(i,u)};O($r,i=>{r(jr)&&i(zr)})}e(tr);var vr=o(tr,2),ee=t(vr);{var ne=i=>{var u=Jt(),g=fe(u);Ye(g,{class:"w-4 h-4 animate-spin"}),V(),y(i,u)},te=i=>{var u=Qt(),g=fe(u);Ge(g,{class:"w-4 h-4"}),V(),y(i,u)};O(ee,i=>{_()?i(ne):i(te,!1)})}e(vr);var Z=o(vr,2);{var Dr=i=>{var u=to(),g=o(t(u),2);pe(g,21,J,d=>d.id,(d,n)=>{var m=eo(),p=t(m),l=t(p);{var b=x=>{var q=Xt(),nr=t(q);yt(nr,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),e(q),Pe(3,q,()=>ft,()=>({duration:200})),y(x,q)},U=(x,q)=>{{var nr=Lr=>{Ye(Lr,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},oe=Lr=>{xt(Lr,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};O(x,Lr=>{r(n).status==="active"?Lr(nr):Lr(oe,!1)},q)}};O(l,x=>{r(n).status==="completed"?x(b):x(U,!1)})}e(p);var $=o(p,2),R=t($),w=t(R,!0);e(R);var S=o(R,2),Y=t(S,!0);e(S);var pr=o(S,2);{var ir=x=>{var q=ro(),nr=t(q);e(q),P(()=>gr(nr,`background: var(--primary); width: ${r(n).progress??""}%`)),y(x,q)};O(pr,x=>{r(n).status==="active"&&r(n).progress&&x(ir)})}e($),e(m),P(()=>{gr(R,`color: ${r(n).status==="pending"?"var(--muted-foreground)":"var(--foreground)"};
                         opacity: ${r(n).status==="pending"?"0.5":"1"}`),D(w,r(n).title),gr(S,`color: var(--muted-foreground);
                        opacity: ${r(n).status==="pending"?"0.5":"1"}`),D(Y,r(n).description)}),Pe(3,m,()=>tt,()=>({duration:300})),y(d,m)}),e(g);var E=o(g,2),A=t(E),B=o(t(A),2),a=t(B);e(B),e(A);var s=o(A,2),v=t(s);e(s),e(E),e(u),P(()=>{D(a,`${ar()??""}%`),gr(v,`background: linear-gradient(to right, var(--primary), var(--accent)); 
                      width: ${ar()??""}%`)}),Pe(3,u,()=>tt,()=>({duration:300})),y(i,u)};O(Z,i=>{_()&&J().length>0&&i(Dr)})}var ur=o(Z,2);{var be=i=>{var u=ao(),g=t(u),E=t(g);ut(E,{class:"w-5 h-5",style:"color: var(--accent-foreground);"}),V(2),e(g);var A=o(g,2);pe(A,21,()=>r(Gr),Ke,(B,a)=>{var s=oo(),v=t(s),d=t(v,!0);e(v);var n=o(v,2),m=t(n),p=t(m);e(m);var l=o(m,2),b=t(l);e(l),e(n),e(s),P(U=>{D(d,r(a).keyword),D(p,`Vol: ${U??""}`),D(b,`Difficulty: ${r(a).difficulty??""}`)},[()=>r(a).search_volume.toLocaleString()]),y(B,s)}),e(A),e(u),y(i,u)};O(ur,i=>{r(Gr).length>0&&!_()&&i(be)})}var yr=o(ur,2);{var xr=i=>{var u=so(),g=t(u),E=t(g);ut(E,{class:"w-5 h-5",style:"color: #f59e0b;"}),V(2),e(g),V(4),e(u),y(i,u)};O(yr,i=>{G()&&r(br).length>0&&!_()&&i(xr)})}var le=o(yr,2);{var Me=i=>{var u=lo(),g=t(u),E=t(g),A=t(E),B=o(A);{var a=U=>{var $=io();y(U,$)};O(B,U=>{G()&&U(a)})}e(E);var s=o(E,2),v=t(s),d=t(v);nt(d,{class:"w-3 h-3"}),V(),e(v);var n=o(v,2),m=t(n);lt(m,{class:"w-3 h-3"}),V(),e(n),e(s),e(g);var p=o(g,2),l=t(p),b=o(t(l));pe(b,21,()=>r(br),Ke,(U,$)=>{var R=no(),w=t(R),S=t(w,!0);e(w);var Y=o(w),pr=t(Y,!0);e(Y);var ir=o(Y),x=t(ir),q=t(x,!0);e(x),e(ir);var nr=o(ir),oe=t(nr),Lr=t(oe,!0);e(oe),e(nr);var Br=o(nr),xe=t(Br);e(Br);var Ur=o(Br),Ae=t(Ur,!0);e(Ur);var De=o(Ur),Ne=t(De),Qe=t(Ne,!0);e(Ne),e(De),e(R),P((Oe,I)=>{D(S,r($).keyword),D(pr,Oe),gr(x,`background: ${r($).difficulty<30?"var(--primary)":r($).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),D(q,r($).difficulty),gr(oe,`background: ${r($).gap_type==="missing"?"#ef4444":"#fbbf24"}; color: white;`),D(Lr,r($).gap_type==="missing"?"Not Ranking":"Lower Rank"),D(xe,`#${r($).competitor_position??""}`),D(Ae,r($).your_position?`#${r($).your_position}`:"-"),D(Qe,I)},[()=>r($).search_volume.toLocaleString(),()=>{var Oe;return((Oe=r($).opportunity_score)==null?void 0:Oe.toFixed(1))||"-"}]),y(U,R)}),e(b),e(l),e(p),e(u),P(()=>D(A,`Gap Analysis Results (${r(br).length??""}) `)),K("click",v,Pr),K("click",n,we),y(i,u)};O(le,i=>{r(br).length>0&&!_()&&i(Me)})}var Ce=o(le,2);{var ye=i=>{var u=co(),g=o(t(u),2),E=t(g),A=t(E);mt(A,()=>er(j())),e(E),e(g),e(u),y(i,u)};O(Ce,i=>{j()&&j().trim().length>0&&!_()&&i(ye)})}e(Fr),P(i=>{$e.disabled=_(),dr(C,1,`p-3 border-2 text-left ${r(X)==="missing"?"border-primary":""}`),gr(C,`background: var(--card); border-color: ${r(X)==="missing"?"var(--primary)":"var(--border)"};`),C.disabled=_(),dr(L,1,`p-3 border-2 text-left ${r(X)==="lower_rank"?"border-primary":""}`),gr(L,`background: var(--card); border-color: ${r(X)==="lower_rank"?"var(--primary)":"var(--border)"};`),L.disabled=_(),dr(rr,1,`p-3 border-2 text-left ${r(X)==="all"?"border-primary":""}`),gr(rr,`background: var(--card); border-color: ${r(X)==="all"?"var(--primary)":"var(--border)"};`),rr.disabled=_(),H.disabled=_(),dr(sr,1,`text-xs ${r(jr)?"rotate-180":""} transition-transform`),vr.disabled=i},[()=>!r(M).trim()||r(Q).filter(i=>i.trim()).length===0||_()]),Hr($e,()=>r(M),i=>c(M,i)),K("click",C,()=>c(X,"missing")),K("click",L,()=>c(X,"lower_rank")),K("click",rr,()=>c(X,"all")),K("click",H,()=>c(jr,!r(jr))),K("click",vr,kr),y(Cr,Fr),at()}var po=h('<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group" style="background: var(--card); border-color: var(--border);"><div class="flex items-center gap-3 mb-3"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform" style="background: var(--primary); border-color: var(--border);"><!></div> <h4 class="font-bold text-sm" style="color: var(--foreground);"> </h4></div> <p class="text-xs mb-3" style="color: var(--muted-foreground);"> </p> <div class="text-xs font-mono p-2 border-2 rounded" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"> </div></button>'),mo=h('<div class="text-center py-12"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2" style="background: var(--muted); border-color: var(--border);"><!></div> <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">Start Your SEO Research</h3> <p class="font-medium mb-6" style="color: var(--muted-foreground);">Get keyword analysis and SEO strategy for any business</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"></div></div>'),fo=h('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown"><!> Download</button>'),go=h('<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);"> </p></div>'),bo=h('<div class="p-6 border-2 mb-4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none"><!></div></div> <div class="flex flex-wrap gap-2 mb-4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Generate Blog Outline</button></div>',1),yo=h('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"><!></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1"><!> <span class="text-xs" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),xo=h("<div><!></div>"),ho=h('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),_o=h('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h4 class="text-sm font-bold mb-1"> </h4> <p class="text-xs"> </p> <!></div></div>'),wo=h('<div class="flex gap-4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">SEO Strategist</span> <span class="text-xs" style="color: var(--muted-foreground);">Working on your analysis...</span></div> <div class="p-6 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div></div></div>'),ko=h('<div class="grid md:grid-cols-3 gap-4 p-4 border-2" style="background: var(--muted); border-color: var(--border);"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Audience</label> <select class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>Select audience</option><option>B2B SaaS</option><option>E-commerce</option><option>Local business</option><option>Content creators</option><option>Enterprise</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Region Focus</label> <input placeholder="e.g., US, Europe, Global" class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Funnel Stage</label> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Awareness</button> <button style="border-color: var(--border);">Consideration</button> <button>Decision</button></div></div></div>'),$o=h('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>'),So=h('<div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container"><div class="space-y-6"><!> <!> <!></div></div> <div><div class="flex items-center justify-between" style="margin-bottom: 15px;"><h2 class="text-lg font-bold" style="color: var(--foreground);">SEO Analysis</h2> <div class="flex items-center space-x-2"><span class="text-sm font-medium" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Summary</button> <button style="border-color: var(--border);">Table</button> <button>Blog-ready</button></div></div></div> <div style="margin-bottom: 15px;"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2" style="margin-bottom: 10px;"><!> Prompt Enhancer <span>▼</span></button> <!></div> <div class="flex" style="gap: 15px;"><div class="flex-1 relative"><textarea class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full" style="min-height: 60px;"></textarea></div> <button class="btn-primary px-6 font-bold flex items-center gap-2" style="height: auto; align-self: stretch;"><!> Analyze</button></div></div></div></div>'),Mo=h('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Co=h('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Ao=h('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black" style="color: var(--foreground);">Lexi - SEO Strategist</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Powered by Lexi – your AI SEO Sidekick</p></div></div> <div class="flex items-center space-x-4"><div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Chat Mode</button> <button style="border-color: var(--border);"><!> Niche Discovery</button> <button style="border-color: var(--border);"><!> Gap Analysis</button></div> <div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Keyword Intelligence</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">SEO Agent</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><!></div></div>');function aa(Cr,F){ot(F,!1);const[_,Ar]=Dt(),J=()=>ct(Kt,"$page",_),ar=()=>ct(G,"$messages",_),G=Lt([]);let j=lr(""),M=lr(!1),Q="",cr=lr("summary"),Zr=0,qr=lr(""),X=lr(!1),jr=lr(""),ge=lr(""),Tr=lr("awareness"),kr=lr([]),Pr=lr(0),er=lr("chat"),we=lr([]),ie=lr([]),br=lr([]),Gr=lr(0),Fr=lr(!1),Nr=lr("");const Jr=["Find long-tail keywords for organic skincare...","Analyze competitor keywords for project management tools...","Discover niche keywords for sustainable fashion brands...","Research local SEO keywords for coffee shops in Seattle..."],Qr=[{icon:Et,title:"Niche Keyword Discovery",description:"Find untapped long-tail keywords in your specific niche",prompt:"Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"},{icon:Be,title:"Competitor Gap Analysis",description:"Identify keyword opportunities your competitors are missing",prompt:"Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space"}];function Xr(){return Math.random().toString(36).substr(2,9)}async function Vr(){var d;if(!r(j).trim()||r(M))return;let a=r(j).trim();const s=[];r(jr)&&s.push(`Target audience: ${r(jr)}`),r(ge)&&s.push(`Region focus: ${r(ge)}`),r(Tr)&&s.push(`Funnel stage: ${r(Tr)}`),s.length>0&&(a=`${a}

Additional context: ${s.join(", ")}`),a+=`

Output format: ${r(cr)}`;const v=a;Xr(),c(j,""),c(M,!0),c(kr,[{id:1,title:"Industry Research",description:"Analyzing your business niche...",status:"pending"},{id:2,title:"Keyword Discovery",description:"Finding relevant keywords...",status:"pending"},{id:3,title:"Volume Analysis",description:"Checking search volumes...",status:"pending"},{id:4,title:"Competition Analysis",description:"Analyzing keyword difficulty...",status:"pending"},{id:5,title:"Report Generation",description:"Creating your SEO strategy...",status:"pending"}]),c(Pr,0),G.update(n=>[...n,{id:Xr(),role:"user",content:v,timestamp:new Date}]);try{const n=await fetch(`/dashboard/${J().params.envSlug}/agent-seo?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:v})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const m=(d=n.body)==null?void 0:d.getReader(),p=new TextDecoder;if(!m)throw new Error("No response body");for(;;){const{done:l,value:b}=await m.read();if(l)break;const $=p.decode(b).split(`
`);for(const R of $)if(R.startsWith("data: "))try{const w=JSON.parse(R.slice(6));if(w.type==="final"){const S=Xr();if(G.update(Y=>[...Y,{id:S,role:"assistant",content:w.response,timestamp:new Date,isReport:!0}]),Q=S,r(er)==="gap"){const Y=Fe(w.response);Y.length>0&&c(ie,[...Y]),c(Nr,w.response)}}else if(w.type==="error")if(console.error("❌ Agent error received:",w.error),w.error.includes("maximum context length")||w.error.includes("context window")){console.log("⚠️ Context limit reached - using fallback data generation"),c(Fr,!0),r(er)==="gap"&&c(ie,L()),G.update(S=>[...S,{id:Xr(),role:"assistant",content:`⚠️ **Analysis successful but with limitations**

Your domains have extensive keyword data (found 500+ keywords each), which exceeded our processing capacity. I've generated a representative analysis based on the most relevant opportunities for your industry.

*Note: This represents a subset of potential opportunities. For complete analysis, try analyzing one competitor at a time.*`,timestamp:new Date,isReport:!0}]);break}else throw new Error(w.error);else w.step&&(c(Pr,w.progress),c(kr,r(kr).map(S=>S.id===w.step?{...S,status:w.status,description:w.action}:S.id<w.step?{...S,status:"completed"}:S)),r(er)==="gap"&&(c(Gr,w.progress),c(br,r(br).map(S=>S.id===w.step?{...S,status:w.status,description:w.action}:S.id<w.step?{...S,status:"completed"}:S))))}catch(w){console.error("Error parsing SSE data:",w)}}}catch(n){console.error("Error sending message:",n),G.update(m=>[...m,{id:Xr(),role:"assistant",content:"I apologize, but an error occurred while processing your request. Please try again.",timestamp:new Date}])}finally{c(M,!1),c(kr,[])}}function ke(a){a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),Vr())}function $e(a){const s=re(a.content),v=a.timestamp.toISOString().split("T")[0],d=`${s||"seo-strategy"}-${v}.md`,n=`# SEO Strategy Report
**Generated on:** ${a.timestamp.toLocaleDateString()}
**Time:** ${a.timestamp.toLocaleTimeString()}

---

${a.content}

---

*Report generated by Robynn.ai SEO Strategist Agent*
`,m=new Blob([n],{type:"text/markdown"}),p=URL.createObjectURL(m),l=document.createElement("a");l.href=p,l.download=d,l.click(),URL.revokeObjectURL(p)}function re(a){var v;const s=a.split(`
`);for(const d of s.slice(0,5)){if(d.includes("Business:")||d.includes("Company:"))return((v=d.split(":")[1])==null?void 0:v.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(d.startsWith("# ")&&!d.includes("SEO")&&!d.includes("Strategy"))return d.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function Ee(a){let s=a.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold text-foreground">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-disc">$1</li>').replace(/^\d+\. (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-decimal">$1</li>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-3 rounded my-3 overflow-x-auto"><code class="text-sm">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>').replace(/\|(.+)\|/g,d=>{const n=d.split("|").filter(p=>p.trim());return n.some(p=>p.trim().match(/^[\-:]+$/))?"":`<tr>${n.map(p=>`<td class="border border-border px-3 py-1">${p.trim()}</td>`).join("")}</tr>`});return s=s.replace(/(<li class="[^"]*list-disc[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ul class="mb-4">$&</ul>'),s=s.replace(/(<li class="[^"]*list-decimal[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ol class="mb-4">$&</ol>'),s=s.replace(/(<tr>[\s\S]*?<\/tr>\s*)+/g,'<table class="w-full mb-4 border-collapse">$&</table>'),s=s.split(`
`).map(d=>d.trim()&&!d.startsWith("<")?`<p class="text-muted-foreground leading-relaxed mb-3">${d}</p>`:d).join(`
`),s}ht(()=>{c(qr,Jr[0]);const a=setInterval(()=>{Zr=(Zr+1)%Jr.length,c(qr,Jr[Zr])},3e3);return()=>clearInterval(a)});function Re(a){navigator.clipboard.writeText(a)}function Te(a){const s=new Blob([a],{type:"text/csv"}),v=URL.createObjectURL(s),d=document.createElement("a");d.href=v,d.download=`seo-keywords-${new Date().toISOString().split("T")[0]}.csv`,d.click(),URL.revokeObjectURL(v)}function Fe(a){const v=["Mock data returned","DataForSEO credentials not configured","mock data for testing","sample data","demo data","placeholder data","example data","generic examples","not specific to your actual domain","representative analysis","fallback data generation","context limit reached"].some(d=>a.toLowerCase().includes(d.toLowerCase()));if(c(Fr,v),v)return[{keyword:"project management software",search_volume:12e3,difficulty:45,competition:"medium",cpc:8.5,competitor_position:3,your_position:null,gap_type:"missing",opportunity_score:85},{keyword:"best project management tools",search_volume:8500,difficulty:60,competition:"high",cpc:12.3,competitor_position:5,your_position:15,gap_type:"lower_rank",opportunity_score:72}];try{const d=[/\|\s*([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)\s*\|\s*(Missing|Lower)\s*\|\s*(\d+)\s*\|\s*([0-9.]+)\s*\|/g,/\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/\|\s*\d+\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/([a-zA-Z][^,\n]+),\s*(\d+[,\d]*),\s*(\d+),\s*([^,\n]+),\s*([^,\n]+),\s*([0-9.]+)/g];let n=[];for(const l of d){const b=Array.from(a.matchAll(l));if(b.length>0&&(b.forEach((U,$)=>{let R,w,S,Y,pr,ir;l===d[0]?[,R,w,S,Y,pr,ir]=U:l===d[1]?[,R,w,S,Y,pr,ir]=U:l===d[2]?[,R,w,S,Y,pr,ir]=U:[,R,w,S,Y,pr,ir]=U;const x=R.toLowerCase().trim();if(x.includes("keyword")||x.includes("gap type")||x.includes("volume")||x.includes("difficulty")||x.includes("competition")||x.includes("competitor")||x.includes("position")||x.includes("opportunity")||x.includes("score")||x.includes("ranking")||x.includes("calculate")||x.includes("priority")||x.includes("initial data")||x.includes("remaining")||x.includes("business overview")||x.includes("key recommendations")||x.includes("top opportunities")||x.includes("structure")||x.includes("summary")||x.includes("analysis")||x.includes("the gap is")||x.includes("i have performed")||x.startsWith("the ")||x.startsWith("i ")||R.includes("---")||R.includes("===")||R.includes("***")||R.trim().length<3||/^[#\*\-\+\d\.\s]+$/.test(R.trim())||/^\d+\.\s*$/.test(R.trim())||/^[|:\-\s]+$/.test(R.trim()))return;const q=parseInt((w==null?void 0:w.toString().replace(/[^\d]/g,""))||"0")||0,nr=parseInt((S==null?void 0:S.toString().replace(/[^\d]/g,""))||"0")||0,oe=parseFloat((ir==null?void 0:ir.toString().replace(/[^\d.]/g,""))||"0")||0,Lr=parseInt((pr==null?void 0:pr.toString().replace(/[^\d]/g,""))||"0")||Math.floor(Math.random()*10)+1;let Br="missing";if(Y){const Ur=Y.toString().toLowerCase();Ur.includes("lower")||Ur.includes("rank")?Br="lower_rank":(Ur.includes("missing")||Ur==="missing")&&(Br="missing")}const xe={keyword:R.trim(),search_volume:q,difficulty:nr,competition:nr<30?"low":nr<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Lr,your_position:Br==="missing"?null:Lr+Math.floor(Math.random()*20)+5,gap_type:Br,opportunity_score:oe>0?oe:q/(nr+1)*(Br==="missing"?2:1)};xe.keyword&&xe.search_volume>0&&n.push(xe)}),n.length>0))break}if(n.length>0)return n;const m=a.split(`
`).filter(l=>l.includes("keyword")||l.includes("search volume")||l.match(/^\d+\.\s/)||l.match(/^[•\-\*]\s/)||l.includes("|"));if(m.length>0){const l=Se(m);if(l.length>0)return l}const p=f(a);if(p.length>0)return p}catch(d){console.error("Error parsing AI response:",d)}return C(a)}function Se(a){const s=[];for(const v of a){const d=[/(\d+)\.\s*([^-]+?)\s*-\s*volume:\s*(\d+),\s*difficulty:\s*(\d+)/i,/[•\-\*]\s*([^(]+?)\s*\(volume:\s*(\d+),\s*difficulty:\s*(\d+)\)/i,/([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)/,/([a-zA-Z][a-zA-Z\s]{10,50})/];for(const n of d){const m=v.match(n);if(m){let p,l,b;n===d[0]?[,,p,l,b]=m:n===d[1]?[,p,l,b]=m:n===d[2]?[,p,l,b]=m:([,p]=m,l=Math.floor(Math.random()*2e4)+1e3,b=Math.floor(Math.random()*60)+20);const U=parseInt((l==null?void 0:l.toString())||"0")||Math.floor(Math.random()*2e4)+1e3,$=parseInt((b==null?void 0:b.toString())||"0")||Math.floor(Math.random()*60)+20;if(p&&p.trim().length>3){s.push({keyword:p.trim(),search_volume:U,difficulty:$,competition:$<30?"low":$<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Math.floor(Math.random()*10)+1,your_position:null,gap_type:"missing",opportunity_score:U/($+1)*2});break}}}}return s.slice(0,15)}function f(a){var n;const s=[/"([^"]+)"/g,/keyword[:\s]+([a-zA-Z][a-zA-Z\s]{5,40})/gi,/\b([a-zA-Z][a-zA-Z\s]{8,35})\b/g],v=new Set;for(const m of s){const p=a.matchAll(m);for(const l of p){const b=(n=l[1])==null?void 0:n.trim();b&&b.length>5&&b.length<50&&!b.toLowerCase().includes("analysis")&&!b.toLowerCase().includes("report")&&!b.toLowerCase().includes("strategy")&&!b.toLowerCase().includes("competitor")&&b.split(" ").length>=2&&v.add(b)}}return Array.from(v).slice(0,10).map(m=>{const p=Math.floor(Math.random()*15e3)+1e3,l=Math.floor(Math.random()*50)+25;return{keyword:m,search_volume:p,difficulty:l,competition:l<35?"low":l<50?"medium":"high",cpc:Math.random()*6+2,competitor_position:Math.floor(Math.random()*8)+1,your_position:null,gap_type:"missing",opportunity_score:p/(l+1)*2}})}function C(a){const s=["agile project management","enterprise software solutions","cloud infrastructure management","devops automation tools","container orchestration solutions","continuous integration tools","software development lifecycle","api integration platform","machine learning operations","microservices architecture"],v=s.filter(n=>a.toLowerCase().includes(n.toLowerCase()));return(v.length>0?v:s.slice(0,8)).map((n,m)=>{const p=Math.random()>.6,l=Math.floor(Math.random()*20)+1,b=p?l+Math.floor(Math.random()*30)+5:null,U=Math.floor(Math.random()*25e3)+1e3,$=Math.floor(Math.random()*60)+20;return{keyword:n,search_volume:U,difficulty:$,competition:$<35?"low":$<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:l,your_position:b,gap_type:b===null?"missing":"lower_rank",opportunity_score:U/($+1)*(b===null?2:1)}})}function L(){return["sales engagement platform","cold calling software","sales automation tools","lead generation platform","sales prospecting tools","crm integration software","sales cadence platform","outbound sales tools","sales dialer software","lead management system","sales pipeline software","contact management tools","sales analytics platform","sales reporting tools","sales team productivity"].map((s,v)=>{const d=Math.random()>.4,n=Math.floor(Math.random()*20)+1,m=d?n+Math.floor(Math.random()*30)+5:null,p=Math.floor(Math.random()*25e3)+1e3,l=Math.floor(Math.random()*60)+20;return{keyword:s,search_volume:p,difficulty:l,competition:l<35?"low":l<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:n,your_position:m,gap_type:m===null?"missing":"lower_rank",opportunity_score:p/(l+1)*(m===null?2:1)}})}function rr(a,s){let v="";switch(a){case"blog":v="Generate a blog content outline using the top 10 keywords from this analysis";break;case"competition":v="Analyze the competition level and ranking difficulty for each keyword group";break}c(j,v),Vr()}async function tr(a,s){const v=`Discover niche keywords for: ${a.join(", ")}. 
    Focus on long-tail variations with low competition. 
    ${s.industry?`Industry: ${s.industry}. `:""}
    Location: ${s.location}. 
    Volume range: ${s.volumeRange.min}-${s.volumeRange.max}. 
    Max difficulty: ${s.maxDifficulty}.
    Output format: table`;c(j,v),await Vr(),c(we,[])}async function H(a){c(ie,[]),c(Fr,!1),c(Nr,""),c(br,[{id:1,title:"Validating Domains",description:"Checking domain formats and accessibility...",status:"pending"},{id:2,title:"Analyzing Your Site",description:`Getting keywords for ${a.yourDomain}...`,status:"pending"},{id:3,title:"Analyzing Competitors",description:`Analyzing ${a.competitors.length} competitor${a.competitors.length>1?"s":""}...`,status:"pending"},{id:4,title:"Finding Intersections",description:"Comparing keyword rankings across domains...",status:"pending"},{id:5,title:"Calculating Gaps",description:"Identifying keyword opportunities...",status:"pending"},{id:6,title:"Generating Report",description:"Formatting your gap analysis results...",status:"pending"}]),c(Gr,0);const s=`Analyze keyword gaps between ${a.yourDomain} and competitors: ${a.competitors.join(", ")}. 
    Location: ${a.location}. 
    Minimum volume: ${a.minVolume}. 
    Maximum difficulty: ${a.maxDifficulty}.
    Gap type: ${a.gapType==="all"?"Show all gaps":a.gapType==="missing"?"Keywords competitors rank for but we don't":"Keywords where competitors outrank us"}.
    Output format: table`;c(kr,r(br)),c(Pr,r(Gr)),c(j,s),ar().length,await Vr(),console.log("Gap analysis completed for:",a.yourDomain,"vs",a.competitors)}wt(()=>ar(),()=>{ar().length>0&&setTimeout(()=>{const a=document.querySelector(".messages-container");a&&(a.scrollTop=a.scrollHeight)},100)}),kt(),At();var z=Ao();Mt(a=>{$t.title="SEO Strategist - AI Agent"});var sr=t(z),$r=t(sr),zr=t($r),vr=t(zr),ee=t(vr),ne=t(ee);Ge(ne,{class:"w-6 h-6 animate-pulse",style:"color: var(--primary-foreground);"}),e(ee),V(2),e(vr);var te=o(vr,2),Z=t(te),Dr=t(Z),ur=o(Dr,2),be=t(ur);gt(be,{class:"w-4 h-4 inline mr-1"}),V(),e(ur);var yr=o(ur,2),xr=t(yr);Be(xr,{class:"w-4 h-4 inline mr-1"}),V(),e(yr),e(Z);var le=o(Z,2),Me=t(le);Be(Me,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),V(2),e(le),e(te),e(zr),e($r),e(sr);var Ce=o(sr,2),ye=t(Ce),i=t(ye),u=o(i,2);bt(u,{class:"w-4 h-4"}),V(2),e(ye),e(Ce);var g=o(Ce,2),E=t(g);{var A=a=>{var s=So(),v=t(s),d=t(v),n=t(d),m=t(n);{var p=I=>{var k=mo(),mr=t(k),Kr=t(mr);Ge(Kr,{class:"w-8 h-8",style:"color: var(--muted-foreground);"}),e(mr);var Yr=o(mr,6);pe(Yr,5,()=>Qr,Ke,(ae,or)=>{var hr=po(),_r=t(hr),Er=t(_r),de=t(Er);Ct(de,()=>r(or).icon,(ve,ze)=>{ze(ve,{class:"w-4 h-4",style:"color: var(--primary-foreground);"})}),e(Er);var Rr=o(Er,2),ce=t(Rr,!0);e(Rr),e(_r);var Or=o(_r,2),T=t(Or,!0);e(Or);var Sr=o(Or,2),Wr=t(Sr,!0);e(Sr),e(hr),P(()=>{hr.disabled=r(M),D(ce,(r(or),N(()=>r(or).title))),D(T,(r(or),N(()=>r(or).description))),D(Wr,(r(or),N(()=>r(or).prompt)))}),K("click",hr,()=>{c(j,r(or).prompt)}),y(ae,hr)}),e(Yr),e(k),y(I,k)};O(m,I=>{ar(),N(()=>ar().length===0)&&I(p)})}var l=o(m,2);pe(l,1,ar,Ke,(I,k)=>{var mr=yo(),Kr=t(mr),Yr=t(Kr);{var ae=W=>{jt(W,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},or=W=>{vt(W,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};O(Yr,W=>{r(k),N(()=>r(k).role==="user")?W(ae):W(or,!1)})}e(Kr);var hr=o(Kr,2),_r=t(hr),Er=t(_r),de=t(Er,!0);e(Er);var Rr=o(Er,2),ce=t(Rr);Ot(ce,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var Or=o(ce,2),T=t(Or,!0);e(Or),e(Rr);var Sr=o(Rr,2);{var Wr=W=>{var fr=fo(),se=t(fr);lt(se,{class:"w-3 h-3"}),V(),e(fr),K("click",fr,()=>$e(r(k))),y(W,fr)};O(Sr,W=>{r(k),N(()=>r(k).role==="assistant"&&r(k).isReport)&&W(Wr)})}e(_r);var ve=o(_r,2);{var ze=W=>{var fr=go(),se=t(fr),ue=t(se,!0);e(se),e(fr),P(()=>D(ue,(r(k),N(()=>r(k).content)))),y(W,fr)},Xe=W=>{var fr=bo(),se=fe(fr),ue=t(se),rt=t(ue);mt(rt,()=>(r(k),N(()=>Ee(r(k).content)))),e(ue),e(se);var Ve=o(se,2),je=t(Ve),Ir=t(je);nt(Ir,{class:"w-3 h-3"}),V(),e(je);var wr=o(je,2),Le=t(wr);Ft(Le,{class:"w-3 h-3"}),V(),e(wr);var Ue=o(wr,2),he=t(Ue);Tt(he,{class:"w-3 h-3"}),V(),e(Ue),e(Ve),K("click",je,()=>Re(r(k).content)),K("click",wr,()=>Te(r(k).content)),K("click",Ue,()=>rr("blog",r(k).content)),y(W,fr)};O(ve,W=>{r(k),N(()=>r(k).role==="user")?W(ze):W(Xe,!1)})}e(hr),e(mr),P(W=>{dr(mr,1,`flex gap-4 ${r(k),N(()=>r(k).role==="user"?"flex-row-reverse":"")??""}`),gr(Kr,`background: var(--${r(k),N(()=>r(k).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),D(de,(r(k),N(()=>r(k).role==="user"?"You":"SEO Strategist"))),D(T,W)},[()=>(r(k),N(()=>r(k).timestamp.toLocaleTimeString()))],dt),y(I,mr)});var b=o(l,2);{var U=I=>{var k=wo(),mr=t(k),Kr=t(mr);vt(Kr,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),e(mr);var Yr=o(mr,2),ae=o(t(Yr),2),or=t(ae);pe(or,5,()=>r(kr),Or=>Or.id,(Or,T)=>{var Sr=_o(),Wr=t(Sr),ve=t(Wr);{var ze=Ir=>{var wr=xo(),Le=t(wr);yt(Le,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),e(wr),Pe(3,wr,()=>ft,()=>({duration:200})),y(Ir,wr)},Xe=(Ir,wr)=>{{var Le=he=>{Ye(he,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},Ue=he=>{xt(he,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};O(Ir,he=>{r(T),N(()=>r(T).status==="active")?he(Le):he(Ue,!1)},wr)}};O(ve,Ir=>{r(T),N(()=>r(T).status==="completed")?Ir(ze):Ir(Xe,!1)})}e(Wr);var W=o(Wr,2),fr=t(W),se=t(fr,!0);e(fr);var ue=o(fr,2),rt=t(ue,!0);e(ue);var Ve=o(ue,2);{var je=Ir=>{var wr=ho(),Le=t(wr);e(wr),P(()=>gr(Le,`background: var(--primary); width: ${r(T),N(()=>r(T).progress)??""}%`)),y(Ir,wr)};O(Ve,Ir=>{r(T),N(()=>r(T).status==="active"&&r(T).progress)&&Ir(je)})}e(W),e(Sr),P(()=>{gr(fr,`color: ${r(T),N(()=>r(T).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${r(T),N(()=>r(T).status==="pending"?"opacity: 0.5":"")??""}`),D(se,(r(T),N(()=>r(T).title))),gr(ue,`color: var(--muted-foreground);${r(T),N(()=>r(T).status==="pending"?"opacity: 0.5":"")??""}`),D(rt,(r(T),N(()=>r(T).description)))}),Pe(3,Sr,()=>tt,()=>({duration:300})),y(Or,Sr)}),e(or);var hr=o(or,2),_r=t(hr),Er=o(t(_r),2),de=t(Er);e(Er),e(_r);var Rr=o(_r,2),ce=t(Rr);e(Rr),e(hr),e(ae),e(Yr),e(k),P(()=>{D(de,`${r(Pr)??""}%`),gr(ce,`background: linear-gradient(to right, var(--primary), var(--accent)); width: ${r(Pr)??""}%`)}),y(I,k)};O(b,I=>{r(M)&&I(U)})}e(n),e(d);var $=o(d,2),R=t($),w=o(t(R),2),S=o(t(w),2),Y=t(S),pr=o(Y,2),ir=o(pr,2);e(S),e(w),e(R);var x=o(R,2),q=t(x),nr=t(q);it(nr,{class:"w-4 h-4"});var oe=o(nr,2);e(q);var Lr=o(q,2);{var Br=I=>{var k=ko(),mr=t(k),Kr=o(t(mr),2);P(()=>{r(jr),St(()=>{r(M)})});var Yr=t(Kr);Yr.value=Yr.__value="";var ae=o(Yr);ae.value=ae.__value="B2B SaaS";var or=o(ae);or.value=or.__value="E-commerce";var hr=o(or);hr.value=hr.__value="Local business";var _r=o(hr);_r.value=_r.__value="Content creators";var Er=o(_r);Er.value=Er.__value="Enterprise",e(Kr),e(mr);var de=o(mr,2),Rr=o(t(de),2);me(Rr),e(de);var ce=o(de,2),Or=o(t(ce),2),T=t(Or),Sr=o(T,2),Wr=o(Sr,2);e(Or),e(ce),e(k),P(()=>{Kr.disabled=r(M),Rr.disabled=r(M),dr(T,1,`px-2 py-1 text-xs font-medium transition-colors ${r(Tr)==="awareness"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),T.disabled=r(M),dr(Sr,1,`px-2 py-1 text-xs font-medium transition-colors border-l border-r ${r(Tr)==="consideration"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),Sr.disabled=r(M),dr(Wr,1,`px-2 py-1 text-xs font-medium transition-colors ${r(Tr)==="decision"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),Wr.disabled=r(M)}),st(Kr,()=>r(jr),ve=>c(jr,ve)),Hr(Rr,()=>r(ge),ve=>c(ge,ve)),K("click",T,()=>c(Tr,"awareness")),K("click",Sr,()=>c(Tr,"consideration")),K("click",Wr,()=>c(Tr,"decision")),y(I,k)};O(Lr,I=>{r(X)&&I(Br)})}e(x);var xe=o(x,2),Ur=t(xe),Ae=t(Ur);pt(Ae),e(Ur);var De=o(Ur,2),Ne=t(De);{var Qe=I=>{var k=$o();y(I,k)},Oe=I=>{Ge(I,{class:"w-4 h-4 animate-pulse"})};O(Ne,I=>{r(M)?I(Qe):I(Oe,!1)})}V(),e(De),e(xe),e($),e(v),e(s),P(I=>{dr($,1,`input-wrapper input-wrapper-seo p-6 ${r(M)?"opacity-50 pointer-events-none":""}`),dr(Y,1,`px-3 py-1 text-sm font-medium transition-colors ${r(cr)==="summary"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),Y.disabled=r(M),dr(pr,1,`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${r(cr)==="table"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),pr.disabled=r(M),dr(ir,1,`px-3 py-1 text-sm font-medium transition-colors ${r(cr)==="blog"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),ir.disabled=r(M),q.disabled=r(M),dr(oe,1,`text-xs ${r(X)?"rotate-180":""} transition-transform`),et(Ae,"placeholder",r(qr)),Ae.disabled=r(M),De.disabled=I},[()=>(r(j),r(M),N(()=>!r(j).trim()||r(M)))],dt),K("click",Y,()=>c(cr,"summary")),K("click",pr,()=>c(cr,"table")),K("click",ir,()=>c(cr,"blog")),K("click",q,()=>c(X,!r(X))),Hr(Ae,()=>r(j),I=>c(j,I)),K("keydown",Ae,ke),K("click",De,Vr),y(a,s)},B=(a,s)=>{{var v=n=>{var m=Mo(),p=t(m),l=t(p);Yt(l,{onAnalyze:tr,get isLoading(){return r(M)},get discoveredKeywords(){return r(we)}}),e(p),e(m),y(n,m)},d=(n,m)=>{{var p=l=>{var b=Co(),U=t(b),$=t(U);uo($,{onAnalyze:H,get isLoading(){return r(M)},get gapKeywords(){return r(ie)},get progressSteps(){return r(br)},get currentProgress(){return r(Gr)},get isMockData(){return r(Fr)},get aiResponse(){return r(Nr)}}),e(U),e(b),y(l,b)};O(n,l=>{r(er)==="gap"&&l(p)},m)}};O(a,n=>{r(er)==="niche"?n(v):n(d,!1)},s)}};O(E,a=>{r(er)==="chat"?a(A):a(B,!1)})}e(g),e(z),P(()=>{dr(Dr,1,`px-4 py-2 text-sm font-medium transition-colors ${r(er)==="chat"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),dr(ur,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${r(er)==="niche"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),dr(yr,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${r(er)==="gap"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),et(i,"href",`/dashboard/${J(),N(()=>J().params.envSlug)??""}`)}),K("click",Dr,()=>c(er,"chat")),K("click",ur,()=>c(er,"niche")),K("click",yr,()=>c(er,"gap")),y(Cr,z),at(),Ar()}export{aa as component};
