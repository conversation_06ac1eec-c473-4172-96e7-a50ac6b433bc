

export const index = 38;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/(marketing)/blog/(posts)/how_we_built_our_41kb_saas_website/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/38.D2O-Pao3.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Cvx8ZW61.js","_app/immutable/chunks/wnqW1tdD.js"];
export const stylesheets = [];
export const fonts = [];
