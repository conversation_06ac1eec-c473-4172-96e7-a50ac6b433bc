import * as universal from '../entries/pages/(marketing)/blog/_layout.ts.js';

export const index = 6;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/routes/(marketing)/blog/+layout.ts";
export const imports = ["_app/immutable/nodes/6.DmJvdx4g.js","_app/immutable/chunks/BYyWxyJW.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/wnqW1tdD.js","_app/immutable/chunks/yk44OJLy.js"];
export const stylesheets = [];
export const fonts = [];
