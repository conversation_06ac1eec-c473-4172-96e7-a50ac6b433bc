import { K as sanitize_props, M as spread_props, N as slot, V as ensure_array_like, F as store_get, I as head, Y as attr, Z as stringify, J as escape_html, X as attr_class, W as attr_style, G as unsubscribe_stores, E as pop, A as push } from "../../../../../../chunks/index.js";
import { w as writable } from "../../../../../../chunks/index3.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { C as Chevron_right, B as Bot } from "../../../../../../chunks/chevron-right.js";
import { U as User, C as Clock, D as Download } from "../../../../../../chunks/user.js";
import { C as Copy, F as Filter } from "../../../../../../chunks/filter.js";
import { Z as Zap } from "../../../../../../chunks/zap.js";
import { U as Users } from "../../../../../../chunks/users.js";
import { h as html } from "../../../../../../chunks/html.js";
function Calendar($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "M8 2v4" }],
    ["path", { "d": "M16 2v4" }],
    [
      "rect",
      {
        "width": "18",
        "height": "18",
        "x": "3",
        "y": "4",
        "rx": "2"
      }
    ],
    ["path", { "d": "M3 10h18" }]
  ];
  Icon($$payload, spread_props([
    { name: "calendar" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Mail($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "rect",
      {
        "width": "20",
        "height": "16",
        "x": "2",
        "y": "4",
        "rx": "2"
      }
    ],
    [
      "path",
      {
        "d": "m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "mail" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Megaphone($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "m3 11 18-5v12L3 14v-3z" }],
    [
      "path",
      { "d": "M11.6 16.8a3 3 0 1 1-5.8-1.6" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "megaphone" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Share_2($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["circle", { "cx": "18", "cy": "5", "r": "3" }],
    ["circle", { "cx": "6", "cy": "12", "r": "3" }],
    [
      "circle",
      { "cx": "18", "cy": "19", "r": "3" }
    ],
    [
      "line",
      {
        "x1": "8.59",
        "x2": "15.42",
        "y1": "13.51",
        "y2": "17.49"
      }
    ],
    [
      "line",
      {
        "x1": "15.41",
        "x2": "8.59",
        "y1": "6.51",
        "y2": "10.49"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "share-2" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const messages = writable([]);
  let input = "";
  let isLoading = false;
  let currentPlaceholder = "";
  const interactiveCards = [
    {
      icon: Zap,
      title: "Product Launch Campaign",
      description: "Multi-phase campaign strategy for new product introductions",
      prompt: "Create a comprehensive product launch campaign for [Product Name] targeting [Audience] with a [Timeline] timeline"
    },
    {
      icon: Calendar,
      title: "Seasonal Promotion",
      description: "Coordinated campaigns for holidays, events, and seasonal sales",
      prompt: "Plan a [Holiday/Season] marketing campaign across email, social, and paid channels with a budget of [Budget]"
    },
    {
      icon: Users,
      title: "Customer Retention",
      description: "Multi-touch engagement campaigns to reduce churn and increase LTV",
      prompt: "Design a customer retention campaign with personalized email sequences and loyalty rewards for [Customer Segment]"
    }
  ];
  function formatContent(content) {
    return content.replace(/^### (.+)$/gm, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>').replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>').replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>').replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>').replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>").replace(/\n\n/g, '</p><p class="mb-4">').replace(/^/, '<p class="mb-4">').replace(/$/, "</p>");
  }
  const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$messages", messages));
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Campaign Orchestrator - AI Agent</title>`;
  });
  $$payload.out += `<div class="h-screen flex flex-col svelte-1bbifsw" style="background: var(--background);"><div class="border-b-2 flex-shrink-0 svelte-1bbifsw" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6 svelte-1bbifsw"><div class="flex items-center justify-between svelte-1bbifsw"><div class="flex items-center space-x-4 svelte-1bbifsw"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer svelte-1bbifsw" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Megaphone($$payload, {
    class: "w-6 h-6 animate-pulse",
    style: "color: var(--primary-foreground);"
  });
  $$payload.out += `<!----></div> <div class="svelte-1bbifsw"><h1 class="text-3xl font-black svelte-1bbifsw" style="color: var(--foreground);">Campaign Orchestrator</h1> <p class="text-lg font-medium svelte-1bbifsw" style="color: var(--muted-foreground);">AI-powered multi-channel campaign planning and execution</p></div></div> <div class="flex items-center space-x-4 svelte-1bbifsw"><div class="flex items-center space-x-2 px-4 py-2 border-2 svelte-1bbifsw" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Share_2($$payload, {
    class: "w-4 h-4",
    style: "color: var(--accent-foreground);"
  });
  $$payload.out += `<!----> <span class="text-sm font-bold svelte-1bbifsw" style="color: var(--accent-foreground);">Multi-Channel</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4 svelte-1bbifsw"><nav class="flex items-center space-x-2 text-sm text-muted-foreground svelte-1bbifsw"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors svelte-1bbifsw">Dashboard</a> `;
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span class="text-foreground font-medium svelte-1bbifsw">Campaign Orchestrator</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full svelte-1bbifsw"><div class="h-full svelte-1bbifsw"><div class="card-brutal p-0 chat-container h-full svelte-1bbifsw" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container svelte-1bbifsw"><div class="space-y-6 svelte-1bbifsw">`;
  if (store_get($$store_subs ??= {}, "$messages", messages).length === 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(interactiveCards);
    $$payload.out += `<div class="text-center py-12 svelte-1bbifsw"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 svelte-1bbifsw" style="background: var(--muted); border-color: var(--border);">`;
    Megaphone($$payload, {
      class: "w-8 h-8",
      style: "color: var(--muted-foreground);"
    });
    $$payload.out += `<!----></div> <h3 class="text-xl font-bold mb-2 svelte-1bbifsw" style="color: var(--foreground);">Start Planning Your Campaign</h3> <p class="font-medium mb-6 svelte-1bbifsw" style="color: var(--muted-foreground);">Create comprehensive multi-channel marketing campaigns with AI</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto svelte-1bbifsw"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let card = each_array[$$index];
      $$payload.out += `<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group svelte-1bbifsw" style="background: var(--card); border-color: var(--border);"${attr("disabled", isLoading, true)}><div class="flex items-center gap-3 mb-3 svelte-1bbifsw"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform svelte-1bbifsw" style="background: var(--primary); border-color: var(--border);"><!---->`;
      card.icon?.($$payload, {
        class: "w-4 h-4",
        style: "color: var(--primary-foreground);"
      });
      $$payload.out += `<!----></div> <h4 class="font-bold text-sm svelte-1bbifsw" style="color: var(--foreground);">${escape_html(card.title)}</h4></div> <p class="text-xs mb-3 svelte-1bbifsw" style="color: var(--muted-foreground);">${escape_html(card.description)}</p> <div class="text-xs font-mono p-2 border-2 rounded svelte-1bbifsw" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);">${escape_html(card.prompt)}</div></button>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let message = each_array_1[$$index_1];
    $$payload.out += `<div${attr_class(`flex gap-4 ${stringify(message.role === "user" ? "flex-row-reverse" : "")}`, "svelte-1bbifsw")}><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-1bbifsw"${attr_style(`background: var(--${stringify(message.role === "user" ? "primary" : "secondary")}); border-color: var(--border); box-shadow: var(--shadow-sm);`)}>`;
    if (message.role === "user") {
      $$payload.out += "<!--[-->";
      User($$payload, {
        class: "w-5 h-5",
        style: "color: var(--primary-foreground);"
      });
    } else {
      $$payload.out += "<!--[!-->";
      Bot($$payload, {
        class: "w-5 h-5",
        style: "color: var(--secondary-foreground);"
      });
    }
    $$payload.out += `<!--]--></div> <div class="flex-1 max-w-3xl svelte-1bbifsw"><div class="flex items-center gap-2 mb-2 svelte-1bbifsw"><span class="text-sm font-bold svelte-1bbifsw" style="color: var(--foreground);">${escape_html(message.role === "user" ? "You" : "Campaign Orchestrator")}</span> <div class="flex items-center gap-1 svelte-1bbifsw">`;
    Clock($$payload, {
      class: "w-3 h-3",
      style: "color: var(--muted-foreground);"
    });
    $$payload.out += `<!----> <span class="text-xs svelte-1bbifsw" style="color: var(--muted-foreground);">${escape_html(message.timestamp.toLocaleTimeString())}</span></div> `;
    if (message.role === "assistant" && message.isReport) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-1bbifsw" title="Download as Markdown">`;
      Download($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Download</button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> `;
    if (message.role === "user") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="p-4 border-2 svelte-1bbifsw" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-1bbifsw" style="color: var(--primary-foreground);">${escape_html(message.content)}</p></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="p-6 border-2 mb-4 svelte-1bbifsw" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none svelte-1bbifsw">${html(formatContent(message.content))}</div></div> <div class="flex flex-wrap gap-2 mb-4 svelte-1bbifsw"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw">`;
      Copy($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw">`;
      Mail($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Email Brief</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw">`;
      Calendar($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Export Timeline</button></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div${attr_class(`input-wrapper input-wrapper-seo p-6 ${stringify("")}`, "svelte-1bbifsw")}><div class="flex items-center justify-between svelte-1bbifsw" style="margin-bottom: 15px;"><h2 class="text-lg font-bold svelte-1bbifsw" style="color: var(--foreground);">Campaign Planning</h2> <div class="flex items-center space-x-2 svelte-1bbifsw"><span class="text-sm font-medium svelte-1bbifsw" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2 svelte-1bbifsw" style="border-color: var(--border); background: var(--background);"><button${attr_class(`px-3 py-1 text-sm font-medium transition-colors ${stringify("bg-primary text-primary-foreground")}`, "svelte-1bbifsw")}${attr("disabled", isLoading, true)}>Overview</button> <button${attr_class(`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${stringify("text-muted-foreground hover:text-foreground")}`, "svelte-1bbifsw")} style="border-color: var(--border);"${attr("disabled", isLoading, true)}>Detailed</button> <button${attr_class(`px-3 py-1 text-sm font-medium transition-colors ${stringify("text-muted-foreground hover:text-foreground")}`, "svelte-1bbifsw")}${attr("disabled", isLoading, true)}>Executive</button></div></div></div> <div style="margin-bottom: 15px;" class="svelte-1bbifsw"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2 svelte-1bbifsw" style="margin-bottom: 10px;"${attr("disabled", isLoading, true)}>`;
  Filter($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> Campaign Configuration <span${attr_class(`text-xs ${stringify("")} transition-transform`, "svelte-1bbifsw")}>▼</span></button> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="flex svelte-1bbifsw" style="gap: 15px;"><div class="flex-1 relative svelte-1bbifsw"><textarea${attr("placeholder", currentPlaceholder)} class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full svelte-1bbifsw" style="min-height: 60px;"${attr("disabled", isLoading, true)}>`;
  const $$body = escape_html(input);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <button${attr("disabled", !input.trim() || isLoading, true)} class="btn-primary px-6 font-bold flex items-center gap-2 svelte-1bbifsw" style="height: auto; align-self: stretch;">`;
  {
    $$payload.out += "<!--[!-->";
    Megaphone($$payload, { class: "w-4 h-4 animate-pulse" });
  }
  $$payload.out += `<!--]--> Orchestrate</button></div></div></div></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
