import { K as sanitize_props, M as spread_props, N as slot, B as setContext, O as getContext, R as rest_props, P as fallback, F as store_get, S as spread_attributes, G as unsubscribe_stores, Q as bind_props, E as pop, A as push, V as ensure_array_like, I as head, J as escape_html, Y as attr, Z as stringify } from "../../../../chunks/index.js";
import { P as Pricing_module } from "../../../../chunks/pricing_module.js";
import { W as WebsiteName } from "../../../../chunks/config.js";
import { o as omit, m as makeElement, c as createElHelpers, h as disabledAttr, e as executeCallbacks, a as addMeltEventListener, i as isHTMLElement, s as styleToString, k as kbd, r as getElementByMeltId } from "../../../../chunks/create.js";
import "clsx";
import { t as tick } from "../../../../chunks/index-server.js";
import { t as toWritableStores, g as generateIds, o as overridable, b as generateId, r as removeUndefined, a as getOptionUpdater } from "../../../../chunks/updater.js";
import { d as derived, w as writable } from "../../../../chunks/index3.js";
import { c as createBitAttrs } from "../../../../chunks/attrs.js";
import { c as cn } from "../../../../chunks/utils.js";
import { I as Icon } from "../../../../chunks/Icon.js";
import { s as slide } from "../../../../chunks/index4.js";
function Chevron_down($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [["path", { "d": "m6 9 6 6 6-6" }]];
  Icon($$payload, spread_props([
    { name: "chevron-down" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
const { name, selector } = createElHelpers("accordion");
const defaults = {
  multiple: false,
  disabled: false,
  forceVisible: false
};
const createAccordion = (props) => {
  const withDefaults = { ...defaults, ...props };
  const options = toWritableStores(omit(withDefaults, "value", "onValueChange", "defaultValue"));
  const meltIds = generateIds(["root"]);
  const { disabled, forceVisible } = options;
  const valueWritable = withDefaults.value ?? writable(withDefaults.defaultValue);
  const value = overridable(valueWritable, withDefaults?.onValueChange);
  const isSelected = (key, v) => {
    if (v === void 0)
      return false;
    if (typeof v === "string")
      return v === key;
    return v.includes(key);
  };
  const isSelectedStore = derived(value, ($value) => {
    return (key) => isSelected(key, $value);
  });
  const root = makeElement(name(), {
    returned: () => ({
      "data-melt-id": meltIds.root
    })
  });
  const parseItemProps = (props2) => {
    if (typeof props2 === "string") {
      return { value: props2 };
    } else {
      return props2;
    }
  };
  const parseHeadingProps = (props2) => {
    if (typeof props2 === "number") {
      return { level: props2 };
    } else {
      return props2;
    }
  };
  const item = makeElement(name("item"), {
    stores: value,
    returned: ($value) => {
      return (props2) => {
        const { value: itemValue, disabled: disabled2 } = parseItemProps(props2);
        return {
          "data-state": isSelected(itemValue, $value) ? "open" : "closed",
          "data-disabled": disabledAttr(disabled2)
        };
      };
    }
  });
  const trigger = makeElement(name("trigger"), {
    stores: [value, disabled],
    returned: ([$value, $disabled]) => {
      return (props2) => {
        const { value: itemValue, disabled: disabled2 } = parseItemProps(props2);
        return {
          disabled: disabledAttr($disabled || disabled2),
          "aria-expanded": isSelected(itemValue, $value) ? true : false,
          "aria-disabled": disabled2 ? true : false,
          "data-disabled": disabledAttr(disabled2),
          "data-value": itemValue,
          "data-state": isSelected(itemValue, $value) ? "open" : "closed"
        };
      };
    },
    action: (node) => {
      const unsub = executeCallbacks(addMeltEventListener(node, "click", () => {
        const disabled2 = node.dataset.disabled === "true";
        const itemValue = node.dataset.value;
        if (disabled2 || !itemValue)
          return;
        handleValueUpdate(itemValue);
      }), addMeltEventListener(node, "keydown", (e) => {
        if (![kbd.ARROW_DOWN, kbd.ARROW_UP, kbd.HOME, kbd.END].includes(e.key)) {
          return;
        }
        e.preventDefault();
        if (e.key === kbd.SPACE || e.key === kbd.ENTER) {
          const disabled2 = node.dataset.disabled === "true";
          const itemValue = node.dataset.value;
          if (disabled2 || !itemValue)
            return;
          handleValueUpdate(itemValue);
          return;
        }
        const el = e.target;
        const rootEl = getElementByMeltId(meltIds.root);
        if (!rootEl || !isHTMLElement(el))
          return;
        const items = Array.from(rootEl.querySelectorAll(selector("trigger")));
        const candidateItems = items.filter((item2) => {
          if (!isHTMLElement(item2))
            return false;
          return item2.dataset.disabled !== "true";
        });
        if (!candidateItems.length)
          return;
        const elIdx = candidateItems.indexOf(el);
        if (e.key === kbd.ARROW_DOWN) {
          candidateItems[(elIdx + 1) % candidateItems.length].focus();
        }
        if (e.key === kbd.ARROW_UP) {
          candidateItems[(elIdx - 1 + candidateItems.length) % candidateItems.length].focus();
        }
        if (e.key === kbd.HOME) {
          candidateItems[0].focus();
        }
        if (e.key === kbd.END) {
          candidateItems[candidateItems.length - 1].focus();
        }
      }));
      return {
        destroy: unsub
      };
    }
  });
  const content = makeElement(name("content"), {
    stores: [value, disabled, forceVisible],
    returned: ([$value, $disabled, $forceVisible]) => {
      return (props2) => {
        const { value: itemValue } = parseItemProps(props2);
        const isVisible = isSelected(itemValue, $value) || $forceVisible;
        return {
          "data-state": isVisible ? "open" : "closed",
          "data-disabled": disabledAttr($disabled),
          "data-value": itemValue,
          hidden: isVisible ? void 0 : true,
          style: styleToString({
            display: isVisible ? void 0 : "none"
          })
        };
      };
    },
    action: (node) => {
      tick().then(() => {
        const contentId = generateId();
        const triggerId = generateId();
        const parentTrigger = document.querySelector(`${selector("trigger")}, [data-value="${node.dataset.value}"]`);
        if (!isHTMLElement(parentTrigger))
          return;
        node.id = contentId;
        parentTrigger.setAttribute("aria-controls", contentId);
        parentTrigger.id = triggerId;
      });
    }
  });
  const heading = makeElement(name("heading"), {
    returned: () => {
      return (props2) => {
        const { level } = parseHeadingProps(props2);
        return {
          role: "heading",
          "aria-level": level,
          "data-heading-level": level
        };
      };
    }
  });
  function handleValueUpdate(itemValue) {
    value.update(($value) => {
      if ($value === void 0) {
        return withDefaults.multiple ? [itemValue] : itemValue;
      }
      if (Array.isArray($value)) {
        if ($value.includes(itemValue)) {
          return $value.filter((v) => v !== itemValue);
        }
        $value.push(itemValue);
        return $value;
      }
      return $value === itemValue ? void 0 : itemValue;
    });
  }
  return {
    ids: meltIds,
    elements: {
      root,
      item,
      trigger,
      content,
      heading
    },
    states: {
      value
    },
    helpers: {
      isSelected: isSelectedStore
    },
    options
  };
};
function getAccordionData() {
  const NAME = "accordion";
  const ITEM_NAME = "accordion-item";
  const PARTS = ["root", "content", "header", "item", "trigger"];
  return { NAME, ITEM_NAME, PARTS };
}
function setCtx(props) {
  const initAccordion = createAccordion(removeUndefined(props));
  const { NAME, PARTS } = getAccordionData();
  const getAttrs = createBitAttrs(NAME, PARTS);
  const accordion = {
    ...initAccordion,
    getAttrs,
    updateOption: getOptionUpdater(initAccordion.options)
  };
  setContext(NAME, accordion);
  return accordion;
}
function getCtx() {
  const { NAME } = getAccordionData();
  return getContext(NAME);
}
function setItem(props) {
  const { ITEM_NAME } = getAccordionData();
  const propsStore = writable(props);
  setContext(ITEM_NAME, { propsStore });
  const ctx = getCtx();
  return { ...ctx, propsStore };
}
function getItemProps() {
  const { ITEM_NAME } = getAccordionData();
  return getContext(ITEM_NAME);
}
function getContent() {
  const ctx = getCtx();
  const { propsStore } = getItemProps();
  return {
    ...ctx,
    propsStore
  };
}
function getTrigger() {
  const ctx = getCtx();
  const { propsStore } = getItemProps();
  return {
    ...ctx,
    props: propsStore
  };
}
function arraysAreEqual(arr1, arr2) {
  if (arr1.length !== arr2.length) {
    return false;
  }
  return arr1.every((value, index) => value === arr2[index]);
}
function Accordion($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, [
    "multiple",
    "value",
    "onValueChange",
    "disabled",
    "asChild",
    "el"
  ]);
  push();
  var $$store_subs;
  let builder;
  let multiple = fallback($$props["multiple"], false);
  let value = fallback($$props["value"], () => void 0, true);
  let onValueChange = fallback($$props["onValueChange"], () => void 0, true);
  let disabled = fallback($$props["disabled"], false);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  const {
    elements: { root },
    states: { value: localValue },
    updateOption,
    getAttrs
  } = setCtx({
    multiple,
    disabled,
    defaultValue: value,
    onValueChange: ({ next }) => {
      if (Array.isArray(next)) {
        if (!Array.isArray(value) || !arraysAreEqual(value, next)) {
          onValueChange?.(next);
          value = next;
          return next;
        }
        return next;
      }
      if (value !== next) {
        onValueChange?.(next);
        value = next;
      }
      return next;
    }
  });
  const attrs = getAttrs("root");
  value !== void 0 && localValue.set(Array.isArray(value) ? [...value] : value);
  updateOption("multiple", multiple);
  updateOption("disabled", disabled);
  builder = store_get($$store_subs ??= {}, "$root", root);
  Object.assign(builder, attrs);
  if (asChild) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    multiple,
    value,
    onValueChange,
    disabled,
    asChild,
    el
  });
  pop();
}
function Accordion_item$1($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["value", "disabled", "asChild", "el"]);
  push();
  var $$store_subs;
  let builder;
  let value = $$props["value"];
  let disabled = fallback($$props["disabled"], () => void 0, true);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  const { elements: { item }, propsStore, getAttrs } = setItem({ value, disabled });
  const attrs = getAttrs("item");
  propsStore.set({ value, disabled });
  builder = store_get($$store_subs ??= {}, "$item", item)({
    ...store_get($$store_subs ??= {}, "$propsStore", propsStore),
    disabled
  });
  Object.assign(builder, attrs);
  if (asChild) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { value, disabled, asChild, el });
  pop();
}
function Accordion_header($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["level", "asChild", "el"]);
  push();
  var $$store_subs;
  let builder;
  let level = fallback($$props["level"], 3);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  const { elements: { heading: header }, getAttrs } = getCtx();
  const attrs = getAttrs("header");
  builder = store_get($$store_subs ??= {}, "$header", header)(level);
  Object.assign(builder, attrs);
  if (asChild) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { level, asChild, el });
  pop();
}
function Accordion_trigger$1($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["asChild", "el"]);
  push();
  var $$store_subs;
  let builder;
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  const { elements: { trigger }, props, getAttrs } = getTrigger();
  const attrs = getAttrs("trigger");
  builder = store_get($$store_subs ??= {}, "$trigger", trigger)({
    ...store_get($$store_subs ??= {}, "$props", props)
  });
  Object.assign(builder, attrs);
  if (asChild) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<button${spread_attributes({ ...builder, type: "button", ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></button>`;
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { asChild, el });
  pop();
}
function Accordion_content$1($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, [
    "transition",
    "transitionConfig",
    "inTransition",
    "inTransitionConfig",
    "outTransition",
    "outTransitionConfig",
    "asChild",
    "el"
  ]);
  push();
  var $$store_subs;
  let builder;
  let transition = fallback($$props["transition"], () => void 0, true);
  let transitionConfig = fallback($$props["transitionConfig"], () => void 0, true);
  let inTransition = fallback($$props["inTransition"], () => void 0, true);
  let inTransitionConfig = fallback($$props["inTransitionConfig"], () => void 0, true);
  let outTransition = fallback($$props["outTransition"], () => void 0, true);
  let outTransitionConfig = fallback($$props["outTransitionConfig"], () => void 0, true);
  let asChild = fallback($$props["asChild"], false);
  let el = fallback($$props["el"], () => void 0, true);
  const {
    elements: { content },
    helpers: { isSelected },
    propsStore,
    getAttrs
  } = getContent();
  const attrs = getAttrs("content");
  builder = store_get($$store_subs ??= {}, "$content", content)({
    ...store_get($$store_subs ??= {}, "$propsStore", propsStore)
  });
  Object.assign(builder, attrs);
  if (asChild && store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!---->`;
  } else if (transition && store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  } else if (inTransition && outTransition && store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  } else if (inTransition && store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[3-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  } else if (outTransition && store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[4-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  } else if (store_get($$store_subs ??= {}, "$isSelected", isSelected)(store_get($$store_subs ??= {}, "$propsStore", propsStore).value)) {
    $$payload.out += "<!--[5-->";
    $$payload.out += `<div${spread_attributes({ ...builder, ...$$restProps }, null)}><!---->`;
    slot($$payload, $$props, "default", { builder }, null);
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    transition,
    transitionConfig,
    inTransition,
    inTransitionConfig,
    outTransition,
    outTransitionConfig,
    asChild,
    el
  });
  pop();
}
function Accordion_content($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "transition", "transitionConfig"]);
  push();
  let className = fallback($$props["class"], void 0);
  let transition = fallback($$props["transition"], slide);
  let transitionConfig = fallback($$props["transitionConfig"], () => ({ duration: 200 }), true);
  Accordion_content$1($$payload, spread_props([
    {
      class: cn("overflow-hidden text-sm transition-all", className),
      transition,
      transitionConfig
    },
    $$restProps,
    {
      children: ($$payload2) => {
        $$payload2.out += `<div class="pb-4 pt-0"><!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!----></div>`;
      },
      $$slots: { default: true }
    }
  ]));
  bind_props($$props, {
    class: className,
    transition,
    transitionConfig
  });
  pop();
}
function Accordion_item($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "value"]);
  push();
  let className = fallback($$props["class"], void 0);
  let value = $$props["value"];
  Accordion_item$1($$payload, spread_props([
    { value, class: cn("border-b", className) },
    $$restProps,
    {
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
  bind_props($$props, { class: className, value });
  pop();
}
function Accordion_trigger($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["class", "level"]);
  push();
  let className = fallback($$props["class"], void 0);
  let level = fallback($$props["level"], 3);
  Accordion_header($$payload, {
    level,
    class: "flex",
    children: ($$payload2) => {
      Accordion_trigger$1($$payload2, spread_props([
        {
          class: cn("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180", className)
        },
        $$restProps,
        {
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            slot($$payload3, $$props, "default", {}, null);
            $$payload3.out += `<!----> `;
            Chevron_down($$payload3, {
              class: "h-4 w-4 transition-transform duration-200"
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        }
      ]));
    },
    $$slots: { default: true }
  });
  bind_props($$props, { class: className, level });
  pop();
}
const Root = Accordion;
function _page($$payload) {
  const planFeatures = [
    { name: "Section 1", header: true },
    {
      name: "Feature 1",
      freeIncluded: true,
      proIncluded: true
    },
    {
      name: "Feature 2",
      freeIncluded: false,
      proIncluded: true
    },
    {
      name: "Feature 3",
      freeString: "3",
      proString: "Unlimited"
    },
    { name: "Section 2", header: true },
    {
      name: "Feature 4",
      freeIncluded: true,
      proIncluded: true
    },
    {
      name: "Feature 5",
      freeIncluded: false,
      proIncluded: true
    }
  ];
  const each_array = ensure_array_like(planFeatures);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Pricing</title>`;
    $$payload2.out += `<meta name="description"${attr("content", `Pricing - ${stringify(WebsiteName)}`)}/>`;
  });
  $$payload.out += `<div class="min-h-[70vh] pb-12 pt-16 px-6 bg-white"><div class="max-w-6xl mx-auto text-center"><h1 class="text-4xl md:text-5xl font-light tracking-tight text-gray-900 font-jakarta">Simple, transparent pricing</h1> <p class="text-lg text-gray-600 mt-4 font-light max-w-2xl mx-auto">Choose the plan that's right for your team. Start free and scale as you grow.</p></div> <div class="w-full my-8">`;
  Pricing_module($$payload, {
    callToAction: "Get Started",
    highlightedPlanId: "pro"
  });
  $$payload.out += `<!----> <h1 class="text-2xl font-bold text-center mt-24">Pricing FAQ</h1> <div class="flex place-content-center">`;
  Root($$payload, {
    class: "max-w-xl mx-auto",
    children: ($$payload2) => {
      Accordion_item($$payload2, {
        value: "faq1",
        children: ($$payload3) => {
          Accordion_trigger($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Is this template free to use?`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Accordion_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Yup! This template is free to use for any project.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Accordion_item($$payload2, {
        value: "faq2",
        children: ($$payload3) => {
          Accordion_trigger($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Why does a free template have a pricing page?`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Accordion_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->The pricing page is part of the boilerplate. It shows how the
            pricing page integrates into the billing portal and the Stripe
            Checkout flows.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Accordion_item($$payload2, {
        value: "faq3",
        children: ($$payload3) => {
          Accordion_trigger($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->What license is the template under?`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Accordion_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->The template is under the MIT license.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Accordion_item($$payload2, {
        value: "Is this template free to use?",
        children: ($$payload3) => {
          Accordion_trigger($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Can I try out purchase flows without real a credit card?`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Accordion_content($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->You can use the credit card number 4242 4242 4242 4242 with any
            future expiry date to test the payment and upgrade flows.`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <svg style="display:none" version="2.0"><defs><symbol id="checkcircle" viewBox="0 0 24 24" stroke-width="2" fill="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M16.417 10.283A7.917 7.917 0 1 1 8.5 2.366a7.916 7.916 0 0 1 7.917 7.917zm-4.105-4.498a.791.791 0 0 0-1.082.29l-3.828 6.63-1.733-2.08a.791.791 0 1 0-1.216 1.014l2.459 2.952a.792.792 0 0 0 .608.285.83.83 0 0 0 .068-.003.791.791 0 0 0 .618-.393L12.6 6.866a.791.791 0 0 0-.29-1.081z"></path></symbol></defs></svg> <svg style="display:none" version="2.0"><defs><symbol id="nocircle" viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm4,11H8a1,1,0,0,1,0-2h8a1,1,0,0,1,0,2Z"></path></symbol></defs></svg> <h1 class="text-2xl font-bold text-center mt-16">Plan Features</h1> <h2 class="text-xl text-center mt-1 pb-3">Example feature table</h2> <div class="overflow-visible mx-auto max-w-xl mt-4"><table class="table w-full"><thead class="text-lg sticky top-0 bg-foreground text-background bg-opacity-50 z-10 backdrop-blur"><tr><th></th><th class="text-center">Free</th><th class="text-center">Pro</th></tr></thead><tbody><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    if (feature.header) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<tr class="bg-foreground text-background font-bold p-2"><td colspan="3">${escape_html(feature.name)}</td></tr>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<tr class="relative"><td>${escape_html(feature.name)}</td><td class="text-center">`;
      if (feature.freeString) {
        $$payload.out += "<!--[-->";
        $$payload.out += `${escape_html(feature.freeString)}`;
      } else if (feature.freeIncluded) {
        $$payload.out += "<!--[1-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>`;
      }
      $$payload.out += `<!--]--></td><td class="text-center">`;
      if (feature.proString) {
        $$payload.out += "<!--[-->";
        $$payload.out += `${escape_html(feature.proString)}`;
      } else if (feature.proIncluded) {
        $$payload.out += "<!--[1-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>`;
      }
      $$payload.out += `<!--]--></td></tr>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></tbody></table></div></div></div>`;
}
export {
  _page as default
};
