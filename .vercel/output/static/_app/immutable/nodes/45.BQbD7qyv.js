import"../chunks/CWj6FrbW.js";import{p as o,f as n,s as i,e as d,a as p,g as l,$ as u,j as m,o as c,n as g}from"../chunks/BWPOaEfG.js";import{h}from"../chunks/CZV_L0uA.js";import{A as f,s as v,o as x}from"../chunks/BJRboDQ-.js";import"../chunks/BFr-ef-6.js";var b=n('<h1 class="text-2xl font-bold mb-6">Forgot Password</h1> <!> <div class="text-l text-primary mt-4">Remember your password? <a class="underline" href="/login/sign_in">Sign in</a>.</div>',1);function k(t,a){o(a,!0);var e=b();h(w=>{u.title="Forgot Password"});var r=i(d(e),2);const s=c(()=>`${a.data.url}/auth/callback?next=%2Faccount%2Fsettings%2Freset_password`);f(r,{get supabaseClient(){return a.data.supabase},view:"forgotten_password",get redirectTo(){return m(s)},get providers(){return x},socialLayout:"horizontal",showLinks:!1,get appearance(){return v},additionalData:void 0}),g(2),p(t,e),l()}export{k as component};
