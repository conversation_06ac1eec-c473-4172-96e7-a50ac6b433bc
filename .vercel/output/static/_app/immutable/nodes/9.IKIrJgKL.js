import"../chunks/CWj6FrbW.js";import"../chunks/BFr-ef-6.js";import{c as Qt,o as Tt,a as ea}from"../chunks/_trTl4Xy.js";import{b as Ie,e as de,a as m,p as Fe,g as Ne,k as $,m as ce,f as h,c as t,s as a,r as e,j as r,t as z,b1 as Ye,n as S,d as Ge,v as Ct,l as ta,h as aa,i as I,$ as ra}from"../chunks/BWPOaEfG.js";import{r as sa,s as D,e as E,h as ia}from"../chunks/CZV_L0uA.js";import{i as R}from"../chunks/CpAxVSI9.js";import{e as he,i as ye}from"../chunks/DvNK1QAi.js";import{h as ze}from"../chunks/fXKYsmio.js";import{r as Pe,s as Le}from"../chunks/DqS74z4u.js";import{s as Oe}from"../chunks/QKe3DSJl.js";import{i as We}from"../chunks/CyVVhm_o.js";import{l as Ke,s as He,p as jt}from"../chunks/C3C4ffT7.js";import{a as ct,s as mt}from"../chunks/BuXe-Psm.js";import{w as vt,r as na,d as oa}from"../chunks/BKngoYAq.js";import{r as $t,l as da}from"../chunks/BGGTUj09.js";import{b as Ee}from"../chunks/CqE6vre2.js";import{X as la}from"../chunks/7vBwaWN8.js";import{B as ot,S as Et,C as Ot,U as It,T as ca}from"../chunks/BpMdh2kU.js";import{C as qe}from"../chunks/BdO0auy_.js";import{Z as ut,C as ma,B as va}from"../chunks/DOO0hYL0.js";import{T as dt}from"../chunks/BqXjQFWO.js";import{s as Je}from"../chunks/B23TJBQP.js";import{I as Ue}from"../chunks/vKJQZfWG.js";import{S as ua}from"../chunks/DpDMvrJr.js";function pa(l,x){const v=Ke(x,["children","$$slots","$$events","$$legacy"]),M=[["line",{x1:"12",x2:"12",y1:"20",y2:"10"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16"}]];Ue(l,He({name:"chart-no-axes-column-increasing"},()=>v,{get iconNode(){return M},children:(c,u)=>{var i=Ie(),o=de(i);Je(o,x,"default",{},null),m(c,i)},$$slots:{default:!0}}))}function Re(l,x){const v=Ke(x,["children","$$slots","$$events","$$legacy"]),M=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335"}],["path",{d:"m9 11 3 3L22 4"}]];Ue(l,He({name:"circle-check-big"},()=>v,{get iconNode(){return M},children:(c,u)=>{var i=Ie(),o=de(i);Je(o,x,"default",{},null),m(c,i)},$$slots:{default:!0}}))}function ga(l,x){const v=Ke(x,["children","$$slots","$$events","$$legacy"]),M=[["circle",{cx:"12",cy:"12",r:"3"}],["path",{d:"M3 7V5a2 2 0 0 1 2-2h2"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2"}]];Ue(l,He({name:"focus"},()=>v,{get iconNode(){return M},children:(c,u)=>{var i=Ie(),o=de(i);Je(o,x,"default",{},null),m(c,i)},$$slots:{default:!0}}))}function fa(l,x){const v=Ke(x,["children","$$slots","$$events","$$legacy"]),M=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["circle",{cx:"9",cy:"7",r:"4"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75"}]];Ue(l,He({name:"users"},()=>v,{get iconNode(){return M},children:(c,u)=>{var i=Ie(),o=de(i);Je(o,x,"default",{},null),m(c,i)},$$slots:{default:!0}}))}function At(l){return Object.prototype.toString.call(l)==="[object Date]"}function lt(l,x,v,M){if(typeof v=="number"||At(v)){const c=M-v,u=(v-x)/(l.dt||1/60),i=l.opts.stiffness*c,o=l.opts.damping*u,j=(i-o)*l.inv_mass,Q=(u+j)*l.dt;return Math.abs(Q)<l.opts.precision&&Math.abs(c)<l.opts.precision?M:(l.settled=!1,At(v)?new Date(v.getTime()+Q):v+Q)}else{if(Array.isArray(v))return v.map((c,u)=>lt(l,x[u],v[u],M[u]));if(typeof v=="object"){const c={};for(const u in v)c[u]=lt(l,x[u],v[u],M[u]);return c}else throw new Error(`Cannot spring ${typeof v} values`)}}function St(l,x={}){const v=vt(l),{stiffness:M=.15,damping:c=.8,precision:u=.01}=x;let i,o,j,Q=l,O=l,P=1,me=0,q=!1;function ee(te,_={}){O=te;const G=j={};return l==null||_.hard||V.stiffness>=1&&V.damping>=1?(q=!0,i=$t.now(),Q=te,v.set(l=O),Promise.resolve()):(_.soft&&(me=1/((_.soft===!0?.5:+_.soft)*60),P=0),o||(i=$t.now(),q=!1,o=da(L=>{if(q)return q=!1,o=null,!1;P=Math.min(P+me,1);const ie=Math.min(L-i,1e3/30),re={inv_mass:P,opts:V,settled:!0,dt:ie*60/1e3},p=lt(re,Q,l,O);return i=L,Q=l,v.set(l=p),re.settled&&(o=null),!re.settled})),new Promise(L=>{o.promise.then(()=>{G===j&&L()})}))}const V={set:ee,update:(te,_)=>ee(te(O,l),_),subscribe:v.subscribe,stiffness:M,damping:c,precision:u};return V}const Mt=na(0,l=>{const x=()=>l(window.scrollY);return window.addEventListener("scroll",x),x(),()=>window.removeEventListener("scroll",x)});var xa=h('<div class="p-4 rounded-lg" style="background-color: #10b981; color: white;"> </div>'),ba=h('<div class="p-4 rounded-lg" style="background-color: #ef4444; color: white;"> </div>'),ha=h(`<div class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background: rgba(0, 0, 0, 0.8);" role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1"><div class="relative w-full max-w-lg border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-xl);" role="document"><div class="flex items-center justify-between p-6 border-b-2" style="border-color: var(--border);"><h2 id="modal-title" class="text-2xl font-black" style="color: var(--foreground);">Let's Start Your Growth Story</h2> <button class="p-2 transition-colors hover:opacity-70" style="color: var(--muted-foreground);" aria-label="Close modal"><!></button></div> <form class="p-6 space-y-6"><div><label for="name" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Full Name *</label> <input id="name" type="text" required class="input-brutal w-full" placeholder="Your full name"/></div> <div><label for="email" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Email Address *</label> <input id="email" type="email" required class="input-brutal w-full" placeholder="<EMAIL>"/></div> <div><label for="website" class="block text-sm font-bold mb-2" style="color: var(--foreground);">Company Website</label> <input id="website" type="url" class="input-brutal w-full" placeholder="https://yourcompany.com"/></div> <div><label for="description" class="block text-sm font-bold mb-2" style="color: var(--foreground);">What would you like to learn or discuss? *</label> <textarea id="description" required rows="4" class="textarea-brutal w-full" placeholder="Tell us about your marketing challenges, goals, or what you'd like to explore with our team..."></textarea></div> <!> <!> <div class="flex gap-4 pt-4"><button type="submit"> </button> <button type="button" class="btn-secondary px-6 py-3 font-bold">Cancel</button></div></form></div></div>`);function ya(l,x){Fe(x,!1);let v=jt(x,"isOpen",12,!1);const M=Qt();let c=ce({name:"",email:"",website:"",description:""}),u=ce(!1),i=ce(""),o=ce("");function j(){v(!1),$(i,""),$(o,""),M("close")}function Q(){$(c,{name:"",email:"",website:"",description:""}),$(i,""),$(o,"")}async function O(_){_.preventDefault(),$(i,""),$(o,""),$(u,!0);try{const L=await(await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r(c))})).json();L.success?($(i,"Thank you! Your message has been sent successfully. We'll get back to you soon."),Q(),M("submit",{success:!0,data:L.data}),setTimeout(()=>{v()&&j()},2e3)):$(o,L.error||"Failed to send message. Please try again.")}catch(G){console.error("Form submission error:",G),$(o,"Network error. Please check your connection and try again.")}finally{$(u,!1)}}function P(_){_.target===_.currentTarget&&j()}function me(_){_.key==="Escape"&&j()}function q(_){v()&&_.key==="Escape"&&j()}Tt(()=>{typeof document<"u"&&document.addEventListener("keydown",q)}),ea(()=>{typeof document<"u"&&document.removeEventListener("keydown",q)}),We();var ee=Ie(),V=de(ee);{var te=_=>{var G=ha(),L=t(G),ie=t(L),re=a(t(ie),2),p=t(re);la(p,{class:"w-6 h-6"}),e(re),e(ie);var k=a(ie,2),d=t(k),y=a(t(d),2);Pe(y),e(d);var b=a(d,2),B=a(t(b),2);Pe(B),e(b);var F=a(b,2),ne=a(t(F),2);Pe(ne),e(F);var T=a(F,2),s=a(t(T),2);sa(s),e(T);var w=a(T,2);{var A=C=>{var X=xa(),le=t(X,!0);e(X),z(()=>D(le,r(i))),m(C,X)};R(w,C=>{r(i)&&C(A)})}var Y=a(w,2);{var ae=C=>{var X=ba(),le=t(X,!0);e(X),z(()=>D(le,r(o))),m(C,X)};R(Y,C=>{r(o)&&C(ae)})}var se=a(Y,2),N=t(se),W=t(N,!0);e(N);var K=a(N,2);e(se),e(k),e(L),e(G),z(()=>{N.disabled=r(u),Oe(N,1,`btn-primary flex-1 px-6 py-3 font-bold ${r(u)?"opacity-75 cursor-not-allowed":""}`),D(W,r(u)?"Sending...":"Send Message")}),E("click",re,j),Ee(y,()=>r(c).name,C=>Ye(c,r(c).name=C)),Ee(B,()=>r(c).email,C=>Ye(c,r(c).email=C)),Ee(ne,()=>r(c).website,C=>Ye(c,r(c).website=C)),Ee(s,()=>r(c).description,C=>Ye(c,r(c).description=C)),E("click",K,j),E("submit",k,O),E("click",G,P),E("keydown",G,me),m(_,G)};R(V,_=>{v()&&_(te)})}m(l,ee),Ne()}var wa=h('<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1"><!> <span class="text-xs font-medium text-foreground"> </span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to try →</span> <!></div></button>'),_a=h('<div class="grid gap-3 mb-4"></div> <div class="flex gap-2"><input placeholder="Or ask about any company..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"/> <button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50"><!></button></div>',1),ka=h('<div class="p-3 bg-primary/10 border border-primary/20 rounded-lg"><p class="text-xs text-foreground"> </p></div>'),$a=h('<span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded"> </span>'),Aa=h('<div class="mt-3 pt-3 border-t border-border/50"><div class="flex flex-wrap gap-1"></div></div>'),Sa=h('<div class="p-3 bg-background border border-border rounded-lg"><div class="prose prose-xs max-w-none"><!></div> <!></div>'),Ma=h('<div><div><!></div> <div class="flex-1 max-w-md"><div class="flex items-center gap-2 mb-1"><span class="text-xs font-medium text-foreground"> </span> <!> <span class="text-xs text-muted-foreground"> </span></div> <!></div></div>'),Ta=h('<div class="flex gap-3"><div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded bg-secondary"><!></div> <div class="flex-1"><div class="p-3 bg-background border border-border rounded-lg"><div class="flex items-center gap-2"><div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-200"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-400"></div> <span class="text-xs text-muted-foreground">Analyzing...</span></div></div></div></div>'),Ca=h('<div class="space-y-4 max-h-96 overflow-y-auto"><!> <!></div> <div class="mt-4 pt-4 border-t border-border/50 text-center"><p class="text-xs text-muted-foreground mb-2">This is a demo with limited capabilities</p> <button class="text-xs text-primary hover:underline">Try another query</button></div>',1),ja=h('<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded"><!></div> <h3 class="text-lg font-bold">Try Athena Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant competitive intelligence on any company</p></div> <!></div>');function Ea(l,x){Fe(x,!1);const[v,M]=ct(),c=()=>mt(u,"$messages",v),u=vt([]);let i=ce(""),o=ce(!1),j=ce(!1);const Q=["Research Stripe's recent marketing strategy","Analyze Notion's competitive positioning","Tell me about Figma's go-to-market approach"];function O(){return Math.random().toString(36).substr(2,9)}async function P(p){const k=p||r(i).trim();if(!(!k||r(o))){$(i,""),$(o,!0),$(j,!0),u.update(d=>[...d,{id:O(),role:"user",content:k,timestamp:new Date}]);try{const d=await fetch("/api/demo/researcher",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:k})}),y=await d.json();if(d.ok)u.update(b=>[...b,{id:O(),role:"assistant",content:y.response,timestamp:new Date,metadata:y.metadata}]);else throw new Error(y.error||"Failed to get response")}catch(d){console.error("Demo error:",d),u.update(y=>[...y,{id:O(),role:"assistant",content:"Sorry, there was an error processing your request. Please try again.",timestamp:new Date}])}finally{$(o,!1)}}}function me(p){p.key==="Enter"&&!p.shiftKey&&(p.preventDefault(),P())}function q(p){return p.replace(/^# (.*$)/gim,'<h1 class="text-xl font-bold mb-3" style="color: var(--foreground);">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-lg font-bold mb-2" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-base font-bold mb-2" style="color: var(--foreground);">$1</h3>').replace(/^\*\*(.*?)\*\*/gim,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^\* (.*$)/gim,'<li class="ml-4 text-sm" style="color: var(--muted-foreground);">• $1</li>').replace(/\n\n/gim,'</p><p class="text-sm mb-2" style="color: var(--muted-foreground);">').replace(/^(?!<[h|l|s])(.+)$/gim,'<p class="text-sm mb-2" style="color: var(--muted-foreground);">$1</p>')}We();var ee=ja(),V=t(ee),te=t(V),_=t(te),G=t(_);ot(G,{class:"w-4 h-4 text-primary-foreground"}),e(_),S(2),e(te),S(2),e(V);var L=a(V,2);{var ie=p=>{var k=_a(),d=de(k);he(d,5,()=>Q,ye,(s,w)=>{var A=wa(),Y=t(A),ae=t(Y);Et(ae,{class:"w-3 h-3 text-primary"});var se=a(ae,2),N=t(se,!0);e(se),e(Y);var W=a(Y,2),K=a(t(W),2);qe(K,{class:"w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"}),e(W),e(A),z(()=>D(N,r(w))),E("click",A,()=>P(r(w))),m(s,A)}),e(d);var y=a(d,2),b=t(y);Pe(b);var B=a(b,2),F=t(B);{var ne=s=>{ut(s,{class:"w-3 h-3 animate-spin"})},T=s=>{var w=Ct("Try");m(s,w)};R(F,s=>{r(o)?s(ne):s(T,!1)})}e(B),e(y),z(s=>{b.disabled=r(o),B.disabled=s},[()=>!r(i).trim()||r(o)],Ge),Ee(b,()=>r(i),s=>$(i,s)),E("keydown",b,me),E("click",B,()=>P()),m(p,k)},re=p=>{var k=Ca(),d=de(k),y=t(d);he(y,1,c,ye,(T,s)=>{var w=Ma(),A=t(w),Y=t(A);{var ae=g=>{It(g,{class:"w-3 h-3 text-primary-foreground"})},se=g=>{ot(g,{class:"w-3 h-3 text-secondary-foreground"})};R(Y,g=>{r(s).role==="user"?g(ae):g(se,!1)})}e(A);var N=a(A,2),W=t(N),K=t(W),C=t(K,!0);e(K);var X=a(K,2);Ot(X,{class:"w-2 h-2 text-muted-foreground"});var le=a(X,2),we=t(le,!0);e(le),e(W);var De=a(W,2);{var _e=g=>{var H=ka(),J=t(H),ve=t(J,!0);e(J),e(H),z(()=>D(ve,r(s).content)),m(g,H)},ke=g=>{var H=Sa(),J=t(H),ve=t(J);ze(ve,()=>q(r(s).content)),e(J);var $e=a(J,2);{var be=ue=>{var U=Aa(),ge=t(U);he(ge,5,()=>r(s).metadata.insights.tags,ye,(Ae,Se)=>{var pe=$a(),Me=t(pe,!0);e(pe),z(()=>D(Me,r(Se))),m(Ae,pe)}),e(ge),e(U),m(ue,U)};R($e,ue=>{var U;(U=r(s).metadata)!=null&&U.insights&&ue(be)})}e(H),m(g,H)};R(De,g=>{r(s).role==="user"?g(_e):g(ke,!1)})}e(N),e(w),z(g=>{Oe(w,1,`flex gap-3 ${r(s).role==="user"?"flex-row-reverse":""}`),Oe(A,1,`w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded ${r(s).role==="user"?"bg-primary":"bg-secondary"}`),D(C,r(s).role==="user"?"You":"Athena"),D(we,g)},[()=>r(s).timestamp.toLocaleTimeString()],Ge),m(T,w)});var b=a(y,2);{var B=T=>{var s=Ta(),w=t(s),A=t(w);ot(A,{class:"w-3 h-3 text-secondary-foreground"}),e(w),S(2),e(s),m(T,s)};R(b,T=>{r(o)&&T(B)})}e(d);var F=a(d,2),ne=a(t(F),2);e(F),E("click",ne,()=>{$(j,!1),u.set([])}),m(p,k)};R(L,p=>{r(j)?p(re,!1):p(ie)})}e(ee),m(l,ee),Ne(),M()}var Oa=h('<button class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"><div class="flex items-center gap-2 mb-1"><!> <span class="text-xs font-medium text-foreground"> </span></div> <div class="flex items-center justify-between"><span class="text-xs text-muted-foreground">Click to analyze →</span> <!></div></button>'),Ia=h('<div class="grid gap-3 mb-4"></div> <div class="flex gap-2"><input placeholder="e.g., vegan protein powder, sustainable fashion..." class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"/> <button class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50"><!></button></div>',1),Da=h('<div class="p-3 bg-primary/10 border border-primary/20 rounded-lg"><p class="text-xs text-foreground"> </p></div>'),La=h('<span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded"> </span>'),Pa=h('<div class="mt-3 pt-3 border-t border-border/50"><div class="flex items-center gap-2 mb-2"><!> <span class="text-xs font-medium text-foreground">Top Keywords</span></div> <div class="flex flex-wrap gap-1"></div></div>'),Ga=h('<div class="p-3 bg-background border border-border rounded-lg"><div class="prose prose-xs max-w-none"><!></div> <!></div>'),Ba=h('<div><div><!></div> <div class="flex-1 max-w-md"><div class="flex items-center gap-2 mb-1"><span class="text-xs font-medium text-foreground"> </span> <!> <span class="text-xs text-muted-foreground"> </span></div> <!></div></div>'),Ya=h('<div class="flex gap-3"><div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded bg-secondary"><!></div> <div class="flex-1"><div class="p-3 bg-background border border-border rounded-lg"><div class="flex items-center gap-2"><div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-200"></div> <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-400"></div> <span class="text-xs text-muted-foreground">Researching keywords...</span></div></div></div></div>'),Ra=h('<div class="space-y-4 max-h-96 overflow-y-auto"><!> <!></div> <div class="mt-4 pt-4 border-t border-border/50 text-center"><p class="text-xs text-muted-foreground mb-2">This is a demo with limited capabilities</p> <button class="text-xs text-primary hover:underline">Try another query</button></div>',1),za=h('<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center mb-6"><div class="flex items-center justify-center gap-2 mb-4"><div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded"><!></div> <h3 class="text-lg font-bold">Try Lexi Live</h3></div> <p class="text-sm text-muted-foreground mb-4">Get instant SEO keyword analysis for any topic</p></div> <!></div>');function qa(l,x){Fe(x,!1);const[v,M]=ct(),c=()=>mt(u,"$messages",v),u=vt([]);let i=ce(""),o=ce(!1),j=ce(!1);const Q=["Find long-tail keywords for organic skincare products","Analyze local SEO keywords for coffee shops in Seattle","Research B2B keywords for project management software"];function O(){return Math.random().toString(36).substr(2,9)}async function P(p){const k=p||r(i).trim();if(!(!k||r(o))){$(i,""),$(o,!0),$(j,!0),u.update(d=>[...d,{id:O(),role:"user",content:k,timestamp:new Date}]);try{const d=await fetch("/api/demo/seo",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:k})}),y=await d.json();if(d.ok)u.update(b=>[...b,{id:O(),role:"assistant",content:y.response,timestamp:new Date,metadata:y.metadata}]);else throw new Error(y.error||"Failed to get response")}catch(d){console.error("Demo error:",d),u.update(y=>[...y,{id:O(),role:"assistant",content:d instanceof Error&&d.message.includes("rate limit")?"You've reached the demo limit (5 requests per hour). Sign up for unlimited access!":"Sorry, there was an error processing your request. Please try again.",timestamp:new Date}])}finally{$(o,!1)}}}function me(p){p.key==="Enter"&&!p.shiftKey&&(p.preventDefault(),P())}function q(p){return p.replace(/^# (.*$)/gim,'<h1 class="text-xl font-bold mb-3" style="color: var(--foreground);">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-lg font-bold mb-2" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-base font-bold mb-2" style="color: var(--foreground);">$1</h3>').replace(/^\*\*(.*?)\*\*/gim,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^[\-\*] (.*$)/gim,'<li class="ml-4 text-sm" style="color: var(--muted-foreground);">• $1</li>').replace(/\n\n/gim,'</p><p class="text-sm mb-2" style="color: var(--muted-foreground);">').replace(/^(?!<[h|l|s])(.+)$/gim,'<p class="text-sm mb-2" style="color: var(--muted-foreground);">$1</p>').replace(/\|(.+)\|/g,k=>{const d=k.split("|").filter(b=>b.trim());return d.some(b=>b.trim().match(/^[\-:]+$/))?"":`<tr>${d.map(b=>`<td class="px-2 py-1 text-xs border-b border-border/50">${b.trim()}</td>`).join("")}</tr>`})}We();var ee=za(),V=t(ee),te=t(V),_=t(te),G=t(_);dt(G,{class:"w-4 h-4 text-primary-foreground"}),e(_),S(2),e(te),S(2),e(V);var L=a(V,2);{var ie=p=>{var k=Ia(),d=de(k);he(d,5,()=>Q,ye,(s,w)=>{var A=Oa(),Y=t(A),ae=t(Y);Et(ae,{class:"w-3 h-3 text-primary"});var se=a(ae,2),N=t(se,!0);e(se),e(Y);var W=a(Y,2),K=a(t(W),2);qe(K,{class:"w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors"}),e(W),e(A),z(()=>D(N,r(w))),E("click",A,()=>P(r(w))),m(s,A)}),e(d);var y=a(d,2),b=t(y);Pe(b);var B=a(b,2),F=t(B);{var ne=s=>{ut(s,{class:"w-3 h-3 animate-spin"})},T=s=>{var w=Ct("Analyze");m(s,w)};R(F,s=>{r(o)?s(ne):s(T,!1)})}e(B),e(y),z(s=>{b.disabled=r(o),B.disabled=s},[()=>!r(i).trim()||r(o)],Ge),Ee(b,()=>r(i),s=>$(i,s)),E("keydown",b,me),E("click",B,()=>P()),m(p,k)},re=p=>{var k=Ra(),d=de(k),y=t(d);he(y,1,c,ye,(T,s)=>{var w=Ba(),A=t(w),Y=t(A);{var ae=g=>{It(g,{class:"w-3 h-3 text-primary-foreground"})},se=g=>{dt(g,{class:"w-3 h-3 text-secondary-foreground"})};R(Y,g=>{r(s).role==="user"?g(ae):g(se,!1)})}e(A);var N=a(A,2),W=t(N),K=t(W),C=t(K,!0);e(K);var X=a(K,2);Ot(X,{class:"w-2 h-2 text-muted-foreground"});var le=a(X,2),we=t(le,!0);e(le),e(W);var De=a(W,2);{var _e=g=>{var H=Da(),J=t(H),ve=t(J,!0);e(J),e(H),z(()=>D(ve,r(s).content)),m(g,H)},ke=g=>{var H=Ga(),J=t(H),ve=t(J);ze(ve,()=>q(r(s).content)),e(J);var $e=a(J,2);{var be=ue=>{var U=Pa(),ge=t(U),Ae=t(ge);pa(Ae,{class:"w-3 h-3 text-primary"}),S(2),e(ge);var Se=a(ge,2);he(Se,5,()=>r(s).metadata.keywords.slice(0,5),ye,(pe,Me)=>{var Te=La(),Be=t(Te,!0);e(Te),z(()=>D(Be,r(Me))),m(pe,Te)}),e(Se),e(U),m(ue,U)};R($e,ue=>{var U;(U=r(s).metadata)!=null&&U.keywords&&ue(be)})}e(H),m(g,H)};R(De,g=>{r(s).role==="user"?g(_e):g(ke,!1)})}e(N),e(w),z(g=>{Oe(w,1,`flex gap-3 ${r(s).role==="user"?"flex-row-reverse":""}`),Oe(A,1,`w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded ${r(s).role==="user"?"bg-primary":"bg-secondary"}`),D(C,r(s).role==="user"?"You":"Lexi"),D(we,g)},[()=>r(s).timestamp.toLocaleTimeString()],Ge),m(T,w)});var b=a(y,2);{var B=T=>{var s=Ya(),w=t(s),A=t(w);dt(A,{class:"w-3 h-3 text-secondary-foreground"}),e(w),S(2),e(s),m(T,s)};R(b,T=>{r(o)&&T(B)})}e(d);var F=a(d,2),ne=a(t(F),2);e(F),E("click",ne,()=>{$(j,!1),u.set([])}),m(p,k)};R(L,p=>{r(j)?p(re,!1):p(ie)})}e(ee),m(l,ee),Ne(),M()}var Fa=h('<meta name="description"/> <meta property="og:title"/> <meta property="og:description"/> <meta property="og:image" content="/og-preview.jpg"/> <meta property="og:image:width" content="1200"/> <meta property="og:image:height" content="630"/> <meta property="og:image:alt" content="Robynn AI - Marketing Agents for Startups"/> <meta property="og:type" content="website"/> <meta property="og:url" content="https://robynn.ai"/> <meta name="twitter:card" content="summary_large_image"/> <meta name="twitter:title"/> <meta name="twitter:description"/> <meta name="twitter:image" content="/og-preview.jpg"/> <link rel="preconnect" href="https://fonts.googleapis.com"/> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/> <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=JetBrains+Mono:wght@400;500;600&amp;display=swap" rel="stylesheet"/>',1),Na=h('<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight"> <span class="text-primary"> </span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto"><!></p>',1),Wa=h(`<h1 class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight">The <span class="text-primary">10X fractional CMO</span><br/> team you've been <span class="text-primary">looking for</span></h1> <p class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto">Strategic 10x marketing team that scales with your ambitions. We
            combine seasoned CMO expertise with cutting-edge AI to transform
            your go-to-market strategy build your GTM machine.</p>`,1),Ka=h('<span class="linear-tag linear-tag-green"> </span>'),Ha=h('<span class="linear-tag linear-tag-green">Strategic Partnership</span> <span class="linear-tag linear-tag-purple">AI-Powered</span>',1),Ja=h('<div class="linear-card linear-fade-in p-6 rounded-lg"><span> </span> <h3 class="linear-heading text-xl mb-4"> </h3> <p class="linear-body text-muted-foreground"><!></p></div>'),Ua=h(`<div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Content Agent</span> <h3 class="linear-heading text-xl mb-4">Custom Content Agent</h3> <p class="linear-body text-muted-foreground">Generate SEO optimized content in your brand voice. This agent
              analyzes your existing content, understands your unique voice and
              tone, then creates high-performing content that ranks while
              staying true to your brand identity.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-blue mb-4 inline-block">Research Agent</span> <h3 class="linear-heading text-xl mb-4">Competitive Researcher</h3> <p class="linear-body text-muted-foreground">Custom agent to do deep competitive analysis on real-time data.
              Continuously monitors your competitors' strategies, pricing,
              content, and market positioning to give you actionable insights
              and strategic advantages.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-purple mb-4 inline-block">Social Agent</span> <h3 class="linear-heading text-xl mb-4">Social Media Agent</h3> <p class="linear-body text-muted-foreground">Listen to your brand and your competitors' signals, assess
              sentiment and act upon them. This agent monitors social
              conversations, tracks brand mentions, analyzes sentiment trends,
              and provides real-time insights to optimize your social media
              strategy.</p></div>`,1),Va=h(`<div class="min-h-screen bg-background text-foreground"><nav class="linear-nav fixed top-0 left-0 right-0 z-50 px-6 py-4"><div class="max-w-7xl mx-auto flex items-center justify-between"><div class="flex items-center space-x-8"><div class="linear-heading text-xl font-bold text-foreground">Robynn.ai</div> <div class="hidden md:flex items-center space-x-6"><button class="nav-link text-foreground font-medium transition-all">Approach</button> <button class="nav-link text-foreground font-medium transition-all">Services</button> <button class="nav-link text-foreground font-medium transition-all">Agents</button> <button class="nav-link text-foreground font-medium transition-all">Results</button> <button class="nav-link text-foreground font-medium transition-all">Stories</button></div></div> <button class="linear-btn-primary px-6 py-2 rounded-lg">↗ Get Started</button></div></nav> <section class="linear-hero min-h-screen flex items-center justify-center px-6 py-20"><div class="max-w-7xl mx-auto text-center"><div class="linear-fade-in mb-8"><!></div> <div class="linear-fade-in mb-12"><div class="flex flex-wrap justify-center gap-3 mb-8"><!></div> <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center"> <!></button> <a href="#agents" class="linear-btn-secondary px-8 py-3 text-lg rounded-lg inline-flex items-center justify-center">See Agents In Action</a></div> <p class="linear-mono text-sm text-muted-foreground"> </p></div> <div class="linear-fade-in"><div class="max-w-4xl mx-auto"><h3 class="linear-heading text-2xl text-center mb-8 flex items-center justify-center gap-3"><!> Everything you need. Nothing you don't.</h3> <div class="grid md:grid-cols-3 gap-6 mb-6"><div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded text-sm font-medium mb-4">Fractional CMO</div> <p class="linear-body text-muted-foreground">Strategic clarity without full-time overhead</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-blue-500/10 text-blue-600 rounded text-sm font-medium mb-4">Expert Marketers</div> <p class="linear-body text-muted-foreground">Campaigns crafted by hands-on pros</p></div></div> <div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"><div class="text-center"><div class="inline-flex items-center px-4 py-2 bg-purple-500/10 text-purple-600 rounded text-sm font-medium mb-4">AI Agents</div> <p class="linear-body text-muted-foreground">Automations that move fast—and scale faster</p></div></div></div> <div class="text-center"><p class="linear-mono text-sm text-muted-foreground/80">Custom-assembled for your company. No bloat. No fluff.</p></div></div></div></div></section> <div class="linear-section-separator"></div> <section class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">You've built something <span class="text-primary">remarkable</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">You're past the early stage hustle. Your product works. Your customers
          love it. You've hit that magical 5M+ revenue milestone. But now you're
          facing a new challenge.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Expensive Team Building</h3> <p class="linear-body text-muted-foreground">VP of Marketing + team costs $500K+ annually</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Scattered Marketing</h3> <p class="linear-body text-muted-foreground">Tactics without strategy, campaigns without cohesion</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Unclear ROI</h3> <p class="linear-body text-muted-foreground">Spending money without knowing what's working</p></div></div></div></section> <div class="linear-dotted-line"></div> <section id="approach" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Think different about marketing <br/> <span class="text-primary">in the world of AI</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">While others offer fractional hours, we offer strategic embedded team
          members and custom agents for your team. Our CMO-led team doesn't just
          provide strategy, we build your entire GTM machine from the tech stack
          to a GTM campaign system built for your needs.</p></div> <div class="grid md:grid-cols-3 gap-8 mb-16"><div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">01</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Embedded Team</span> <h3 class="linear-heading text-xl mb-4">Your marketing co-founder and your agents when and where you need
            them</h3> <p class="linear-body text-muted-foreground">We think like an owner, act like a partner, and deliver like the CMO
            you've been looking for.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">02</div> <span class="linear-tag linear-tag-purple mb-4 inline-block">AI-Native Approach</span> <h3 class="linear-heading text-xl mb-4">Built for efficiency, powered by intelligence. Automation for
            scaling up.</h3> <p class="linear-body text-muted-foreground">Our methodology leverages AI at every step, making us faster and
            more efficient than traditional approaches.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><div class="linear-mono text-4xl font-bold text-primary mb-4">03</div> <span class="linear-tag linear-tag-green mb-4 inline-block">Continuous Learning</span> <h3 class="linear-heading text-xl mb-4">End-to-end campaigns tried and tested to generate pipelines and
            revenue</h3> <p class="linear-body text-muted-foreground">Deep product understanding drives differentiated messaging that
            actually resonates with your market.</p></div></div> <div class="linear-card linear-fade-in p-8 rounded-lg"><h3 class="linear-heading text-2xl text-center mb-8">Strategic Leadership, Not Just Services</h3> <p class="linear-body text-center text-muted-foreground mb-8">You get the strategic thinking of a seasoned CMO, the execution power
          of a full marketing team, and the efficiency of AI-native tools. All
          without the politics, overhead, or six-figure salaries.</p> <div class="grid md:grid-cols-4 gap-6"><div class="text-center"><!> <div class="linear-body font-semibold">Strategy</div> <div class="linear-body text-sm text-muted-foreground">GTM Strategy & Execution</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Messaging</div> <div class="linear-body text-sm text-muted-foreground">Differentiated Positioning</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Campaigns</div> <div class="linear-body text-sm text-muted-foreground">AI-Powered 1-to-1 Marketing</div></div> <div class="text-center"><!> <div class="linear-body font-semibold">Growth</div> <div class="linear-body text-sm text-muted-foreground">Predictable Pipeline</div></div></div></div></div></section> <section id="services" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Our <span class="text-primary">Services</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">We don't just advise—we build, deploy, and run the complete marketing
          infrastructure your growing business needs to scale efficiently.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Custom Marketing Agents</h3> <p class="linear-body text-muted-foreground">Build and run your custom marketing agents centered around your
            product, your audience and your business requirements.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">AI Campaign Orchestration</h3> <p class="linear-body text-muted-foreground">Build and setup an entire AI based campaign orchestration machine
            for your business.</p></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><!> <h3 class="linear-heading text-xl mb-4">Complete Marketing Tech Stack</h3> <p class="linear-body text-muted-foreground">Build, setup, and run your entire marketing tech stack from website
            to AI-powered automation, data pipelines and analytics dashboard.</p></div></div></div></section> <section id="agents" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Marketing <span class="text-primary">Agents</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Our AI Agents That Scale Your Marketing. Meet your new marketing team.
          Each agent is specifically designed to handle complex marketing tasks
          that typically require hours of manual work, delivering results in
          minutes with unprecedented accuracy and insight.</p></div> <div class="grid md:grid-cols-3 gap-8"><!></div> <div class="mt-16 max-w-4xl mx-auto linear-fade-in"><div class="text-center mb-8"><h3 class="linear-heading text-2xl font-bold mb-4">See Our Agents in Action</h3> <p class="linear-body text-muted-foreground">Try Athena, our competitive researcher, and see real-time analysis
            in action.</p></div> <!></div> <div class="max-w-3xl mx-auto mt-16"><div class="text-center mb-8"><h3 class="linear-heading text-3xl font-bold mb-4">🎯 SEO Keyword Research Demo</h3> <p class="linear-body text-muted-foreground">Try Lexi, our SEO strategist, and discover high-value keywords
            instantly.</p></div> <!></div></div></section> <section id="results" class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Growth you can <span class="text-primary">measure</span>.<br/> Impact you can <span class="text-primary">feel</span>.</h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Our clients typically see 40% improvement in marketing efficiency
          within 90 days, 60% reduction in customer acquisition costs within 6
          months, and 3x pipeline growth within the first year.</p></div> <div class="grid md:grid-cols-4 gap-8"><div class="linear-chart text-center linear-fade-in"><div class="linear-metric">40%</div> <div class="linear-body text-muted-foreground">Marketing Efficiency</div> <div class="linear-mono text-sm text-muted-foreground mt-2">within 90 days</div></div> <div class="linear-chart text-center linear-fade-in"><div class="linear-metric">60%</div> <div class="linear-body text-muted-foreground">CAC Reduction</div> <div class="linear-mono text-sm text-muted-foreground mt-2">within 6 months</div></div> <div class="linear-chart text-center linear-fade-in"><div class="linear-metric">3x</div> <div class="linear-body text-muted-foreground">Pipeline Growth</div> <div class="linear-mono text-sm text-muted-foreground mt-2">within first year</div></div> <div class="linear-chart text-center linear-fade-in"><div class="linear-metric">90%</div> <div class="linear-body text-muted-foreground">Client Satisfaction</div> <div class="linear-mono text-sm text-muted-foreground mt-2">measurable results</div></div></div></div></section> <section id="stories" class="py-20 px-6 bg-card/50"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Real companies. <span class="text-primary">Real results</span>.</h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">See how we've helped AI and technology companies transform their
          go-to-market strategy and achieve remarkable growth.</p></div> <div class="grid md:grid-cols-2 gap-8"><div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-green mb-4 inline-block">Protecto</span> <h3 class="linear-heading text-xl mb-4">Data Guardrails for Enterprise AI Agents</h3> <p class="linear-body text-muted-foreground mb-6">A brilliant AI privacy startup helping prevent data leaks, privacy
            violations, and compliance risks in AI automation couldn't break
            through in a crowded market. We repositioned them around "Data
            Guardrails" and built a comprehensive GTM strategy.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"Working with Robynn was like having a strategic co-founder who
              understood both our technology and our market better than we did."</p> <div class="mt-4"><div class="linear-body font-semibold">Amar Kanagaraj</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Protecto</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">300%</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Growth</div></div> <div><div class="linear-mono text-xl font-bold text-primary">60%</div> <div class="linear-body text-sm text-muted-foreground">Shorter Cycles</div></div> <div><div class="linear-mono text-xl font-bold text-primary">3x</div> <div class="linear-body text-sm text-muted-foreground">Valuation</div></div></div></div> <div class="linear-card linear-fade-in p-6 rounded-lg"><span class="linear-tag linear-tag-pink mb-4 inline-block">Apptware</span> <h3 class="linear-heading text-xl mb-4">Design first AI Services</h3> <p class="linear-body text-muted-foreground mb-6">A successful AI services company was struggling to gain traction in
            the US market. We rebuilt their market entry strategy from the
            ground up, created campaigns and helped them achieve a 2X pipeline
            growth.</p> <div class="linear-testimonial mb-6"><p class="linear-body italic text-muted-foreground">"They transformed our US market entry from a costly experiment
              into our fastest-growing revenue stream."</p> <div class="mt-4"><div class="linear-body font-semibold">Harish Rohokale</div> <div class="linear-mono text-sm text-muted-foreground">CEO, Apptware</div></div></div> <div class="grid grid-cols-3 gap-4 text-center"><div><div class="linear-mono text-xl font-bold text-primary">$8M</div> <div class="linear-body text-sm text-muted-foreground">Pipeline Built</div></div> <div><div class="linear-mono text-xl font-bold text-primary">150%</div> <div class="linear-body text-sm text-muted-foreground">Goal Exceeded</div></div> <div><div class="linear-mono text-xl font-bold text-primary">4</div> <div class="linear-body text-sm text-muted-foreground">Months</div></div></div></div></div></div></section> <section class="py-20 px-6 linear-grid"><div class="max-w-7xl mx-auto"><div class="text-center mb-16 linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">The Robynn <span class="text-primary">Team</span></h2> <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">Meet the strategic minds behind your growth. We're not just
          marketers—we're growth architects with deep expertise in AI,
          technology, and scaling businesses.</p></div> <div class="grid md:grid-cols-3 gap-8"><div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-primary">MK</span></div> <h3 class="linear-heading text-xl mb-2">Madhukar Kumar</h3> <p class="linear-body text-muted-foreground mb-4">CEO, Co-Founder</p> <p class="linear-body text-muted-foreground mb-4">Former CMO at two unicorn startups. Two decades plus years scaling
            B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led
            growth and AI-native marketing strategies including building
            compelling and memorable brands.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-green">Go-to-Market Strategy</span> <span class="linear-tag linear-tag-purple">Product-Led Growth</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-blue-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-blue-400">JH</span></div> <h3 class="linear-heading text-xl mb-2">Joel Horwitz</h3> <p class="linear-body text-muted-foreground mb-4">The AI Demand Gen Maestro</p> <p class="linear-body text-muted-foreground mb-4">Former Head of Growth at Series B AI company. Built marketing
            automation systems that scaled 10x without proportional team growth.
            Expert in AI Demand Gen Orchestration.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-blue">Marketing Automation</span> <span class="linear-tag linear-tag-orange">Demand Generation</span></div></div> <div class="linear-team-card linear-fade-in p-6"><div class="w-16 h-16 bg-orange-400/20 rounded-full flex items-center justify-center mb-4"><span class="linear-mono text-xl font-bold text-orange-400">MT</span></div> <h3 class="linear-heading text-xl mb-2">Matt Tanner</h3> <p class="linear-body text-muted-foreground mb-4">SEO and Content 10Xer</p> <p class="linear-body text-muted-foreground mb-4">Software engineer turned SEO expert. Analyze and optimize content
            for AI-powered search engines. Helped large and small companies
            scale their organic traffic by orders of magnitude.</p> <div class="flex flex-wrap gap-2"><span class="linear-tag linear-tag-orange">AI Implementation</span> <span class="linear-tag linear-tag-purple">SEO</span></div></div></div></div></section> <section class="py-20 px-6 bg-card/50"><div class="max-w-4xl mx-auto text-center linear-fade-in"><h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">Your next chapter starts with a <span class="text-primary">conversation</span></h2> <p class="linear-body text-xl text-muted-foreground mb-12">We don't believe in hard sells or high-pressure tactics. We believe in
        finding the right fit. If you're ready to transform your marketing from
        a cost center into a growth engine, let's talk.</p> <div class="flex justify-center"><button class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center">Start Your Growth Journey <!></button></div> <p class="linear-mono text-sm text-muted-foreground mt-8">Ready to grow smarter, not just faster?</p></div></section></div> <!>`,1);function wr(l,x){Fe(x,!1);const[v,M]=ct(),c=()=>mt(Mt,"$scrollY",v);let u=jt(x,"data",8);const{homeContent:i,meta:o}=u();oa(Mt,n=>n>50);const j=St(1),Q=St(1);let O=ce(!1);function P(){$(O,!0)}function me(n){console.log("Contact form submitted:",n.detail),$(O,!1)}function q(n){var f;(f=document.getElementById(n))==null||f.scrollIntoView({behavior:"smooth"})}function ee(n){const f=["linear-tag-green","linear-tag-blue","linear-tag-purple"];return f[n%f.length]}function V(n){return n.toLowerCase().includes("content")||n.toLowerCase().includes("seo")?"Content Agent":n.toLowerCase().includes("competitive")||n.toLowerCase().includes("research")?"Research Agent":n.toLowerCase().includes("social")||n.toLowerCase().includes("campaign")||n.toLowerCase().includes("orchestrator")?"Social Agent":"Agent"}Tt(()=>{const n=()=>{document.querySelectorAll(".linear-fade-in").forEach(Z=>{Z.getBoundingClientRect().top<window.innerHeight-150&&Z.classList.add("visible")})};return window.addEventListener("scroll",n),n(),()=>{window.removeEventListener("scroll",n)}}),ta(()=>c(),()=>{j.set(1-Math.min(c(),300)/300),Q.set(1-Math.min(c(),300)/300*.2)}),aa(),We();var te=Va();ia(n=>{var f=Fa(),Z=de(f),fe=a(Z,2),oe=a(fe,2),Ce=a(oe,16),xe=a(Ce,2);S(8),z(()=>{ra.title=I(()=>o.title),Le(Z,"content",I(()=>o.description)),Le(fe,"content",I(()=>o.title)),Le(oe,"content",I(()=>o.description)),Le(Ce,"content",I(()=>o.title)),Le(xe,"content",I(()=>o.description))}),m(n,f)});var _=de(te),G=t(_),L=t(G),ie=t(L),re=a(t(ie),2),p=t(re),k=a(p,2),d=a(k,2),y=a(d,2),b=a(y,2);e(re),e(ie);var B=a(ie,2);e(L),e(G);var F=a(G,2),ne=t(F),T=t(ne),s=t(T);{var w=n=>{var f=Na(),Z=de(f),fe=t(Z,!0),oe=a(fe),Ce=t(oe,!0);e(oe),e(Z);var xe=a(Z,2),je=t(xe);ze(je,()=>I(()=>i.hero.content)),e(xe),z(()=>{D(fe,I(()=>i.hero.frontmatter.title)),D(Ce,I(()=>i.hero.frontmatter.titleHighlight))}),m(n,f)},A=n=>{var f=Wa();S(2),m(n,f)};R(s,n=>{I(()=>i==null?void 0:i.hero)?n(w):n(A,!1)})}e(T);var Y=a(T,2),ae=t(Y),se=t(ae);{var N=n=>{var f=Ka(),Z=t(f,!0);e(f),z(()=>D(Z,I(()=>i.hero.frontmatter.badge))),m(n,f)},W=n=>{var f=Ha();S(2),m(n,f)};R(se,n=>{I(()=>{var f,Z;return(Z=(f=i==null?void 0:i.hero)==null?void 0:f.frontmatter)==null?void 0:Z.badge})?n(N):n(W,!1)})}e(ae);var K=a(ae,2),C=t(K),X=t(C),le=a(X);qe(le,{class:"ml-2 h-4 w-4"}),e(C),S(2),e(K);var we=a(K,2),De=t(we,!0);e(we),e(Y);var _e=a(Y,2),ke=t(_e),g=t(ke),H=t(g);ua(H,{class:"h-6 w-6 text-primary"}),S(),e(g),S(4),e(ke),e(_e),e(ne),e(F);var J=a(F,4),ve=t(J),$e=a(t(ve),2),be=t($e),ue=t(be);fa(ue,{class:"h-8 w-8 text-primary mb-4"}),S(4),e(be);var U=a(be,2),ge=t(U);ga(ge,{class:"h-8 w-8 text-blue-400 mb-4"}),S(4),e(U);var Ae=a(U,2),Se=t(Ae);ma(Se,{class:"h-8 w-8 text-orange-400 mb-4"}),S(4),e(Ae),e($e),e(ve),e(J);var pe=a(J,4),Me=t(pe),Te=a(t(Me),4),Be=a(t(Te),4),Ve=t(Be),Dt=t(Ve);Re(Dt,{class:"h-6 w-6 text-primary mx-auto mb-2"}),S(4),e(Ve);var Xe=a(Ve,2),Lt=t(Xe);Re(Lt,{class:"h-6 w-6 text-primary mx-auto mb-2"}),S(4),e(Xe);var Ze=a(Xe,2),Pt=t(Ze);Re(Pt,{class:"h-6 w-6 text-primary mx-auto mb-2"}),S(4),e(Ze);var pt=a(Ze,2),Gt=t(pt);Re(Gt,{class:"h-6 w-6 text-primary mx-auto mb-2"}),S(4),e(pt),e(Be),e(Te),e(Me),e(pe);var Qe=a(pe,2),gt=t(Qe),ft=a(t(gt),2),et=t(ft),Bt=t(et);va(Bt,{class:"h-8 w-8 text-primary mb-4"}),S(4),e(et);var tt=a(et,2),Yt=t(tt);ut(Yt,{class:"h-8 w-8 text-blue-400 mb-4"}),S(4),e(tt);var xt=a(tt,2),Rt=t(xt);ca(Rt,{class:"h-8 w-8 text-orange-400 mb-4"}),S(4),e(xt),e(ft),e(gt),e(Qe);var at=a(Qe,2),bt=t(at),rt=a(t(bt),2),zt=t(rt);{var qt=n=>{var f=Ie(),Z=de(f);he(Z,1,()=>I(()=>i.marketingAgents),ye,(fe,oe,Ce)=>{var xe=Ja(),je=t(xe),Jt=t(je,!0);e(je);var nt=a(je,2),Ut=t(nt,!0);e(nt);var kt=a(nt,2),Vt=t(kt);ze(Vt,()=>(r(oe),I(()=>r(oe).content))),e(kt),e(xe),z((Xt,Zt)=>{Oe(je,1,`linear-tag ${Xt??""} mb-4 inline-block`),D(Jt,Zt),D(Ut,(r(oe),I(()=>r(oe).frontmatter.name)))},[()=>I(()=>ee(Ce)),()=>(r(oe),I(()=>V(r(oe).frontmatter.name)))],Ge),m(fe,xe)}),m(n,f)},Ft=n=>{var f=Ua();S(4),m(n,f)};R(zt,n=>{I(()=>(i==null?void 0:i.marketingAgents)&&i.marketingAgents.length>0)?n(qt):n(Ft,!1)})}e(rt);var st=a(rt,2),Nt=a(t(st),2);Ea(Nt,{}),e(st);var ht=a(st,2),Wt=a(t(ht),2);qa(Wt,{}),e(ht),e(bt),e(at);var yt=a(at,8),wt=t(yt),_t=a(t(wt),4),it=t(_t),Kt=a(t(it));qe(Kt,{class:"ml-2 h-4 w-4"}),e(it),e(_t),S(2),e(wt),e(yt),e(_);var Ht=a(_,2);ya(Ht,{get isOpen(){return r(O)},set isOpen(n){$(O,n)},$$events:{close:()=>$(O,!1),submit:me},$$legacy:!0}),z(()=>{D(X,`${I(()=>{var n,f;return((f=(n=i==null?void 0:i.hero)==null?void 0:n.frontmatter)==null?void 0:f.ctaPrimary)||"Start Your Growth Story"})??""} `),D(De,I(()=>{var n,f;return((f=(n=i==null?void 0:i.hero)==null?void 0:n.frontmatter)==null?void 0:f.trustBadge)||"Trusted by 50+ scaling startups"}))}),E("click",p,()=>q("approach")),E("click",k,()=>q("services")),E("click",d,()=>q("agents")),E("click",y,()=>q("results")),E("click",b,()=>q("stories")),E("click",B,P),E("click",C,P),E("click",it,P),m(l,te),Ne(),M()}export{wr as component};
