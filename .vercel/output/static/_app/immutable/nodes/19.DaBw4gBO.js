import"../chunks/CWj6FrbW.js";import{p as n,f as o,s as r,e as s,a as d,g as u,$ as f,j as p,o as c}from"../chunks/wnqW1tdD.js";import{h as g}from"../chunks/CDPCzm7q.js";import{S as h}from"../chunks/udb0C_eZ.js";import{p as b}from"../chunks/B_9MMhPz.js";var _=o('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function w(i,a){n(a,!0);let{profile:e}=a.data;var t=_();g(x=>{f.title="Edit Profile"});var l=r(s(t),2);const m=c(()=>[{id:"full_name",label:"Name",initialValue:(e==null?void 0:e.full_name)??"",placeholder:"Your full name",maxlength:50},{id:"company_name",label:"Company Name",initialValue:(e==null?void 0:e.company_name)??"",maxlength:50},{id:"website",label:"Company Website",initialValue:(e==null?void 0:e.website)??"",maxlength:50}]);h(l,{get data(){return a.data.form},get schema(){return b},editable:!0,title:"Edit Profile",successTitle:"Saved Profile",formTarget:"/api?/updateProfile",get fields(){return p(m)}}),d(i,t),u()}export{w as component};
