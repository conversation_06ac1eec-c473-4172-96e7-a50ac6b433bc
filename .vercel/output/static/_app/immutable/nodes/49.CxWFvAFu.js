import"../chunks/CWj6FrbW.js";import"../chunks/BFr-ef-6.js";import{b7 as Ke,as as Ze,ar as Ge,p as ie,l as D,h as ye,b as Y,e as N,a as v,g as de,aM as O,k as $e,m as Ae,j as n,f as E,c as I,r as S,Y as He,i as re,d as We,s as q,$ as Xe,t as _e,n as oe,v as Q,aR as Ee}from"../chunks/BWPOaEfG.js";import{e as Ue,h as et,s as Pe}from"../chunks/CZV_L0uA.js";import{i as W}from"../chunks/CpAxVSI9.js";import{e as tt,i as rt}from"../chunks/DvNK1QAi.js";import{a as ae,s as at}from"../chunks/DqS74z4u.js";import{P as st}from"../chunks/3zf3rszl.js";import{W as nt}from"../chunks/QNe64B_9.js";import{s as V}from"../chunks/B23TJBQP.js";import{a as se}from"../chunks/BkFJa9ZQ.js";import{b as ne}from"../chunks/BRSKcKbq.js";import{i as ce}from"../chunks/CyVVhm_o.js";import{l as j,p as x,s as Fe}from"../chunks/C3C4ffT7.js";import{a as Ce,s as le}from"../chunks/BuXe-Psm.js";import{o as lt,m as be,f as ot,g as Se,e as it,a as Be,i as ze,s as dt,k as J,h as ct}from"../chunks/czcKNw3K.js";import{d as ut,w as Qe}from"../chunks/BKngoYAq.js";import{t as vt,g as ft,o as gt,a as Le,r as mt,b as ht}from"../chunks/BfHyI4yz.js";import{c as _t,a as bt}from"../chunks/P8b6Hj80.js";import{c as ve}from"../chunks/DxWxHZQT.js";import{b as pt}from"../chunks/ChsRvhx6.js";import{C as xt}from"../chunks/BNAV5Mmy.js";import{t as pe}from"../chunks/Wl6JoDw7.js";import{s as yt}from"../chunks/CiB29Aqe.js";const{name:xe,selector:Ye}=ot("accordion"),$t={multiple:!1,disabled:!1,forceVisible:!1},At=g=>{const e={...$t,...g},_=vt(lt(e,"value","onValueChange","defaultValue")),b=ft(["root"]),{disabled:p,forceVisible:$}=_,A=e.value??Qe(e.defaultValue),o=gt(A,e==null?void 0:e.onValueChange),d=(t,r)=>r===void 0?!1:typeof r=="string"?r===t:r.includes(t),l=ut(o,t=>r=>d(r,t)),h=be(xe(),{returned:()=>({"data-melt-id":b.root})}),m=t=>typeof t=="string"?{value:t}:t,y=t=>typeof t=="number"?{level:t}:t,T=be(xe("item"),{stores:o,returned:t=>r=>{const{value:a,disabled:s}=m(r);return{"data-state":d(a,t)?"open":"closed","data-disabled":Se(s)}}}),C=be(xe("trigger"),{stores:[o,p],returned:([t,r])=>a=>{const{value:s,disabled:c}=m(a);return{disabled:Se(r||c),"aria-expanded":!!d(s,t),"aria-disabled":!!c,"data-disabled":Se(c),"data-value":s,"data-state":d(s,t)?"open":"closed"}},action:t=>({destroy:it(Be(t,"click",()=>{const a=t.dataset.disabled==="true",s=t.dataset.value;a||!s||f(s)}),Be(t,"keydown",a=>{if(![J.ARROW_DOWN,J.ARROW_UP,J.HOME,J.END].includes(a.key))return;if(a.preventDefault(),a.key===J.SPACE||a.key===J.ENTER){const z=t.dataset.disabled==="true",F=t.dataset.value;if(z||!F)return;f(F);return}const s=a.target,c=ct(b.root);if(!c||!ze(s))return;const i=Array.from(c.querySelectorAll(Ye("trigger"))).filter(z=>ze(z)?z.dataset.disabled!=="true":!1);if(!i.length)return;const M=i.indexOf(s);a.key===J.ARROW_DOWN&&i[(M+1)%i.length].focus(),a.key===J.ARROW_UP&&i[(M-1+i.length)%i.length].focus(),a.key===J.HOME&&i[0].focus(),a.key===J.END&&i[i.length-1].focus()}))})}),R=be(xe("content"),{stores:[o,p,$],returned:([t,r,a])=>s=>{const{value:c}=m(s),u=d(c,t)||a;return{"data-state":u?"open":"closed","data-disabled":Se(r),"data-value":c,hidden:u?void 0:!0,style:dt({display:u?void 0:"none"})}},action:t=>{Ke().then(()=>{const r=Le(),a=Le(),s=document.querySelector(`${Ye("trigger")}, [data-value="${t.dataset.value}"]`);ze(s)&&(t.id=r,s.setAttribute("aria-controls",r),s.id=a)})}}),w=be(xe("heading"),{returned:()=>t=>{const{level:r}=y(t);return{role:"heading","aria-level":r,"data-heading-level":r}}});function f(t){o.update(r=>r===void 0?e.multiple?[t]:t:Array.isArray(r)?r.includes(t)?r.filter(a=>a!==t):(r.push(t),r):r===t?void 0:t)}return{ids:b,elements:{root:h,item:T,trigger:C,content:R,heading:w},states:{value:o},helpers:{isSelected:l},options:_}};function Me(){return{NAME:"accordion",ITEM_NAME:"accordion-item",PARTS:["root","content","header","item","trigger"]}}function Ct(g){const e=At(mt(g)),{NAME:_,PARTS:b}=Me(),p=_t(_,b),$={...e,getAttrs:p,updateOption:ht(e.options)};return Ze(_,$),$}function Oe(){const{NAME:g}=Me();return Ge(g)}function wt(g){const{ITEM_NAME:e}=Me(),_=Qe(g);return Ze(e,{propsStore:_}),{...Oe(),propsStore:_}}function Je(){const{ITEM_NAME:g}=Me();return Ge(g)}function Pt(){const g=Oe(),{propsStore:e}=Je();return{...g,propsStore:e}}function St(){const g=Oe(),{propsStore:e}=Je();return{...g,props:e}}function kt(g,e){return g.length!==e.length?!1:g.every((_,b)=>_===e[b])}var It=E("<div><!></div>");function Tt(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["multiple","value","onValueChange","disabled","asChild","el"]);ie(e,!1);const[p,$]=Ce(),A=()=>le(C,"$root",p),o=Ae();let d=x(e,"multiple",8,!1),l=x(e,"value",28,()=>{}),h=x(e,"onValueChange",24,()=>{}),m=x(e,"disabled",8,!1),y=x(e,"asChild",8,!1),T=x(e,"el",28,()=>{});const{elements:{root:C},states:{value:R},updateOption:w,getAttrs:f}=Ct({multiple:d(),disabled:m(),defaultValue:l(),onValueChange:({next:u})=>{var i,M;return Array.isArray(u)?((!Array.isArray(l())||!kt(l(),u))&&((i=h())==null||i(u),l(u)),u):(l()!==u&&((M=h())==null||M(u),l(u)),u)}}),t=f("root");D(()=>O(l()),()=>{l()!==void 0&&R.set(Array.isArray(l())?[...l()]:l())}),D(()=>O(d()),()=>{w("multiple",d())}),D(()=>O(m()),()=>{w("disabled",m())}),D(()=>A(),()=>{$e(o,A())}),D(()=>n(o),()=>{Object.assign(n(o),t)}),ye(),ce();var r=Y(),a=N(r);{var s=u=>{var i=Y(),M=N(i);V(M,e,"default",{get builder(){return n(o)}},null),v(u,i)},c=u=>{var i=It();ae(i,()=>({...n(o),...b}));var M=I(i);V(M,e,"default",{get builder(){return n(o)}},null),S(i),ne(i,z=>T(z),()=>T()),se(i,z=>{var F,k;return(k=(F=n(o)).action)==null?void 0:k.call(F,z)}),v(u,i)};W(a,u=>{y()?u(s):u(c,!1)})}v(g,r),de(),$()}var Et=E("<div><!></div>");function Mt(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["value","disabled","asChild","el"]);ie(e,!1);const[p,$]=Ce(),A=()=>le(T,"$item",p),o=()=>le(C,"$propsStore",p),d=Ae();let l=x(e,"value",8),h=x(e,"disabled",24,()=>{}),m=x(e,"asChild",8,!1),y=x(e,"el",28,()=>{});const{elements:{item:T},propsStore:C,getAttrs:R}=wt({value:l(),disabled:h()}),w=R("item");D(()=>(O(l()),O(h())),()=>{C.set({value:l(),disabled:h()})}),D(()=>(A(),o(),O(h())),()=>{$e(d,A()({...o(),disabled:h()}))}),D(()=>n(d),()=>{Object.assign(n(d),w)}),ye(),ce();var f=Y(),t=N(f);{var r=s=>{var c=Y(),u=N(c);V(u,e,"default",{get builder(){return n(d)}},null),v(s,c)},a=s=>{var c=Et();ae(c,()=>({...n(d),...b}));var u=I(c);V(u,e,"default",{get builder(){return n(d)}},null),S(c),ne(c,i=>y(i),()=>y()),se(c,i=>{var M,z;return(z=(M=n(d)).action)==null?void 0:z.call(M,i)}),v(s,c)};W(t,s=>{m()?s(r):s(a,!1)})}v(g,f),de(),$()}var Ot=E("<div><!></div>");function Nt(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["level","asChild","el"]);ie(e,!1);const[p,$]=Ce(),A=()=>le(m,"$header",p),o=Ae();let d=x(e,"level",8,3),l=x(e,"asChild",8,!1),h=x(e,"el",28,()=>{});const{elements:{heading:m},getAttrs:y}=Oe(),T=y("header");D(()=>(A(),O(d())),()=>{$e(o,A()(d()))}),D(()=>n(o),()=>{Object.assign(n(o),T)}),ye(),ce();var C=Y(),R=N(C);{var w=t=>{var r=Y(),a=N(r);V(a,e,"default",{get builder(){return n(o)}},null),v(t,r)},f=t=>{var r=Ot();ae(r,()=>({...n(o),...b}));var a=I(r);V(a,e,"default",{get builder(){return n(o)}},null),S(r),ne(r,s=>h(s),()=>h()),se(r,s=>{var c,u;return(u=(c=n(o)).action)==null?void 0:u.call(c,s)}),v(t,r)};W(R,t=>{l()?t(w):t(f,!1)})}v(g,C),de(),$()}var Rt=E("<button><!></button>");function Vt(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["asChild","el"]);ie(e,!1);const[p,$]=Ce(),A=()=>le(m,"$trigger",p),o=()=>le(y,"$props",p),d=Ae();let l=x(e,"asChild",8,!1),h=x(e,"el",28,()=>{});const{elements:{trigger:m},props:y,getAttrs:T}=St(),C=bt(),R=T("trigger");D(()=>(A(),o()),()=>{$e(d,A()({...o()}))}),D(()=>n(d),()=>{Object.assign(n(d),R)}),ye(),ce();var w=Y(),f=N(w);{var t=a=>{var s=Y(),c=N(s);V(c,e,"default",{get builder(){return n(d)}},null),v(a,s)},r=a=>{var s=Rt();ae(s,()=>({...n(d),type:"button",...b}));var c=I(s);V(c,e,"default",{get builder(){return n(d)}},null),S(s),ne(s,u=>h(u),()=>h()),se(s,u=>{var i,M;return(M=(i=n(d)).action)==null?void 0:M.call(i,u)}),He(()=>Ue("m-keydown",s,C)),He(()=>Ue("m-click",s,C)),v(a,s)};W(f,a=>{l()?a(t):a(r,!1)})}v(g,w),de(),$()}var jt=E("<div><!></div>"),zt=E("<div><!></div>"),Wt=E("<div><!></div>"),Ft=E("<div><!></div>"),qt=E("<div><!></div>");function Dt(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);ie(e,!1);const[p,$]=Ce(),A=()=>le(t,"$content",p),o=()=>le(a,"$propsStore",p),d=()=>le(r,"$isSelected",p),l=Ae();let h=x(e,"transition",24,()=>{}),m=x(e,"transitionConfig",24,()=>{}),y=x(e,"inTransition",24,()=>{}),T=x(e,"inTransitionConfig",24,()=>{}),C=x(e,"outTransition",24,()=>{}),R=x(e,"outTransitionConfig",24,()=>{}),w=x(e,"asChild",8,!1),f=x(e,"el",28,()=>{});const{elements:{content:t},helpers:{isSelected:r},propsStore:a,getAttrs:s}=Pt(),c=s("content");D(()=>(A(),o()),()=>{$e(l,A()({...o()}))}),D(()=>n(l),()=>{Object.assign(n(l),c)}),ye(),ce();var u=Y(),i=N(u);{var M=F=>{var k=Y(),H=N(k);V(H,e,"default",{get builder(){return n(l)}},null),v(F,k)},z=(F,k)=>{{var H=G=>{var P=jt();ae(P,()=>({...n(l),...b}));var Z=I(P);V(Z,e,"default",{get builder(){return n(l)}},null),S(P),ne(P,ge=>f(ge),()=>f()),se(P,ge=>{var K,U;return(U=(K=n(l)).action)==null?void 0:U.call(K,ge)}),pe(3,P,h,m),v(G,P)},fe=(G,P)=>{{var Z=K=>{var U=zt();ae(U,()=>({...n(l),...b}));var Ne=I(U);V(Ne,e,"default",{get builder(){return n(l)}},null),S(U),ne(U,me=>f(me),()=>f()),se(U,me=>{var X,B;return(B=(X=n(l)).action)==null?void 0:B.call(X,me)}),pe(1,U,y,T),pe(2,U,C,R),v(K,U)},ge=(K,U)=>{{var Ne=X=>{var B=Wt();ae(B,()=>({...n(l),...b}));var Re=I(B);V(Re,e,"default",{get builder(){return n(l)}},null),S(B),ne(B,he=>f(he),()=>f()),se(B,he=>{var ee,L;return(L=(ee=n(l)).action)==null?void 0:L.call(ee,he)}),pe(1,B,y,T),v(X,B)},me=(X,B)=>{{var Re=ee=>{var L=Ft();ae(L,()=>({...n(l),...b}));var Ve=I(L);V(Ve,e,"default",{get builder(){return n(l)}},null),S(L),ne(L,ue=>f(ue),()=>f()),se(L,ue=>{var te,we;return(we=(te=n(l)).action)==null?void 0:we.call(te,ue)}),pe(2,L,C,R),v(ee,L)},he=(ee,L)=>{{var Ve=ue=>{var te=qt();ae(te,()=>({...n(l),...b}));var we=I(te);V(we,e,"default",{get builder(){return n(l)}},null),S(te),ne(te,je=>f(je),()=>f()),se(te,je=>{var qe,De;return(De=(qe=n(l)).action)==null?void 0:De.call(qe,je)}),v(ue,te)};W(ee,ue=>{d(),o(),re(()=>d()(o().value))&&ue(Ve)},L)}};W(X,ee=>{O(C()),d(),o(),re(()=>C()&&d()(o().value))?ee(Re):ee(he,!1)},B)}};W(K,X=>{O(y()),d(),o(),re(()=>y()&&d()(o().value))?X(Ne):X(me,!1)},U)}};W(G,K=>{O(y()),O(C()),d(),o(),re(()=>y()&&C()&&d()(o().value))?K(Z):K(ge,!1)},P)}};W(F,G=>{O(h()),d(),o(),re(()=>h()&&d()(o().value))?G(H):G(fe,!1)},k)}};W(i,F=>{O(w()),d(),o(),re(()=>w()&&d()(o().value))?F(M):F(z,!1)})}v(g,u),de(),$()}const Ht=!0,Tr=Object.freeze(Object.defineProperty({__proto__:null,prerender:Ht},Symbol.toStringTag,{value:"Module"}));var Ut=E('<div class="pb-4 pt-0"><!></div>');function ke(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["class","transition","transitionConfig"]);ie(e,!1);let p=x(e,"class",8,void 0),$=x(e,"transition",8,yt),A=x(e,"transitionConfig",24,()=>({duration:200}));ce();const o=We(()=>(O(ve),O(p()),re(()=>ve("overflow-hidden text-sm transition-all",p()))));Dt(g,Fe({get class(){return n(o)},get transition(){return $()},get transitionConfig(){return A()}},()=>b,{children:(d,l)=>{var h=Ut(),m=I(h);V(m,e,"default",{},null),S(h),v(d,h)},$$slots:{default:!0}})),de()}function Ie(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["class","value"]);ie(e,!1);let p=x(e,"class",8,void 0),$=x(e,"value",8);ce();const A=We(()=>(O(ve),O(p()),re(()=>ve("border-b",p()))));Mt(g,Fe({get value(){return $()},get class(){return n(A)}},()=>b,{children:(o,d)=>{var l=Y(),h=N(l);V(h,e,"default",{},null),v(o,l)},$$slots:{default:!0}})),de()}var Bt=E("<!> <!>",1);function Te(g,e){const _=j(e,["children","$$slots","$$events","$$legacy"]),b=j(_,["class","level"]);ie(e,!1);let p=x(e,"class",8,void 0),$=x(e,"level",8,3);ce(),Nt(g,{get level(){return $()},class:"flex",children:(A,o)=>{const d=We(()=>(O(ve),O(p()),re(()=>ve("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",p()))));Vt(A,Fe({get class(){return n(d)}},()=>b,{$$events:{click(l){pt.call(this,e,l)}},children:(l,h)=>{var m=Bt(),y=N(m);V(y,e,"default",{},null);var T=q(y,2);xt(T,{class:"h-4 w-4 transition-transform duration-200"}),v(l,m)},$$slots:{default:!0}}))},$$slots:{default:!0}}),de()}const Lt=Tt;var Yt=E('<meta name="description"/>'),Zt=E("<!> <!>",1),Gt=E("<!> <!>",1),Qt=E("<!> <!>",1),Jt=E("<!> <!>",1),Kt=E("<!> <!> <!> <!>",1),Xt=E('<tr class="bg-foreground text-background font-bold p-2"><td colspan="3"> </td></tr>'),er=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),tr=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),rr=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 ml-2 inline text-success"><use href="#checkcircle"></use></svg>'),ar=Ee('<svg xmlns="http://www.w3.org/2000/svg" class="w-[26px] h-[26px] inline text-base-200"><use href="#nocircle"></use></svg>'),sr=E('<tr class="relative"><td> </td><td class="text-center"><!></td><td class="text-center"><!></td></tr>'),nr=E(`<div class="min-h-[70vh] pb-12 pt-16 px-6 bg-white"><div class="max-w-6xl mx-auto text-center"><h1 class="text-4xl md:text-5xl font-light tracking-tight text-gray-900 font-jakarta">Simple, transparent pricing</h1> <p class="text-lg text-gray-600 mt-4 font-light max-w-2xl mx-auto">Choose the plan that's right for your team. Start free and scale as you grow.</p></div> <div class="w-full my-8"><!> <h1 class="text-2xl font-bold text-center mt-24">Pricing FAQ</h1> <div class="flex place-content-center"><!></div> <svg style="display:none" version="2.0"><defs><symbol id="checkcircle" viewBox="0 0 24 24" stroke-width="2" fill="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M16.417 10.283A7.917 7.917 0 1 1 8.5 2.366a7.916 7.916 0 0 1 7.917 7.917zm-4.105-4.498a.791.791 0 0 0-1.082.29l-3.828 6.63-1.733-2.08a.791.791 0 1 0-1.216 1.014l2.459 2.952a.792.792 0 0 0 .608.285.83.83 0 0 0 .068-.003.791.791 0 0 0 .618-.393L12.6 6.866a.791.791 0 0 0-.29-1.081z"></path></symbol></defs></svg> <svg style="display:none" version="2.0"><defs><symbol id="nocircle" viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm4,11H8a1,1,0,0,1,0-2h8a1,1,0,0,1,0,2Z"></path></symbol></defs></svg> <h1 class="text-2xl font-bold text-center mt-16">Plan Features</h1> <h2 class="text-xl text-center mt-1 pb-3">Example feature table</h2> <div class="overflow-visible mx-auto max-w-xl mt-4"><table class="table w-full"><thead class="text-lg sticky top-0 bg-foreground text-background bg-opacity-50 z-10 backdrop-blur"><tr><th></th><th class="text-center">Free</th><th class="text-center">Pro</th></tr></thead><tbody></tbody></table></div></div></div>`);function Er(g){const e=[{name:"Section 1",header:!0},{name:"Feature 1",freeIncluded:!0,proIncluded:!0},{name:"Feature 2",freeIncluded:!1,proIncluded:!0},{name:"Feature 3",freeString:"3",proString:"Unlimited"},{name:"Section 2",header:!0},{name:"Feature 4",freeIncluded:!0,proIncluded:!0},{name:"Feature 5",freeIncluded:!1,proIncluded:!0}];var _=nr();et(h=>{var m=Yt();Xe.title="Pricing",_e(()=>at(m,"content",`Pricing - ${nt}`)),v(h,m)});var b=q(I(_),2),p=I(b);st(p,{callToAction:"Get Started",highlightedPlanId:"pro"});var $=q(p,4),A=I($);Lt(A,{class:"max-w-xl mx-auto",children:(h,m)=>{var y=Kt(),T=N(y);Ie(T,{value:"faq1",children:(f,t)=>{var r=Zt(),a=N(r);Te(a,{children:(c,u)=>{oe();var i=Q("Is this template free to use?");v(c,i)},$$slots:{default:!0}});var s=q(a,2);ke(s,{children:(c,u)=>{oe();var i=Q("Yup! This template is free to use for any project.");v(c,i)},$$slots:{default:!0}}),v(f,r)},$$slots:{default:!0}});var C=q(T,2);Ie(C,{value:"faq2",children:(f,t)=>{var r=Gt(),a=N(r);Te(a,{children:(c,u)=>{oe();var i=Q("Why does a free template have a pricing page?");v(c,i)},$$slots:{default:!0}});var s=q(a,2);ke(s,{children:(c,u)=>{oe();var i=Q(`The pricing page is part of the boilerplate. It shows how the
            pricing page integrates into the billing portal and the Stripe
            Checkout flows.`);v(c,i)},$$slots:{default:!0}}),v(f,r)},$$slots:{default:!0}});var R=q(C,2);Ie(R,{value:"faq3",children:(f,t)=>{var r=Qt(),a=N(r);Te(a,{children:(c,u)=>{oe();var i=Q("What license is the template under?");v(c,i)},$$slots:{default:!0}});var s=q(a,2);ke(s,{children:(c,u)=>{oe();var i=Q("The template is under the MIT license.");v(c,i)},$$slots:{default:!0}}),v(f,r)},$$slots:{default:!0}});var w=q(R,2);Ie(w,{value:"Is this template free to use?",children:(f,t)=>{var r=Jt(),a=N(r);Te(a,{children:(c,u)=>{oe();var i=Q("Can I try out purchase flows without real a credit card?");v(c,i)},$$slots:{default:!0}});var s=q(a,2);ke(s,{children:(c,u)=>{oe();var i=Q(`You can use the credit card number 4242 4242 4242 4242 with any
            future expiry date to test the payment and upgrade flows.`);v(c,i)},$$slots:{default:!0}}),v(f,r)},$$slots:{default:!0}}),v(h,y)},$$slots:{default:!0}}),S($);var o=q($,10),d=I(o),l=q(I(d));tt(l,5,()=>e,rt,(h,m)=>{var y=Y(),T=N(y);{var C=w=>{var f=Xt(),t=I(f),r=I(t,!0);S(t),S(f),_e(()=>Pe(r,n(m).name)),v(w,f)},R=w=>{var f=sr(),t=I(f),r=I(t,!0);S(t);var a=q(t),s=I(a);{var c=k=>{var H=Q();_e(()=>Pe(H,n(m).freeString)),v(k,H)},u=(k,H)=>{{var fe=P=>{var Z=er();v(P,Z)},G=P=>{var Z=tr();v(P,Z)};W(k,P=>{n(m).freeIncluded?P(fe):P(G,!1)},H)}};W(s,k=>{n(m).freeString?k(c):k(u,!1)})}S(a);var i=q(a),M=I(i);{var z=k=>{var H=Q();_e(()=>Pe(H,n(m).proString)),v(k,H)},F=(k,H)=>{{var fe=P=>{var Z=rr();v(P,Z)},G=P=>{var Z=ar();v(P,Z)};W(k,P=>{n(m).proIncluded?P(fe):P(G,!1)},H)}};W(M,k=>{n(m).proString?k(z):k(F,!1)})}S(i),S(f),_e(()=>Pe(r,n(m).name)),v(w,f)};W(T,w=>{n(m).header?w(C):w(R,!1)})}v(h,y)}),S(l),S(d),S(o),S(b),S(_),v(g,_)}export{Er as component,Tr as universal};
