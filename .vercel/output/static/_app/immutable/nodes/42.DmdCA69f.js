import"../chunks/CWj6FrbW.js";import{p as S,b as W,e as c,a as e,g as j,$ as q,f as i,n,v as w,s as v,c as p,r as _,t as z}from"../chunks/BWPOaEfG.js";import{h as F,s as H}from"../chunks/CZV_L0uA.js";import{i as J}from"../chunks/CpAxVSI9.js";import{c as f}from"../chunks/ygpEKcEO.js";import{a as K,C as L}from"../chunks/Danr6qfo.js";import{C as M}from"../chunks/CurvpjPW.js";import"../chunks/BFr-ef-6.js";import{C as N,a as O}from"../chunks/C4xZpxGw.js";import"../chunks/BRvWcDwV.js";import{B as Q}from"../chunks/s_I_M9BL.js";var R=i("We've sent a confirmation link to <strong> </strong>",1),T=i("<!> <!>",1),U=i(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          new email address.</p> <p class="text-sm text-muted-foreground">You may need to confirm the change on both your old and new email
          addresses.</p>`,1),V=i(`<p class="text-sm text-muted-foreground">Please check your email and click the confirmation link to verify your
          account.</p> <p class="text-sm text-muted-foreground">After confirming your email, you'll be able to sign in with your
          credentials.</p>`,1),X=i(`<div class="space-y-2"><!></div> <div class="pt-4"><a href="/login/sign_in" class="w-full"><!></a></div> <div class="text-xs text-muted-foreground"><p>Didn't receive the email? Check your spam folder or</p> <a href="/login/sign_up" class="underline hover:text-primary">try signing up again</a></div>`,1),Z=i("<!> <!>",1);function ft(Y,g){S(g,!0);var k=W();F(C=>{q.title="Check Your Email"});var B=c(k);f(B,()=>L,(C,E)=>{E(C,{class:"mt-6",children:(A,tt)=>{var P=Z(),b=c(P);f(b,()=>N,(h,x)=>{x(h,{children:($,G)=>{var l=T(),a=c(l);f(a,()=>O,(d,m)=>{m(d,{class:"text-2xl font-bold text-center",children:(r,u)=>{n();var o=w("Check Your Email");e(r,o)},$$slots:{default:!0}})});var y=v(a,2);f(y,()=>M,(d,m)=>{m(d,{class:"text-center",children:(r,u)=>{n();var o=R(),t=v(c(o)),s=p(t,!0);_(t),z(()=>H(s,g.data.email)),e(r,o)},$$slots:{default:!0}})}),e($,l)},$$slots:{default:!0}})});var D=v(b,2);f(D,()=>K,(h,x)=>{x(h,{class:"text-center space-y-4",children:($,G)=>{var l=X(),a=c(l),y=p(a);{var d=t=>{var s=U();n(2),e(t,s)},m=t=>{var s=V();n(2),e(t,s)};J(y,t=>{g.data.type==="email_change"?t(d):t(m,!1)})}_(a);var r=v(a,2),u=p(r),o=p(u);Q(o,{variant:"outline",class:"w-full",children:(t,s)=>{n();var I=w("Go to Sign In");e(t,I)},$$slots:{default:!0}}),_(u),_(r),n(2),e($,l)},$$slots:{default:!0}})}),e(A,P)},$$slots:{default:!0}})}),e(Y,k),j()}export{ft as component};
