import"../chunks/CWj6FrbW.js";import{o as p}from"../chunks/_trTl4Xy.js";import{p as m,u as f,b as c,e as d,a as s,g as h}from"../chunks/BWPOaEfG.js";import{s as b}from"../chunks/CGJ61bb8.js";import{i as l}from"../chunks/C4R3BIxb.js";function M(n,a){m(a,!0);let{supabase:r,session:t}=a.data;f(()=>{({supabase:r,session:t}=a.data)}),p(()=>{const{data:i}=r.auth.onAuthStateChange((v,e)=>{(e==null?void 0:e.expires_at)!==(t==null?void 0:t.expires_at)&&l("supabase:auth")});return()=>i.subscription.unsubscribe()});var u=c(),o=d(u);b(o,()=>a.children),s(n,u),h()}export{M as component};
