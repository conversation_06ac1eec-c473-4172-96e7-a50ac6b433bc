import"../chunks/CWj6FrbW.js";import{o as ur}from"../chunks/CfBaWyh2.js";import{p as cr,u as $r,f as i,a as r,g as gr,k as hr,J as xr,c as k,j as G,r as F,b as D,e as d,n as S,v as w,s as f,q as br,i as H,o as Cr,t as yr}from"../chunks/wnqW1tdD.js";import{s as Pr}from"../chunks/CDPCzm7q.js";import{i as K}from"../chunks/BjRbZGyQ.js";import{c as o}from"../chunks/ojdN50pv.js";import{a as kr}from"../chunks/Cet13GU7.js";import{s as Fr}from"../chunks/Cmdkv-7M.js";import{a as Dr,b as Sr,s as B}from"../chunks/D5ITLM2v.js";import{s as wr,d as Br,b as Ir,C as Lr,F as jr,a as qr}from"../chunks/Bm2IJZ-X.js";import"../chunks/BQZzwlPZ.js";import{a as zr}from"../chunks/B_9MMhPz.js";import{I as Er}from"../chunks/aFtO4Q5C.js";import"../chunks/DpzY6icx.js";import{a as Jr,C as Mr}from"../chunks/Dk7oig4_.js";import"../chunks/Cvx8ZW61.js";import{C as Nr,a as Wr}from"../chunks/DMt7uXAk.js";import{g as O}from"../chunks/qz3za0Vm.js";import{g as Ar}from"../chunks/DyGaIYLH.js";import{B as Gr}from"../chunks/BFIy0mTe.js";import{L as Hr}from"../chunks/CDkJ1nAx.js";var Kr=i("<!> <!> <!>",1),Or=i('<p class="text-destructive text-sm font-bold mt-1"> </p>'),Qr=i('<form method="post"><!> <!> <!></form>'),Rr=i("<!> <!>",1),Tr=i('<div class="max-w-xl mx-auto mt-8"><!></div>');function ht(Q,m){cr(m,!0);const[_,R]=Dr(),u=()=>B(q,"$formData",_),T=()=>B(V,"$delayed",_),I=()=>B(U,"$errors",_);let c=Ar(),L=xr(!0);const j=wr(m.data.form,{validators:Br(zr)});ur(()=>{c.value?O(`/dashboard/${c.value.slug}`):hr(L,!1)}),$r(()=>{var a;(a=m.form)!=null&&a.env&&(c.value=m.form.env,O(`/dashboard/${m.form.env.slug}`))});const{form:q,enhance:$,errors:U,delayed:V}=j;var g=Tr(),X=k(g);{var Y=a=>{var z=D(),rr=d(z);o(rr,()=>Mr,(tr,ar)=>{ar(tr,{children:(er,Ur)=>{var E=Rr(),J=d(E);o(J,()=>Nr,(h,x)=>{x(h,{children:(b,sr)=>{var s=D(),v=d(s);o(v,()=>Wr,(p,C)=>{C(p,{children:(y,t)=>{S();var e=w("Create your company workspace");r(y,e)},$$slots:{default:!0}})}),r(b,s)},$$slots:{default:!0}})});var or=f(J,2);o(or,()=>Jr,(h,x)=>{x(h,{children:(b,sr)=>{var s=Qr(),v=k(s);o(v,()=>Ir,(t,e)=>{e(t,{get form(){return j},name:"name",class:"mb-2",children:(n,Vr)=>{var M=D(),mr=d(M);o(mr,()=>Lr,(nr,lr)=>{lr(nr,{children:br,$$slots:{default:(dr,fr)=>{const ir=Cr(()=>fr.attrs);var N=Kr(),W=d(N);o(W,()=>jr,(l,P)=>{P(l,{children:(pr,Xr)=>{S();var _r=w("Company Workspace Name");r(pr,_r)},$$slots:{default:!0}})});var A=f(W,2);Er(A,Fr(()=>G(ir),{get value(){return u().name},set value(l){Sr(q,H(u).name=l,H(u))}}));var vr=f(A,2);o(vr,()=>qr,(l,P)=>{P(l,{})}),r(dr,N)}}})}),r(n,M)},$$slots:{default:!0}})});var p=f(v,2);Gr(p,{get disabled(){return T()},type:"submit",children:(t,e)=>{S();var n=w("Create");r(t,n)},$$slots:{default:!0}});var C=f(p,2);{var y=t=>{var e=Or(),n=k(e,!0);F(e),yr(()=>Pr(n,I()._errors[0])),r(t,e)};K(C,t=>{I()._errors&&t(y)})}F(s),kr(s,t=>$==null?void 0:$(t)),r(b,s)},$$slots:{default:!0}})}),r(er,E)},$$slots:{default:!0}})}),r(a,z)},Z=a=>{Hr(a,{class:"animate-spin"})};K(X,a=>{G(L)?a(Z,!1):a(Y)})}F(g),r(Q,g),gr(),R()}export{ht as component};
