import"../chunks/CWj6FrbW.js";import{p as n,f as m,s as o,e as d,a as h,g as c,$ as u,j as g,o as f}from"../chunks/wnqW1tdD.js";import{h as p}from"../chunks/CDPCzm7q.js";import{e as E}from"../chunks/B_9MMhPz.js";import{S as b}from"../chunks/udb0C_eZ.js";var v=m('<h1 class="text-2xl font-bold mb-6">Settings</h1> <!>',1);function w(r,e){n(e,!0);let{session:a}=e.data;var i=v();p(t=>{u.title="Change Email"});var l=o(d(i),2);const s=f(()=>{var t;return[{id:"email",label:"Email",initialValue:((t=a==null?void 0:a.user)==null?void 0:t.email)??"",placeholder:"Email address"}]});b(l,{get data(){return e.data.form},get schema(){return E},title:"Change Email",editable:!0,successTitle:"Email change initiated",successBody:"You should receive an email at the new address to confirm the change. Please click the link in the email to finalized the change. Until finalized, you must sign in with your current email.",formTarget:"/api?/updateEmail",get fields(){return g(s)}}),h(r,i),c()}export{w as component};
