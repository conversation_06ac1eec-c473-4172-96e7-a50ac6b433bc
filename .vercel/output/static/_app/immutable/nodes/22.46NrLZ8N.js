import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as Mt}from"../chunks/CfBaWyh2.js";import{b as fe,e as ie,a as p,p as St,l as Ot,h as It,f as P,t as G,j as e,g as Dt,$ as Lt,c as r,s,d as rt,i as l,k as b,m as D,r as t,n as V,aU as At}from"../chunks/wnqW1tdD.js";import{h as Tt,e as S,r as Bt,s as K}from"../chunks/CDPCzm7q.js";import{i as z}from"../chunks/BjRbZGyQ.js";import{e as Be,i as at}from"../chunks/CsnEE4l9.js";import{h as jt}from"../chunks/D8kkBG2G.js";import{c as zt}from"../chunks/ojdN50pv.js";import{s as st,c as oe,r as Et,b as Nt}from"../chunks/rh_XW2Tv.js";import{s as H}from"../chunks/Bz0_kaay.js";import{t as ot}from"../chunks/sBNKiCwy.js";import{b as it}from"../chunks/CJ-FD9ng.js";import{i as Rt}from"../chunks/BxG_UISn.js";import{a as Ut,s as nt}from"../chunks/D5ITLM2v.js";import{w as Ft}from"../chunks/BvpDAKCq.js";import{p as qt}from"../chunks/D0xeg8nZ.js";import{s as Gt,f as Kt}from"../chunks/CiB29Aqe.js";import{s as me}from"../chunks/BDqVm3Gq.js";import{l as ge,s as xe}from"../chunks/Cmdkv-7M.js";import{I as he}from"../chunks/CX_t0Ed_.js";import{C as Ht}from"../chunks/ZAWXEYb0.js";import{F as Yt,C as Zt}from"../chunks/BzUNhcOB.js";import{Z as Vt}from"../chunks/aKJhBsL8.js";import{U as Wt}from"../chunks/c5ELjire.js";import{C as Jt,U as Qt,B as lt}from"../chunks/TU_NZaLw.js";import{D as Xt,C as er,a as tr}from"../chunks/D3xCqHYb.js";import{L as rr}from"../chunks/CDkJ1nAx.js";function dt(E,y){const L=ge(y,["children","$$slots","$$events","$$legacy"]),N=[["path",{d:"M8 2v4"}],["path",{d:"M16 2v4"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2"}],["path",{d:"M3 10h18"}]];he(E,xe({name:"calendar"},()=>L,{get iconNode(){return N},children:(A,W)=>{var g=fe(),f=ie(g);me(f,y,"default",{},null),p(A,g)},$$slots:{default:!0}}))}function ar(E,y){const L=ge(y,["children","$$slots","$$events","$$legacy"]),N=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"}]];he(E,xe({name:"mail"},()=>L,{get iconNode(){return N},children:(A,W)=>{var g=fe(),f=ie(g);me(f,y,"default",{},null),p(A,g)},$$slots:{default:!0}}))}function je(E,y){const L=ge(y,["children","$$slots","$$events","$$legacy"]),N=[["path",{d:"m3 11 18-5v12L3 14v-3z"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6"}]];he(E,xe({name:"megaphone"},()=>L,{get iconNode(){return N},children:(A,W)=>{var g=fe(),f=ie(g);me(f,y,"default",{},null),p(A,g)},$$slots:{default:!0}}))}function sr(E,y){const L=ge(y,["children","$$slots","$$events","$$legacy"]),N=[["circle",{cx:"18",cy:"5",r:"3"}],["circle",{cx:"6",cy:"12",r:"3"}],["circle",{cx:"18",cy:"19",r:"3"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49"}]];he(E,xe({name:"share-2"},()=>L,{get iconNode(){return N},children:(A,W)=>{var g=fe(),f=ie(g);me(f,y,"default",{},null),p(A,g)},$$slots:{default:!0}}))}var or=P('<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group svelte-1bbifsw" style="background: var(--card); border-color: var(--border);"><div class="flex items-center gap-3 mb-3 svelte-1bbifsw"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform svelte-1bbifsw" style="background: var(--primary); border-color: var(--border);"><!></div> <h4 class="font-bold text-sm svelte-1bbifsw" style="color: var(--foreground);"> </h4></div> <p class="text-xs mb-3 svelte-1bbifsw" style="color: var(--muted-foreground);"> </p> <div class="text-xs font-mono p-2 border-2 rounded svelte-1bbifsw" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"> </div></button>'),ir=P('<div class="text-center py-12 svelte-1bbifsw"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2 svelte-1bbifsw" style="background: var(--muted); border-color: var(--border);"><!></div> <h3 class="text-xl font-bold mb-2 svelte-1bbifsw" style="color: var(--foreground);">Start Planning Your Campaign</h3> <p class="font-medium mb-6 svelte-1bbifsw" style="color: var(--muted-foreground);">Create comprehensive multi-channel marketing campaigns with AI</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto svelte-1bbifsw"></div></div>'),nr=P('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1 svelte-1bbifsw" title="Download as Markdown"><!> Download</button>'),lr=P('<div class="p-4 border-2 svelte-1bbifsw" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium svelte-1bbifsw" style="color: var(--primary-foreground);"> </p></div>'),dr=P('<div class="p-6 border-2 mb-4 svelte-1bbifsw" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none svelte-1bbifsw"><!></div></div> <div class="flex flex-wrap gap-2 mb-4 svelte-1bbifsw"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw"><!> Email Brief</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1 svelte-1bbifsw"><!> Export Timeline</button></div>',1),vr=P('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-1bbifsw"><!></div> <div class="flex-1 max-w-3xl svelte-1bbifsw"><div class="flex items-center gap-2 mb-2 svelte-1bbifsw"><span class="text-sm font-bold svelte-1bbifsw" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1 svelte-1bbifsw"><!> <span class="text-xs svelte-1bbifsw" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),cr=P('<div class="svelte-1bbifsw"><!></div>'),br=P('<div class="mt-2 h-1 rounded-full overflow-hidden svelte-1bbifsw" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out svelte-1bbifsw"></div></div>'),ur=P('<div class="flex items-start gap-3 svelte-1bbifsw"><div class="flex-shrink-0 mt-0.5 svelte-1bbifsw"><!></div> <div class="flex-1 svelte-1bbifsw"><h4 class="text-sm font-bold mb-1 svelte-1bbifsw"> </h4> <p class="text-xs svelte-1bbifsw"> </p> <!></div></div>'),pr=P('<div class="flex gap-4 svelte-1bbifsw"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2 svelte-1bbifsw" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1 svelte-1bbifsw"><div class="flex items-center gap-2 mb-2 svelte-1bbifsw"><span class="text-sm font-bold svelte-1bbifsw" style="color: var(--foreground);">Campaign Orchestrator</span> <span class="text-xs svelte-1bbifsw" style="color: var(--muted-foreground);">Planning your campaign...</span></div> <div class="p-6 border-2 svelte-1bbifsw" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="space-y-4 svelte-1bbifsw"></div> <div class="mt-6 pt-4 border-t svelte-1bbifsw" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2 svelte-1bbifsw"><span class="text-xs font-medium svelte-1bbifsw" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold svelte-1bbifsw" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden svelte-1bbifsw" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out svelte-1bbifsw"></div></div></div></div></div></div>'),fr=P('<div class="grid md:grid-cols-3 gap-4 p-4 border-2 svelte-1bbifsw" style="background: var(--muted); border-color: var(--border);"><div class="svelte-1bbifsw"><label for="campaign-type" class="block text-xs font-bold mb-2 svelte-1bbifsw" style="color: var(--foreground);">Campaign Type</label> <select id="campaign-type" class="w-full p-2 border-2 text-xs svelte-1bbifsw" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option class="svelte-1bbifsw">Select type</option><option class="svelte-1bbifsw">Product Launch</option><option class="svelte-1bbifsw">Brand Awareness</option><option class="svelte-1bbifsw">Lead Generation</option><option class="svelte-1bbifsw">Customer Retention</option><option class="svelte-1bbifsw">Seasonal/Holiday</option></select></div> <div class="svelte-1bbifsw"><label for="target-channels" class="block text-xs font-bold mb-2 svelte-1bbifsw" style="color: var(--foreground);">Target Channels</label> <input id="target-channels" placeholder="e.g., Email, Social, PPC" class="w-full p-2 border-2 text-xs svelte-1bbifsw" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div class="svelte-1bbifsw"><label for="campaign-duration" class="block text-xs font-bold mb-2 svelte-1bbifsw" style="color: var(--foreground);">Campaign Duration</label> <div class="flex border-2 svelte-1bbifsw" style="border-color: var(--border); background: var(--background);"><button>2 Weeks</button> <button style="border-color: var(--border);">1 Month</button> <button>3 Months</button></div></div></div>'),mr=P('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin svelte-1bbifsw"></div>'),gr=P('<div class="h-screen flex flex-col svelte-1bbifsw" style="background: var(--background);"><div class="border-b-2 flex-shrink-0 svelte-1bbifsw" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6 svelte-1bbifsw"><div class="flex items-center justify-between svelte-1bbifsw"><div class="flex items-center space-x-4 svelte-1bbifsw"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer svelte-1bbifsw" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="svelte-1bbifsw"><h1 class="text-3xl font-black svelte-1bbifsw" style="color: var(--foreground);">Campaign Orchestrator</h1> <p class="text-lg font-medium svelte-1bbifsw" style="color: var(--muted-foreground);">AI-powered multi-channel campaign planning and execution</p></div></div> <div class="flex items-center space-x-4 svelte-1bbifsw"><div class="flex items-center space-x-2 px-4 py-2 border-2 svelte-1bbifsw" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold svelte-1bbifsw" style="color: var(--accent-foreground);">Multi-Channel</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4 svelte-1bbifsw"><nav class="flex items-center space-x-2 text-sm text-muted-foreground svelte-1bbifsw"><a class="hover:text-foreground transition-colors svelte-1bbifsw">Dashboard</a> <!> <span class="text-foreground font-medium svelte-1bbifsw">Campaign Orchestrator</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full svelte-1bbifsw"><div class="h-full svelte-1bbifsw"><div class="card-brutal p-0 chat-container h-full svelte-1bbifsw" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container svelte-1bbifsw"><div class="space-y-6 svelte-1bbifsw"><!> <!> <!></div></div> <div><div class="flex items-center justify-between svelte-1bbifsw" style="margin-bottom: 15px;"><h2 class="text-lg font-bold svelte-1bbifsw" style="color: var(--foreground);">Campaign Planning</h2> <div class="flex items-center space-x-2 svelte-1bbifsw"><span class="text-sm font-medium svelte-1bbifsw" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2 svelte-1bbifsw" style="border-color: var(--border); background: var(--background);"><button>Overview</button> <button style="border-color: var(--border);">Detailed</button> <button>Executive</button></div></div></div> <div style="margin-bottom: 15px;" class="svelte-1bbifsw"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2 svelte-1bbifsw" style="margin-bottom: 10px;"><!> Campaign Configuration <span>▼</span></button> <!></div> <div class="flex svelte-1bbifsw" style="gap: 15px;"><div class="flex-1 relative svelte-1bbifsw"><textarea class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full svelte-1bbifsw" style="min-height: 60px;"></textarea></div> <button class="btn-primary px-6 font-bold flex items-center gap-2 svelte-1bbifsw" style="height: auto; align-self: stretch;"><!> Orchestrate</button></div></div></div></div></div></div>');function Hr(E,y){St(y,!1);const[L,N]=Ut(),A=()=>nt(qt,"$page",L),W=()=>nt(g,"$messages",L),g=Ft([]);let f=D(""),d=D(!1),J=D("overview"),we=0,_e=D(""),ne=D(!1),ee=D(""),le=D(""),Y=D("1-month"),T=D([]),de=D(0);const ye=["Create a multi-channel campaign for our new product launch...","Plan a seasonal marketing campaign for Black Friday...","Design a customer retention campaign with email sequences...","Orchestrate a brand awareness campaign across social media..."],vt=[{icon:Vt,title:"Product Launch Campaign",description:"Multi-phase campaign strategy for new product introductions",prompt:"Create a comprehensive product launch campaign for [Product Name] targeting [Audience] with a [Timeline] timeline"},{icon:dt,title:"Seasonal Promotion",description:"Coordinated campaigns for holidays, events, and seasonal sales",prompt:"Plan a [Holiday/Season] marketing campaign across email, social, and paid channels with a budget of [Budget]"},{icon:Wt,title:"Customer Retention",description:"Multi-touch engagement campaigns to reduce churn and increase LTV",prompt:"Design a customer retention campaign with personalized email sequences and loyalty rewards for [Customer Segment]"}];function ze(){return Math.random().toString(36).substring(2,11)}async function Ee(){if(!e(f).trim()||e(d))return;let o=e(f).trim();const a=[];e(ee)&&a.push(`Campaign type: ${e(ee)}`),e(le)&&a.push(`Channels: ${e(le)}`),e(Y)&&a.push(`Duration: ${e(Y)}`),a.length>0&&(o=`${o}

Context: ${a.join(", ")}`),o+=`

Output format: ${e(J)}`;const n=o;b(f,""),b(d,!0),b(T,[{id:1,title:"Campaign Analysis",description:"Understanding your objectives...",status:"pending"},{id:2,title:"Audience Segmentation",description:"Identifying target segments...",status:"pending"},{id:3,title:"Channel Strategy",description:"Planning channel distribution...",status:"pending"},{id:4,title:"Content Planning",description:"Creating content strategy...",status:"pending"},{id:5,title:"Timeline & Budget",description:"Optimizing resources...",status:"pending"},{id:6,title:"Campaign Blueprint",description:"Finalizing your plan...",status:"pending"}]),b(de,0),g.update(c=>[...c,{id:ze(),role:"user",content:n,timestamp:new Date}]),setTimeout(()=>{g.update(c=>[...c,{id:ze(),role:"assistant",content:`# Campaign Orchestration Plan

I'll help you create a comprehensive campaign strategy. This feature is coming soon and will include:

## 🎯 Campaign Overview
- **Objective**: Define clear campaign goals and KPIs
- **Target Audience**: Detailed persona mapping
- **Timeline**: Phase-by-phase execution plan
- **Budget Allocation**: Channel-wise budget distribution

## 📱 Channel Strategy
### Email Marketing
- Automated sequences and personalization
- A/B testing recommendations
- Optimal send times

### Social Media
- Platform-specific content calendars
- Engagement strategies
- Influencer partnerships

### Content Marketing
- Blog post schedule
- SEO optimization
- Lead magnets

### Paid Advertising
- Channel selection (Google, Meta, LinkedIn)
- Budget optimization
- Creative recommendations

## 📊 Performance Metrics
- Real-time dashboard
- Conversion tracking
- ROI measurement

Stay tuned for the full Campaign Orchestrator experience!`,timestamp:new Date,isReport:!0}]),b(d,!1)},2e3)}function ct(o){o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),Ee())}function bt(o){return o.replace(/^### (.+)$/gm,'<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>').replace(/^## (.+)$/gm,'<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm,'<h1 class="text-2xl font-bold mb-4">$1</h1>').replace(/^\* (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/^- (.+)$/gm,'<li class="ml-4">• $1</li>').replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>").replace(/\n\n/g,'</p><p class="mb-4">').replace(/^/,'<p class="mb-4">').replace(/$/,"</p>")}function ut(o){navigator.clipboard.writeText(o)}function pt(o){const a=new Blob([o.content],{type:"text/markdown"}),n=URL.createObjectURL(a),c=document.createElement("a");c.href=n,c.download=`campaign-plan-${new Date().toISOString().split("T")[0]}.md`,c.click()}Mt(()=>{b(_e,ye[0]);const o=setInterval(()=>{we=(we+1)%ye.length,b(_e,ye[we])},3e3);return()=>clearInterval(o)});let te=D();Ot(()=>(e(d),e(T),e(te)),()=>{e(d)&&e(T).length>0?b(te,setInterval(()=>{const o=e(T).find(n=>n.status==="pending"),a=e(T).find(n=>n.status==="active");a&&a.progress!==void 0?(a.progress=Math.min(100,a.progress+20),a.progress===100&&(a.status="completed",b(de,Math.round(e(T).filter(n=>n.status==="completed").length/e(T).length*100)))):o?(o.status="active",o.progress=0):clearInterval(e(te)),b(T,[...e(T)])},300)):e(te)&&clearInterval(e(te))}),It(),Rt();var $e=gr();Tt(o=>{Lt.title="Campaign Orchestrator - AI Agent"});var ke=r($e),Ne=r(ke),Re=r(Ne),Ce=r(Re),Ue=r(Ce),ft=r(Ue);je(ft,{class:"w-6 h-6 animate-pulse",style:"color: var(--primary-foreground);"}),t(Ue),V(2),t(Ce);var Fe=s(Ce,2),qe=r(Fe),mt=r(qe);sr(mt,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),V(2),t(qe),t(Fe),t(Re),t(Ne),t(ke);var Pe=s(ke,2),Ge=r(Pe),Ke=r(Ge),gt=s(Ke,2);Ht(gt,{class:"w-4 h-4"}),V(2),t(Ge),t(Pe);var He=s(Pe,2),Ye=r(He),Ze=r(Ye),Me=r(Ze),Ve=r(Me),We=r(Ve);{var xt=o=>{var a=ir(),n=r(a),c=r(n);je(c,{class:"w-8 h-8",style:"color: var(--muted-foreground);"}),t(n);var O=s(n,6);Be(O,5,()=>vt,at,(B,u)=>{var x=or(),h=r(x),$=r(h),R=r($);zt(R,()=>e(u).icon,(F,se)=>{se(F,{class:"w-4 h-4",style:"color: var(--primary-foreground);"})}),t($);var k=s($,2),U=r(k,!0);t(k),t(h);var C=s(h,2),i=r(C,!0);t(C);var w=s(C,2),I=r(w,!0);t(w),t(x),G(()=>{x.disabled=e(d),K(U,(e(u),l(()=>e(u).title))),K(i,(e(u),l(()=>e(u).description))),K(I,(e(u),l(()=>e(u).prompt)))}),S("click",x,()=>{b(f,e(u).prompt)}),p(B,x)}),t(O),t(a),p(o,a)};z(We,o=>{W(),l(()=>W().length===0)&&o(xt)})}var Je=s(We,2);Be(Je,1,W,at,(o,a)=>{var n=vr(),c=r(n),O=r(c);{var B=v=>{Qt(v,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},u=v=>{lt(v,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};z(O,v=>{e(a),l(()=>e(a).role==="user")?v(B):v(u,!1)})}t(c);var x=s(c,2),h=r(x),$=r(h),R=r($,!0);t($);var k=s($,2),U=r(k);Jt(U,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var C=s(U,2),i=r(C,!0);t(C),t(k);var w=s(k,2);{var I=v=>{var m=nr(),j=r(m);Xt(j,{class:"w-3 h-3"}),V(),t(m),S("click",m,()=>pt(e(a))),p(v,m)};z(w,v=>{e(a),l(()=>e(a).role==="assistant"&&e(a).isReport)&&v(I)})}t(h);var F=s(h,2);{var se=v=>{var m=lr(),j=r(m),q=r(j,!0);t(j),t(m),G(()=>K(q,(e(a),l(()=>e(a).content)))),p(v,m)},Ae=v=>{var m=dr(),j=ie(m),q=r(j),Te=r(q);jt(Te,()=>(e(a),l(()=>bt(e(a).content)))),t(q),t(j);var ue=s(j,2),X=r(ue),M=r(X);Zt(M,{class:"w-3 h-3"}),V(),t(X);var _=s(X,2),Q=r(_);ar(Q,{class:"w-3 h-3"}),V(),t(_);var pe=s(_,2),Z=r(pe);dt(Z,{class:"w-3 h-3"}),V(),t(pe),t(ue),S("click",X,()=>ut(e(a).content)),p(v,m)};z(F,v=>{e(a),l(()=>e(a).role==="user")?v(se):v(Ae,!1)})}t(x),t(n),G(v=>{H(n,1,`flex gap-4 ${e(a),l(()=>e(a).role==="user"?"flex-row-reverse":"")??""}`,"svelte-1bbifsw"),oe(c,`background: var(--${e(a),l(()=>e(a).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),K(R,(e(a),l(()=>e(a).role==="user"?"You":"Campaign Orchestrator"))),K(i,v)},[()=>(e(a),l(()=>e(a).timestamp.toLocaleTimeString()))],rt),p(o,n)});var ht=s(Je,2);{var wt=o=>{var a=pr(),n=r(a),c=r(n);lt(c,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),t(n);var O=s(n,2),B=s(r(O),2),u=r(B);Be(u,5,()=>e(T),C=>C.id,(C,i)=>{var w=ur(),I=r(w),F=r(I);{var se=M=>{var _=cr(),Q=r(_);er(Q,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),t(_),ot(3,_,()=>Kt,()=>({duration:200})),p(M,_)},Ae=(M,_)=>{{var Q=Z=>{rr(Z,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},pe=Z=>{tr(Z,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};z(M,Z=>{e(i),l(()=>e(i).status==="active")?Z(Q):Z(pe,!1)},_)}};z(F,M=>{e(i),l(()=>e(i).status==="completed")?M(se):M(Ae,!1)})}t(I);var v=s(I,2),m=r(v),j=r(m,!0);t(m);var q=s(m,2),Te=r(q,!0);t(q);var ue=s(q,2);{var X=M=>{var _=br(),Q=r(_);t(_),G(()=>oe(Q,`background: var(--primary); width: ${e(i),l(()=>e(i).progress)??""}%`)),p(M,_)};z(ue,M=>{e(i),l(()=>e(i).status==="active"&&e(i).progress)&&M(X)})}t(v),t(w),G(()=>{oe(m,`color: ${e(i),l(()=>e(i).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${e(i),l(()=>e(i).status==="pending"?"opacity: 0.5":"")??""}`),K(j,(e(i),l(()=>e(i).title))),oe(q,`color: var(--muted-foreground);${e(i),l(()=>e(i).status==="pending"?"opacity: 0.5":"")??""}`),K(Te,(e(i),l(()=>e(i).description)))}),ot(3,w,()=>Gt,()=>({duration:300})),p(C,w)}),t(u);var x=s(u,2),h=r(x),$=s(r(h),2),R=r($);t($),t(h);var k=s(h,2),U=r(k);t(k),t(x),t(B),t(O),t(a),G(()=>{K(R,`${e(de)??""}%`),oe(U,`background: linear-gradient(to right, var(--primary), var(--accent)); width: ${e(de)??""}%`)}),p(o,a)};z(ht,o=>{e(d)&&o(wt)})}t(Ve),t(Me);var Se=s(Me,2),Oe=r(Se),Qe=s(r(Oe),2),Xe=s(r(Qe),2),ve=r(Xe),ce=s(ve,2),Ie=s(ce,2);t(Xe),t(Qe),t(Oe);var De=s(Oe,2),re=r(De),et=r(re);Yt(et,{class:"w-4 h-4"});var _t=s(et,2);t(re);var yt=s(re,2);{var $t=o=>{var a=fr(),n=r(a),c=s(r(n),2);G(()=>{e(ee),At(()=>{e(d)})});var O=r(c);O.value=O.__value="";var B=s(O);B.value=B.__value="Product Launch";var u=s(B);u.value=u.__value="Brand Awareness";var x=s(u);x.value=x.__value="Lead Generation";var h=s(x);h.value=h.__value="Customer Retention";var $=s(h);$.value=$.__value="Seasonal",t(c),t(n);var R=s(n,2),k=s(r(R),2);Et(k),t(R);var U=s(R,2),C=s(r(U),2),i=r(C),w=s(i,2),I=s(w,2);t(C),t(U),t(a),G(()=>{c.disabled=e(d),k.disabled=e(d),H(i,1,`px-2 py-1 text-xs font-medium transition-colors ${e(Y)==="2-weeks"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),i.disabled=e(d),H(w,1,`px-2 py-1 text-xs font-medium transition-colors border-l border-r ${e(Y)==="1-month"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),w.disabled=e(d),H(I,1,`px-2 py-1 text-xs font-medium transition-colors ${e(Y)==="3-months"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),I.disabled=e(d)}),Nt(c,()=>e(ee),F=>b(ee,F)),it(k,()=>e(le),F=>b(le,F)),S("click",i,()=>b(Y,"2-weeks")),S("click",w,()=>b(Y,"1-month")),S("click",I,()=>b(Y,"3-months")),p(o,a)};z(yt,o=>{e(ne)&&o($t)})}t(De);var tt=s(De,2),Le=r(tt),ae=r(Le);Bt(ae),t(Le);var be=s(Le,2),kt=r(be);{var Ct=o=>{var a=mr();p(o,a)},Pt=o=>{je(o,{class:"w-4 h-4 animate-pulse"})};z(kt,o=>{e(d)?o(Ct):o(Pt,!1)})}V(),t(be),t(tt),t(Se),t(Ze),t(Ye),t(He),t($e),G(o=>{st(Ke,"href",`/dashboard/${A(),l(()=>A().params.envSlug)??""}`),H(Se,1,`input-wrapper input-wrapper-seo p-6 ${e(d)?"opacity-50 pointer-events-none":""}`,"svelte-1bbifsw"),H(ve,1,`px-3 py-1 text-sm font-medium transition-colors ${e(J)==="overview"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),ve.disabled=e(d),H(ce,1,`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${e(J)==="detailed"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),ce.disabled=e(d),H(Ie,1,`px-3 py-1 text-sm font-medium transition-colors ${e(J)==="executive"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`,"svelte-1bbifsw"),Ie.disabled=e(d),re.disabled=e(d),H(_t,1,`text-xs ${e(ne)?"rotate-180":""} transition-transform`,"svelte-1bbifsw"),st(ae,"placeholder",e(_e)),ae.disabled=e(d),be.disabled=o},[()=>(e(f),e(d),l(()=>!e(f).trim()||e(d)))],rt),S("click",ve,()=>b(J,"overview")),S("click",ce,()=>b(J,"detailed")),S("click",Ie,()=>b(J,"executive")),S("click",re,()=>b(ne,!e(ne))),it(ae,()=>e(f),o=>b(f,o)),S("keydown",ae,ct),S("click",be,Ee),p(E,$e),Dt(),N()}export{Hr as component};
