import"../chunks/CWj6FrbW.js";import"../chunks/BFr-ef-6.js";import{p as M,l as d,h as z,f as B,t as D,a as j,g as A,e as E,s,c as f,d as F,i,j as t,k as u,m as g,$ as G,r as _}from"../chunks/BWPOaEfG.js";import{h as H,s as w}from"../chunks/CZV_L0uA.js";import{h as K}from"../chunks/fXKYsmio.js";import{s as Q}from"../chunks/B23TJBQP.js";import{s as n}from"../chunks/DqS74z4u.js";import{i as R}from"../chunks/CyVVhm_o.js";import{a as T,s as V}from"../chunks/BuXe-Psm.js";import{p as X}from"../chunks/BI9GnrMp.js";import{e as Y}from"../chunks/DdKGCmW7.js";import{s as Z}from"../chunks/n8BDlHG4.js";import{W as tt}from"../chunks/QNe64B_9.js";var et=B('<meta name="description"/> <meta property="og:title"/> <meta property="og:description"/> <meta property="og:site_name"/> <meta property="og:url"/>  <meta name="twitter:card" content="summary"/> <meta name="twitter:title"/> <meta name="twitter:description"/>  <!>',1),at=B('<article class="prose mx-auto py-12 px-6 font-sans"><div class="text-sm text-accent"> </div> <h1> </h1> <!></article>');function _t(O,h){M(h,!1);const[I,J]=T(),m=()=>V(X,"$page",I),e=g(),v=g(),y=g();function L(a){let r=null;for(const o of Z)if(a==o.link||a==o.link+"/"){r=o;continue}return r||Y(404,"Blog post not found"),r}function N(a){var r,o;return{"@context":"https://schema.org","@type":"BlogPosting",headline:a.title,datePublished:(r=a.parsedDate)==null?void 0:r.toISOString(),dateModified:(o=a.parsedDate)==null?void 0:o.toISOString()}}d(()=>m(),()=>{u(e,L(m().url.pathname))}),d(()=>t(e),()=>{u(v,`<script type="application/ld+json">${JSON.stringify(N(t(e)))+"<"}/script>`)}),d(()=>m(),()=>{u(y,m().url.origin+m().url.pathname)}),z(),R();var p=at();H(a=>{var r=et(),o=E(r),x=s(o,2),S=s(x,2),$=s(S,2),b=s($,2),P=s(b,4),k=s(P,2),C=s(k,2);K(C,()=>t(v)),D(()=>{G.title=(t(e),i(()=>t(e).title)),n(o,"content",(t(e),i(()=>t(e).description))),n(x,"content",(t(e),i(()=>t(e).title))),n(S,"content",(t(e),i(()=>t(e).description))),n($,"content",tt),n(b,"content",t(y)),n(P,"content",(t(e),i(()=>t(e).title))),n(k,"content",(t(e),i(()=>t(e).description)))}),j(a,r)});var c=f(p),U=f(c,!0);_(c);var l=s(c,2),W=f(l,!0);_(l);var q=s(l,2);Q(q,h,"default",{},null),_(p),D(a=>{w(U,a),w(W,(t(e),i(()=>t(e).title)))},[()=>(t(e),i(()=>{var a;return(a=t(e).parsedDate)==null?void 0:a.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}))],F),j(O,p),A(),J()}export{_t as component};
