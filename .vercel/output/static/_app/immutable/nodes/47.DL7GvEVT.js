import"../chunks/CWj6FrbW.js";import{p as hr,f as l,i as x,aM as cr,c as er,r as nr,a as r,g as Pr,b as pr,e as c,$ as kr,n as H,v as P,s,q as T,j as V,o as W,t as zr}from"../chunks/BWPOaEfG.js";import{h as Dr,s as Ur}from"../chunks/CZV_L0uA.js";import{i as $r}from"../chunks/CpAxVSI9.js";import{c as t}from"../chunks/ygpEKcEO.js";import{a as Br}from"../chunks/BkFJa9ZQ.js";import{l as ur,p as Ir,s as X}from"../chunks/C3C4ffT7.js";import{a as jr,b as Z,s as K}from"../chunks/BuXe-Psm.js";import{s as qr,z as Er,b as rr,C as tr,F as ar,a as or}from"../chunks/CaJ707dK.js";import{a as Hr,C as Mr}from"../chunks/Danr6qfo.js";import{C as Nr}from"../chunks/CurvpjPW.js";import"../chunks/BFr-ef-6.js";import{s as Yr}from"../chunks/B23TJBQP.js";import{a as Ar}from"../chunks/DqS74z4u.js";import{i as Gr}from"../chunks/CyVVhm_o.js";import{c as gr}from"../chunks/DxWxHZQT.js";import{C as Jr,a as Kr}from"../chunks/C4xZpxGw.js";import"../chunks/C4R3BIxb.js";import"../chunks/DdKGCmW7.js";import{s as Lr}from"../chunks/DVZFZ0cO.js";import{I as sr}from"../chunks/D9RgILeF.js";import"../chunks/BRvWcDwV.js";import{B as Or}from"../chunks/s_I_M9BL.js";var Qr=l("<div><!></div>");function Rr(L,h){const C=ur(h,["children","$$slots","$$events","$$legacy"]),O=ur(C,["class"]);hr(h,!1);let e=Ir(h,"class",8,void 0);Gr();var $=Qr();Ar($,N=>({class:N,...O}),[()=>(cr(gr),cr(e()),x(()=>gr("flex items-center p-6 pt-0",e())))]);var M=er($);Yr(M,h,"default",{},null),nr($),r(L,$),Pr()}var Tr=l("<!> <!>",1),Vr=l("<!> <!>",1),Wr=l("<!> <!>",1),Xr=l("<!> <!>",1),Zr=l("<!> <!>",1),rt=l("<!> <!>",1),tt=l("<!> <!>",1),at=l('<p class="text-destructive text-sm font-bold mt-1"> </p>'),ot=l('<form method="post" class="grid gap-4"><!> <!> <!> <!> <!></form>'),st=l('<div class="mt-4 mb-2">Have an account? <a class="underline" href="/login/sign_in">Sign in</a>.</div>'),et=l("<!> <!> <!>",1);function Dt(L,h){hr(h,!0);const[C,O]=jr(),e=()=>K(A,"$formData",C),$=()=>K(wr,"$constraints",C),M=()=>K(Cr,"$errors",C),N=()=>K(xr,"$delayed",C),Y=qr(h.data.form,{validators:Er(Lr)}),{form:A,enhance:Q,delayed:xr,errors:Cr,constraints:wr}=Y;var lr=pr();Dr(dr=>{kr.title="Sign up"});var br=c(lr);t(br,()=>Mr,(dr,yr)=>{yr(dr,{class:"mt-6",children:(Fr,nt)=>{var ir=et(),mr=c(ir);t(mr,()=>Jr,(y,F)=>{F(y,{children:(S,vr)=>{var p=Tr(),k=c(p);t(k,()=>Kr,(w,b)=>{b(w,{class:"text-2xl font-bold text-center",children:(z,R)=>{H();var a=P("Sign Up");r(z,a)},$$slots:{default:!0}})});var G=s(k,2);t(G,()=>Nr,(w,b)=>{b(w,{children:(z,R)=>{H();var a=P(`Create your account to get started. You'll receive a confirmation link
      via email.`);r(z,a)},$$slots:{default:!0}})}),r(S,p)},$$slots:{default:!0}})});var fr=s(mr,2);t(fr,()=>Hr,(y,F)=>{F(y,{children:(S,vr)=>{var p=ot(),k=er(p);t(k,()=>rr,(a,m)=>{m(a,{get form(){return Y},name:"email",children:(f,J)=>{var v=Wr(),_=c(v);t(_,()=>tr,(o,d)=>{d(o,{children:T,$$slots:{default:(D,U)=>{const B=W(()=>U.attrs);var u=Vr(),g=c(u);t(g,()=>ar,(i,j)=>{j(i,{children:(q,_r)=>{H();var E=P("Email");r(q,E)},$$slots:{default:!0}})});var I=s(g,2);sr(I,X(()=>V(B),()=>$().email,{get value(){return e().email},set value(i){Z(A,x(e).email=i,x(e))}})),r(D,u)}}})});var n=s(_,2);t(n,()=>or,(o,d)=>{d(o,{})}),r(f,v)},$$slots:{default:!0}})});var G=s(k,2);t(G,()=>rr,(a,m)=>{m(a,{get form(){return Y},name:"password",children:(f,J)=>{var v=Zr(),_=c(v);t(_,()=>tr,(o,d)=>{d(o,{children:T,$$slots:{default:(D,U)=>{const B=W(()=>U.attrs);var u=Xr(),g=c(u);t(g,()=>ar,(i,j)=>{j(i,{children:(q,_r)=>{H();var E=P("Password");r(q,E)},$$slots:{default:!0}})});var I=s(g,2);sr(I,X({type:"password"},()=>V(B),()=>$().password,{get value(){return e().password},set value(i){Z(A,x(e).password=i,x(e))}})),r(D,u)}}})});var n=s(_,2);t(n,()=>or,(o,d)=>{d(o,{})}),r(f,v)},$$slots:{default:!0}})});var w=s(G,2);t(w,()=>rr,(a,m)=>{m(a,{get form(){return Y},name:"confirmPassword",children:(f,J)=>{var v=tt(),_=c(v);t(_,()=>tr,(o,d)=>{d(o,{children:T,$$slots:{default:(D,U)=>{const B=W(()=>U.attrs);var u=rt(),g=c(u);t(g,()=>ar,(i,j)=>{j(i,{children:(q,_r)=>{H();var E=P("Confirm Password");r(q,E)},$$slots:{default:!0}})});var I=s(g,2);sr(I,X({type:"password"},()=>V(B),()=>$().confirmPassword,{get value(){return e().confirmPassword},set value(i){Z(A,x(e).confirmPassword=i,x(e))}})),r(D,u)}}})});var n=s(_,2);t(n,()=>or,(o,d)=>{d(o,{})}),r(f,v)},$$slots:{default:!0}})});var b=s(w,2);{var z=a=>{var m=at(),f=er(m,!0);nr(m),zr(()=>Ur(f,M()._errors[0])),r(a,m)};$r(b,a=>{M()._errors&&a(z)})}var R=s(b,2);Or(R,{type:"submit",get disabled(){return N()},class:"w-full",children:(a,m)=>{var f=pr(),J=c(f);{var v=n=>{var o=P("...");r(n,o)},_=n=>{var o=P("Sign Up");r(n,o)};$r(J,n=>{N()?n(v):n(_,!1)})}r(a,f)},$$slots:{default:!0}}),nr(p),Br(p,a=>Q==null?void 0:Q(a)),r(S,p)},$$slots:{default:!0}})});var Sr=s(fr,2);t(Sr,()=>Rr,(y,F)=>{F(y,{children:(S,vr)=>{var p=st();r(S,p)},$$slots:{default:!0}})}),r(Fr,ir)},$$slots:{default:!0}})}),r(L,lr),Pr(),O()}export{Dt as component};
