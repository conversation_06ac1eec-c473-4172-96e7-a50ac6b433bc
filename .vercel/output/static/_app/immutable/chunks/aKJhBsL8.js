import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as n,e as p,a as i}from"./wnqW1tdD.js";import{s as m}from"./BDqVm3Gq.js";import{l as c,s as d}from"./Cmdkv-7M.js";import{I as $}from"./CX_t0Ed_.js";function y(s,o){const t=c(o,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"}]];$(s,d({name:"zap"},()=>t,{get iconNode(){return r},children:(e,f)=>{var a=n(),l=p(a);m(l,o,"default",{},null),i(e,a)},$$slots:{default:!0}}))}export{y as Z};
