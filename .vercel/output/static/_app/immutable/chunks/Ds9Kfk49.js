import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as a,e as l,a as i}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as $,s as p}from"./Cmdkv-7M.js";import{I as u}from"./CX_t0Ed_.js";function N(r,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),o=[["circle",{cx:"11",cy:"11",r:"8"}],["path",{d:"m21 21-4.3-4.3"}]];u(r,p({name:"search"},()=>n,{get iconNode(){return o},children:(s,m)=>{var t=a(),c=l(t);d(c,e,"default",{},null),i(s,t)},$$slots:{default:!0}}))}function x(r,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),o=[["circle",{cx:"12",cy:"12",r:"10"}],["circle",{cx:"12",cy:"12",r:"6"}],["circle",{cx:"12",cy:"12",r:"2"}]];u(r,p({name:"target"},()=>n,{get iconNode(){return o},children:(s,m)=>{var t=a(),c=l(t);d(c,e,"default",{},null),i(s,t)},$$slots:{default:!0}}))}function z(r,e){const n=$(e,["children","$$slots","$$events","$$legacy"]),o=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17"}],["polyline",{points:"16 7 22 7 22 13"}]];u(r,p({name:"trending-up"},()=>n,{get iconNode(){return o},children:(s,m)=>{var t=a(),c=l(t);d(c,e,"default",{},null),i(s,t)},$$slots:{default:!0}}))}export{N as S,z as T,x as a};
