import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as i,e as l,a as c}from"./wnqW1tdD.js";import{s as d}from"./BDqVm3Gq.js";import{l as p,s as $}from"./Cmdkv-7M.js";import{I as m}from"./CX_t0Ed_.js";function N(e,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"}]];m(e,$({name:"copy"},()=>r,{get iconNode(){return s},children:(n,f)=>{var o=i(),a=l(o);d(a,t,"default",{},null),c(n,o)},$$slots:{default:!0}}))}function x(e,t){const r=p(t,["children","$$slots","$$events","$$legacy"]),s=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}]];m(e,$({name:"filter"},()=>r,{get iconNode(){return s},children:(n,f)=>{var o=i(),a=l(o);d(a,t,"default",{},null),c(n,o)},$$slots:{default:!0}}))}export{N as C,x as F};
