import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as d,e as p,a as l}from"./wnqW1tdD.js";import{s as i}from"./BDqVm3Gq.js";import{l as c,s as h}from"./Cmdkv-7M.js";import{I as $}from"./CX_t0Ed_.js";function A(o,a){const e=c(a,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18"}]];$(o,h({name:"brain"},()=>e,{get iconNode(){return r},children:(n,m)=>{var t=d(),s=p(t);i(s,a,"default",{},null),l(n,t)},$$slots:{default:!0}}))}function N(o,a){const e=c(a,["children","$$slots","$$events","$$legacy"]),r=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M18 17V9"}],["path",{d:"M13 17V5"}],["path",{d:"M8 17v-3"}]];$(o,h({name:"chart-column"},()=>e,{get iconNode(){return r},children:(n,m)=>{var t=d(),s=p(t);i(s,a,"default",{},null),l(n,t)},$$slots:{default:!0}}))}export{A as B,N as C};
