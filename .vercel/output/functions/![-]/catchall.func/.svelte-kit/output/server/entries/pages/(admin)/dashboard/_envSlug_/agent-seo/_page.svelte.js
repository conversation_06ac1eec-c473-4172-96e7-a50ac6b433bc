import { K as sanitize_props, M as spread_props, N as slot, F as store_get, I as head, X as attr_class, Y as attr, Z as stringify, V as ensure_array_like, J as escape_html, W as attr_style, G as unsubscribe_stores, E as pop, A as push } from "../../../../../../chunks/index.js";
import { w as writable } from "../../../../../../chunks/index3.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { T as Target, a as Trending_up, S as Search } from "../../../../../../chunks/trending-up.js";
import { S as Sparkles } from "../../../../../../chunks/sparkles.js";
import { C as Chevron_right, B as Bot } from "../../../../../../chunks/chevron-right.js";
import { U as User, C as Clock, D as Download } from "../../../../../../chunks/user.js";
import { C as Copy, F as Filter } from "../../../../../../chunks/filter.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { h as html } from "../../../../../../chunks/html.js";
function Book_open($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"
      }
    ],
    [
      "path",
      {
        "d": "M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "book-open" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function File_down($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
      }
    ],
    ["path", { "d": "M14 2v4a2 2 0 0 0 2 2h4" }],
    ["path", { "d": "M12 18v-6" }],
    ["path", { "d": "m9 15 3 3 3-3" }]
  ];
  Icon($$payload, spread_props([
    { name: "file-down" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const messages = writable([]);
  let input = "";
  let isLoading = false;
  let currentPlaceholder = "";
  const interactiveCards = [
    {
      icon: Search,
      title: "Niche Keyword Discovery",
      description: "Find untapped long-tail keywords in your specific niche",
      prompt: "Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"
    },
    {
      icon: Trending_up,
      title: "Competitor Gap Analysis",
      description: "Identify keyword opportunities your competitors are missing",
      prompt: "Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space"
    }
  ];
  function formatContent(content) {
    let formatted = content.replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>').replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>').replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>').replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-foreground">$1</strong>').replace(/^[\-\*•] (.*$)/gim, '<li class="ml-6 mb-1 text-muted-foreground list-disc">$1</li>').replace(/^\d+\. (.*$)/gim, '<li class="ml-6 mb-1 text-muted-foreground list-decimal">$1</li>').replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-3 rounded my-3 overflow-x-auto"><code class="text-sm">$1</code></pre>').replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>').replace(/\|(.+)\|/g, (match) => {
      const cells = match.split("|").filter((cell) => cell.trim());
      const isHeader = cells.some((cell) => cell.trim().match(/^[\-:]+$/));
      if (isHeader) return "";
      return `<tr>${cells.map((cell) => `<td class="border border-border px-3 py-1">${cell.trim()}</td>`).join("")}</tr>`;
    });
    formatted = formatted.replace(/(<li class="[^"]*list-disc[^"]*">[\s\S]*?<\/li>\s*)+/g, '<ul class="mb-4">$&</ul>');
    formatted = formatted.replace(/(<li class="[^"]*list-decimal[^"]*">[\s\S]*?<\/li>\s*)+/g, '<ol class="mb-4">$&</ol>');
    formatted = formatted.replace(/(<tr>[\s\S]*?<\/tr>\s*)+/g, '<table class="w-full mb-4 border-collapse">$&</table>');
    const lines = formatted.split("\n");
    formatted = lines.map((line) => {
      if (line.trim() && !line.startsWith("<")) {
        return `<p class="text-muted-foreground leading-relaxed mb-3">${line}</p>`;
      }
      return line;
    }).join("\n");
    return formatted;
  }
  if (store_get($$store_subs ??= {}, "$messages", messages).length > 0) {
    setTimeout(
      () => {
        const messagesContainer = document.querySelector(".messages-container");
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      },
      100
    );
  }
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>SEO Strategist - AI Agent</title>`;
  });
  $$payload.out += `<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Target($$payload, {
    class: "w-6 h-6 animate-pulse",
    style: "color: var(--primary-foreground);"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-3xl font-black" style="color: var(--foreground);">Lexi - SEO Strategist</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Powered by Lexi – your AI SEO Sidekick</p></div></div> <div class="flex items-center space-x-4"><div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button${attr_class(`px-4 py-2 text-sm font-medium transition-colors ${stringify("bg-primary text-primary-foreground")}`)}>Chat Mode</button> <button${attr_class(`px-4 py-2 text-sm font-medium transition-colors border-l ${stringify("text-muted-foreground hover:text-foreground")}`)} style="border-color: var(--border);">`;
  Sparkles($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Niche Discovery</button> <button${attr_class(`px-4 py-2 text-sm font-medium transition-colors border-l ${stringify("text-muted-foreground hover:text-foreground")}`)} style="border-color: var(--border);">`;
  Trending_up($$payload, { class: "w-4 h-4 inline mr-1" });
  $$payload.out += `<!----> Gap Analysis</button></div> <div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Trending_up($$payload, {
    class: "w-4 h-4",
    style: "color: var(--accent-foreground);"
  });
  $$payload.out += `<!----> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Keyword Intelligence</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors">Dashboard</a> `;
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span class="text-foreground font-medium">SEO Agent</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full">`;
  {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$messages", messages));
    $$payload.out += `<div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container"><div class="space-y-6">`;
    if (store_get($$store_subs ??= {}, "$messages", messages).length === 0) {
      $$payload.out += "<!--[-->";
      const each_array = ensure_array_like(interactiveCards);
      $$payload.out += `<div class="text-center py-12"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2" style="background: var(--muted); border-color: var(--border);">`;
      Target($$payload, {
        class: "w-8 h-8",
        style: "color: var(--muted-foreground);"
      });
      $$payload.out += `<!----></div> <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">Start Your SEO Research</h3> <p class="font-medium mb-6" style="color: var(--muted-foreground);">Get keyword analysis and SEO strategy for any business</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let card = each_array[$$index];
        $$payload.out += `<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group" style="background: var(--card); border-color: var(--border);"${attr("disabled", isLoading, true)}><div class="flex items-center gap-3 mb-3"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform" style="background: var(--primary); border-color: var(--border);"><!---->`;
        card.icon?.($$payload, {
          class: "w-4 h-4",
          style: "color: var(--primary-foreground);"
        });
        $$payload.out += `<!----></div> <h4 class="font-bold text-sm" style="color: var(--foreground);">${escape_html(card.title)}</h4></div> <p class="text-xs mb-3" style="color: var(--muted-foreground);">${escape_html(card.description)}</p> <div class="text-xs font-mono p-2 border-2 rounded" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);">${escape_html(card.prompt)}</div></button>`;
      }
      $$payload.out += `<!--]--></div></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let message = each_array_1[$$index_1];
      $$payload.out += `<div${attr_class(`flex gap-4 ${stringify(message.role === "user" ? "flex-row-reverse" : "")}`)}><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"${attr_style(`background: var(--${stringify(message.role === "user" ? "primary" : "secondary")}); border-color: var(--border); box-shadow: var(--shadow-sm);`)}>`;
      if (message.role === "user") {
        $$payload.out += "<!--[-->";
        User($$payload, {
          class: "w-5 h-5",
          style: "color: var(--primary-foreground);"
        });
      } else {
        $$payload.out += "<!--[!-->";
        Bot($$payload, {
          class: "w-5 h-5",
          style: "color: var(--secondary-foreground);"
        });
      }
      $$payload.out += `<!--]--></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">${escape_html(message.role === "user" ? "You" : "SEO Strategist")}</span> <div class="flex items-center gap-1">`;
      Clock($$payload, {
        class: "w-3 h-3",
        style: "color: var(--muted-foreground);"
      });
      $$payload.out += `<!----> <span class="text-xs" style="color: var(--muted-foreground);">${escape_html(message.timestamp.toLocaleTimeString())}</span></div> `;
      if (message.role === "assistant" && message.isReport) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown">`;
        Download($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> Download</button>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> `;
      if (message.role === "user") {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);">${escape_html(message.content)}</p></div>`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `<div class="p-6 border-2 mb-4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none">${html(formatContent(message.content))}</div></div> <div class="flex flex-wrap gap-2 mb-4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1">`;
        Copy($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1">`;
        File_down($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> Export CSV</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1">`;
        Book_open($$payload, { class: "w-3 h-3" });
        $$payload.out += `<!----> Generate Blog Outline</button></div>`;
      }
      $$payload.out += `<!--]--></div></div>`;
    }
    $$payload.out += `<!--]--> `;
    {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div> <div${attr_class(`input-wrapper input-wrapper-seo p-6 ${stringify("")}`)}><div class="flex items-center justify-between" style="margin-bottom: 15px;"><h2 class="text-lg font-bold" style="color: var(--foreground);">SEO Analysis</h2> <div class="flex items-center space-x-2"><span class="text-sm font-medium" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button${attr_class(`px-3 py-1 text-sm font-medium transition-colors ${stringify("bg-primary text-primary-foreground")}`)}${attr("disabled", isLoading, true)}>Summary</button> <button${attr_class(`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${stringify("text-muted-foreground hover:text-foreground")}`)} style="border-color: var(--border);"${attr("disabled", isLoading, true)}>Table</button> <button${attr_class(`px-3 py-1 text-sm font-medium transition-colors ${stringify("text-muted-foreground hover:text-foreground")}`)}${attr("disabled", isLoading, true)}>Blog-ready</button></div></div></div> <div style="margin-bottom: 15px;"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2" style="margin-bottom: 10px;"${attr("disabled", isLoading, true)}>`;
    Filter($$payload, { class: "w-4 h-4" });
    $$payload.out += `<!----> Prompt Enhancer <span${attr_class(`text-xs ${stringify("")} transition-transform`)}>▼</span></button> `;
    {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> <div class="flex" style="gap: 15px;"><div class="flex-1 relative"><textarea${attr("placeholder", currentPlaceholder)} class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full" style="min-height: 60px;"${attr("disabled", isLoading, true)}>`;
    const $$body = escape_html(input);
    if ($$body) {
      $$payload.out += `${$$body}`;
    }
    $$payload.out += `</textarea></div> <button${attr("disabled", !input.trim() || isLoading, true)} class="btn-primary px-6 font-bold flex items-center gap-2" style="height: auto; align-self: stretch;">`;
    {
      $$payload.out += "<!--[!-->";
      Target($$payload, { class: "w-4 h-4 animate-pulse" });
    }
    $$payload.out += `<!--]--> Analyze</button></div></div></div></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
